{"meta_learning_fitness": 0.9649999999999999, "best_pipeline_config": {"approach": "random", "operations": ["(np.str_('remove_punctuation'), {'apply': np.False_})"]}, "data_sizes": {"original": {"train": 2000, "val": 5314, "test": 5314}, "processed": {"train": 2000, "val": 5314, "test": 5314}}, "autogluon_performance": "{'accuracy': 0.21735039518253668, 'balanced_accuracy': np.float64(0.2152431925024087), 'mcc': np.float64(0.02222662698639043)}", "timing": {"processing_time": 0.0020706653594970703, "autogluon_time": 676.781361579895, "total_time": 814.889479637146}}