{"meta_learning_fitness": 0.5761836441893831, "best_pipeline_config": {"approach": "staged", "operations": ["('clean', {'lowercase': True, 'remove_extra_whitespace': True, 'remove_punctuation': np.False_})", null, null, "('augment', {'technique': np.str_('mixed'), 'augmentation_ratio': np.float64(0.15), 'minority_boost': np.float64(2.5)})"]}, "data_sizes": {"original": {"train": 2000, "val": 1000, "test": 1000}, "processed": {"train": 2322, "val": 1000, "test": 1000}}, "autogluon_performance": "{'accuracy': 0.41, 'balanced_accuracy': np.float64(0.4102006197814581), 'mcc': np.float64(0.12034585408267542)}", "timing": {"processing_time": 0.23824357986450195, "autogluon_time": 1936.7713685035706, "total_time": 1971.923086643219}}