import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.pipeline import Pipeline
from sklearn.metrics import accuracy_score
from sklearn.linear_model import LogisticRegression
import os
import joblib
import time
import torch
from sentence_transformers import SentenceTransformer
from tpot import TPOTClassifier
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import FunctionTransformer
from sklearn.ensemble import IsolationForest
from imblearn.over_sampling import SMOTE
from imblearn.pipeline import Pipeline as ImblearnPipeline

use_gpu = torch.cuda.is_available()
device = 'cuda' if use_gpu else 'cpu'
if use_gpu:
    print("--- GPU detected. Using CUDA for acceleration. ---")
else:
    print("--- No GPU detected. Running on CPU. ---")

# --- Optimized Global Model Loading ---
# Load the model once to be reused by the encoding function.
print("Loading SentenceTransformer model globally...")
SBERT_MODEL = SentenceTransformer('all-MiniLM-L6-v2', device=device)
print("Model loaded.")

def encode_texts(texts):
    print(f"Encoding {len(texts)} texts with sentence-transformers model...")
    # Input can be a pandas Series or a list of strings
    if isinstance(texts, pd.Series):
        texts = texts.tolist()
    return SBERT_MODEL.encode(texts, show_progress_bar=True)

class MetaFeatureExtractor:
    def __init__(self):
        pass

    def characterize(self, train_df, text_column, label_column, sample_embeddings, sample_indices=None):
        """Creates a numerical fingerprint of the dataset using pre-computed embeddings."""
        print("Phase 1: Characterizing dataset with enhanced detection...")
        
        # 1. Statistical Analysis
        num_rows, num_cols = train_df.shape
        class_imbalance = train_df[label_column].value_counts(normalize=True).min()
        cardinality = train_df[text_column].nunique() / num_rows

        # 2. Universal Embedding Analysis (on a sample)
        embedding_variance = np.var(sample_embeddings)
        
        # 3. Detect outliers using Isolation Forest on embeddings
        iso_forest = IsolationForest(contamination=0.05, random_state=42)
        outlier_scores = iso_forest.fit(sample_embeddings).score_samples(sample_embeddings)
        outlier_fraction = np.mean(outlier_scores < -0.5)  # Estimated fraction of outliers
        
        # 4. Estimate noise level by checking class separability in embedding space
        # Calculate centroid for each class
        class_centroids = {}
        class_embedding_vars = {}
        
        # If sample_indices is not provided, create a mapping based on the sample embeddings size
        if sample_indices is None:
            sample_indices = np.arange(len(sample_embeddings))
            sample_df = train_df.iloc[:len(sample_embeddings)]
        else:
            sample_df = train_df.iloc[sample_indices]
            
        for label in train_df[label_column].unique():
            class_indices = np.where(sample_df[label_column] == label)[0]
            if len(class_indices) > 0:
                class_embeddings = sample_embeddings[class_indices]
                class_centroids[label] = np.mean(class_embeddings, axis=0)
                class_embedding_vars[label] = np.var(class_embeddings)
        
        # Calculate average distance between centroids
        centroid_distances = []
        centroids = list(class_centroids.values())
        for i in range(len(centroids)):
            for j in range(i+1, len(centroids)):
                centroid_distances.append(np.linalg.norm(centroids[i] - centroids[j]))
        
        if centroid_distances:
            avg_centroid_distance = np.mean(centroid_distances)
            avg_within_class_variance = np.mean(list(class_embedding_vars.values()))
            noise_estimate = avg_within_class_variance / (avg_centroid_distance + 1e-10)
        else:
            noise_estimate = 0.5  # Default if we can't calculate

        meta_features = {
            'num_rows': num_rows,
            'num_cols': num_cols,
            'class_imbalance': class_imbalance,
            'text_cardinality': cardinality,
            'embedding_variance': embedding_variance,
            'outlier_fraction': outlier_fraction,
            'noise_estimate': noise_estimate
        }
        
        print("Enhanced meta-features extracted:")
        for k, v in meta_features.items():
            print(f"- {k}: {v:.4f}")
        
        return meta_features

class MetaStrategyPredictor:
    def __init__(self, model_path=None):
        """
        Initializes the MetaStrategyPredictor with an optional pre-trained model.
        If no model is provided, it defaults to a simple heuristic-based approach.
        """
        self.model_path = model_path
        self.model = None
        if model_path and os.path.exists(model_path):
            print(f"Loading pre-trained model from {model_path}...")
            self.model = joblib.load(model_path)
        else:
            print("No pre-trained model provided. Using heuristic-based predictions.")

    def predict_policy(self, meta_features):
        """Predicts the best preprocessing strategy using a trained model or heuristic."""
        print("Phase 2: Predicting preprocessing policy with challenge adaptations...")
        
        if self.model:
            # Use the pre-trained model to predict the policy
            meta_features_array = np.array([meta_features[key] for key in sorted(meta_features.keys())]).reshape(1, -1)
            predicted_policy = self.model.predict(meta_features_array)[0]
            print(f"Predicted policy using trained model: {predicted_policy}")
            return predicted_policy
        else:
            # Enhanced heuristic-based policy prediction
            policy = {'priority': [], 'budget': [], 'class_weight': None, 'use_smote': False, 'use_robust': False}
            
            # 1. Handle Class Imbalance
            if meta_features['class_imbalance'] < 0.2:
                # For highly imbalanced data
                policy['class_weight'] = 'balanced'
                if meta_features['num_rows'] > 1000:
                    policy['use_smote'] = True
                    print("Detected significant class imbalance: Enabling SMOTE and balanced weights.")
                else:
                    print("Detected class imbalance: Using balanced class weights (dataset too small for SMOTE).")
            
            # 2. Handle Outliers
            if meta_features['outlier_fraction'] > 0.05:
                policy['use_robust'] = True
                print("Detected outliers: Using robust scaling.")
            
            # 3. Handle Noise
            if meta_features['noise_estimate'] > 0.7:
                # High noise - prioritize feature selection to reduce noise
                policy['priority'] = ['selection', 'transformation']
                policy['budget'] = [0.7, 0.3]
                print("High noise detected: Prioritizing feature selection.")
            elif meta_features['outlier_fraction'] > 0.1:
                # Many outliers - focus on robust transformations
                policy['priority'] = ['transformation', 'selection']
                policy['budget'] = [0.6, 0.4]
                print("Many outliers detected: Prioritizing robust transformations.")
            elif meta_features['text_cardinality'] < 0.8:
                # Low text cardinality (many duplicates/similar texts)
                policy['priority'] = ['transformation', 'selection']
                policy['budget'] = [0.7, 0.3]
                print("Low text cardinality detected: Focusing on transformation for repetitive data.")
            else:
                # Balanced default approach
                policy['priority'] = ['selection', 'transformation']
                policy['budget'] = [0.6, 0.4]
                print("No specific challenges detected: Using balanced approach.")
            
            return policy

class GuidedEvolutionaryOptimizer:
    def __init__(self, policy, label_column):
        self.policy = policy
        self.label_column = label_column

    def find_best_pipeline(self, X_train_embedded, y_train, cache_path):
        """Finds the best preprocessing pipeline using a guided search on pre-computed embeddings."""
        
        # Check if a cached pipeline exists
        if os.path.exists(cache_path):
            print(f"Loading cached TPOT pipeline from {cache_path}")
            return joblib.load(cache_path)

        print("Phase 3: Starting guided evolutionary pipeline optimization on embeddings...")

        # --- Policy-Driven TPOT Configuration for DENSE data ---
        # Define operator categories based on policy priorities
        operator_categories = {
            'selection': {
                'sklearn.feature_selection.SelectFwe': {
                    'alpha': [0.001, 0.005, 0.01, 0.05, 0.1],
                    'score_func': {'sklearn.feature_selection.f_classif'}
                },
                'sklearn.feature_selection.SelectPercentile': {
                    'percentile': range(1, 100),
                    'score_func': {'sklearn.feature_selection.f_classif'}
                },
                'sklearn.feature_selection.SelectFromModel': {
                    'threshold': [0.01, 0.05, 0.1, 0.2],
                    'estimator': {
                        'sklearn.ensemble.ExtraTreesClassifier': {
                            'n_estimators': [100],
                            'criterion': ["gini", "entropy"],
                            'max_features': np.arange(0.05, 1.01, 0.1)
                        }
                    }
                }
            },
            'transformation': {
                'sklearn.preprocessing.StandardScaler': {},
                'sklearn.preprocessing.RobustScaler': {} if self.policy.get('use_robust') else {},
                'sklearn.preprocessing.PowerTransformer': {
                    'method': ['yeo-johnson']
                },
                'sklearn.decomposition.PCA': {
                    'n_components': np.arange(0.25, 1.0, 0.25),
                    'svd_solver': ['auto']
                },
                'sklearn.decomposition.FastICA': {
                    'tol': [0.0, 0.001, 0.01]
                }
            }
        }

        # Build a custom config_dict based on the policy's priorities
        tpot_config = {}
        print(f"Building TPOT config based on policy priorities: {self.policy['priority']}")
        for category in self.policy['priority']:
            if category in operator_categories:
                tpot_config.update(operator_categories[category])
        
        # Always include classifiers
        tpot_config.update({
            'sklearn.linear_model.LogisticRegression': {
                'penalty': ["l1", "l2"],
                'C': [0.001, 0.01, 0.1, 1.0, 10.0, 100.0],
                'dual': [False],
                'class_weight': [self.policy.get('class_weight')], # Apply policy
                'max_iter': [1000] # Increase iterations to help convergence
            },
            'sklearn.ensemble.RandomForestClassifier': {
                'n_estimators': [100],
                'criterion': ["gini", "entropy"],
                'max_features': np.arange(0.05, 1.01, 0.05),
                'class_weight': [self.policy.get('class_weight')] # Apply policy
            }
        })
        # --- End of Policy-Driven Configuration ---

        # Step 2: Configure TPOT with dynamic parameters based on policy
        population_size = max(10, int(100 * self.policy['budget'][0]))  # Ensure a minimum population size
        generations = max(5, int(20 * self.policy['budget'][1]))  # Ensure a minimum number of generations
        crossover_rate = 0.8  # Default crossover rate
        mutation_rate = 0.2  # Default mutation rate

        print(f"Configuring TPOT with population_size={population_size}, generations={generations}, "
              f"crossover_rate={crossover_rate}, mutation_rate={mutation_rate}...")

        tpot = TPOTClassifier(
            generations=generations,
            population_size=population_size,
            crossover_rate=crossover_rate,
            mutation_rate=mutation_rate,
            verbosity=2,
            random_state=42,
            scoring='accuracy',
            cv=3,  # Use cross-validation for better generalization
            config_dict=tpot_config,  # Use the policy-driven configuration
            n_jobs=-1  # Use all available CPU cores for parallel processing
        )

        # Step 3: Fit TPOT on the embedded training data
        print("Evaluating candidate pipelines with TPOT on embedded data...")
        try:
            tpot.fit(X_train_embedded, y_train)
        except Exception as e:
            print(f"TPOT optimization failed: {e}")
            return None

        print("TPOT search complete.")

        # The best pipeline is the one fitted by TPOT. It operates on embeddings.
        best_pipeline = tpot.fitted_pipeline_

        print("Best pipeline structure identified.")
        # Cache the successful pipeline
        joblib.dump(best_pipeline, cache_path)
        print(f"Saved best pipeline to cache: {cache_path}")
        return best_pipeline

def run_pipeline_for_dataset(dataset_name, get_data_function, text_column, label_column, output_base_dir):
    """
    Executes the full meta-learning pipeline for a single dataset.
    """
    print(f"--- Running pipeline for dataset: {dataset_name} ---")
    total_start_time = time.time()
    
    # Define cache paths
    dataset_cache_dir = os.path.join(output_base_dir, dataset_name, 'cache')
    os.makedirs(dataset_cache_dir, exist_ok=True)
    embeddings_cache_path = os.path.join(dataset_cache_dir, 'embeddings.joblib')
    tpot_pipeline_cache_path = os.path.join(dataset_cache_dir, 'tpot_pipeline.joblib')

    # Load data
    print("Loading data...")
    train_df, val_df, test_df = get_data_function()
    
    # --- Phase 0: Cached Universal Embedding ---
    if os.path.exists(embeddings_cache_path):
        print(f"Loading cached embeddings from {embeddings_cache_path}...")
        X_train_embedded, X_val_embedded, X_test_embedded = joblib.load(embeddings_cache_path)
    else:
        print("Phase 0: Creating universal embeddings for all data splits...")
        X_train_embedded = encode_texts(train_df[text_column])
        X_val_embedded = encode_texts(val_df[text_column])
        X_test_embedded = encode_texts(test_df[text_column])
        joblib.dump((X_train_embedded, X_val_embedded, X_test_embedded), embeddings_cache_path)
        print(f"Embeddings created and saved to cache. Shape: {X_train_embedded.shape}")

    # --- Phase 1 & 2: Meta-Analysis ---
    phase1_start_time = time.time()
    meta_extractor = MetaFeatureExtractor()
    # Pass a sample of the embeddings to the extractor
    sample_size = min(1000, X_train_embedded.shape[0])
    sample_indices = np.random.choice(X_train_embedded.shape[0], sample_size, replace=False)
    sample_embeddings = X_train_embedded[sample_indices]
    
    # Pass the sample_indices parameter to the characterize method
    meta_features = meta_extractor.characterize(train_df, text_column, label_column, sample_embeddings, sample_indices)
    
    meta_predictor = MetaStrategyPredictor()
    policy = meta_predictor.predict_policy(meta_features)
    print(f"Time for Meta-Analysis (Phase 1 & 2): {time.time() - phase1_start_time:.2f}s")
    
    # --- Phase 3: Guided Optimization ---
    phase3_start_time = time.time()
    optimizer = GuidedEvolutionaryOptimizer(policy, label_column)
    best_pipeline_structure = optimizer.find_best_pipeline(X_train_embedded, train_df[label_column], tpot_pipeline_cache_path)
    print(f"Time for Guided Optimization (Phase 3): {time.time() - phase3_start_time:.2f}s")
    
    # --- Phase 4: Consistent Data Transformation ---
    phase4_start_time = time.time()
    print("Phase 4: Fitting final preprocessor on full training embeddings...")
    
    # The 'best_pipeline_structure' is the full pipeline from TPOT (transformers + classifier).
    # We need to create a new pipeline containing only the transformer steps.
    if isinstance(best_pipeline_structure, Pipeline):
        # All steps except the last one (the classifier)
        transformer_steps = best_pipeline_structure.steps[:-1]
        if transformer_steps:
            final_preprocessor = Pipeline(transformer_steps)
        else:
            # Pipeline with only a classifier, so no transformations to apply
            final_preprocessor = FunctionTransformer(lambda x: x, validate=False)
    else:
        # The best "pipeline" is just a single classifier, so no transformations.
        final_preprocessor = FunctionTransformer(lambda x: x, validate=False)

    # Check if SMOTE should be added (only for significant imbalance and sufficient data)
    if (policy.get('use_smote', False) and 
        meta_features['class_imbalance'] < 0.2 and 
        meta_features['num_rows'] >= 1000):
        print("Adding SMOTE for handling class imbalance...")
        # We need to create a special imblearn pipeline that includes SMOTE
        # But first fit the original preprocessor
        final_preprocessor.fit(X_train_embedded, train_df[label_column])
        
        # Process the training data
        processed_train_data = final_preprocessor.transform(X_train_embedded)
        
        # Apply SMOTE only to training data
        smote = SMOTE(random_state=42)
        if processed_train_data.ndim == 1:
            processed_train_data = processed_train_data.reshape(-1, 1)
        
        processed_train_data, train_labels = smote.fit_resample(processed_train_data, train_df[label_column].values)
        
        # Process validation and test data (no SMOTE)
        processed_val_data = final_preprocessor.transform(X_val_embedded)
        processed_test_data = final_preprocessor.transform(X_test_embedded)
        
        print(f"SMOTE applied: Training data shape changed from {len(train_df)} to {processed_train_data.shape[0]}")
    else:
        # Standard processing without SMOTE
        # Fit the transformer-only pipeline on the full training embeddings.
        final_preprocessor.fit(X_train_embedded, train_df[label_column])
        
        print("Applying consistent transformation to all data splits...")
        # The pipeline now operates on embeddings.
        processed_train_data = final_preprocessor.transform(X_train_embedded)
        processed_val_data = final_preprocessor.transform(X_val_embedded)
        processed_test_data = final_preprocessor.transform(X_test_embedded)
        train_labels = train_df[label_column].values

    # Create new dataframes with meaningful column names for better readability and compatibility.
    if processed_train_data.ndim == 1:
        processed_train_data = processed_train_data.reshape(-1, 1)
        processed_val_data = processed_val_data.reshape(-1, 1)
        processed_test_data = processed_test_data.reshape(-1, 1)
        
    num_features = processed_train_data.shape[1]
    feature_names = [f'feature_{i}' for i in range(num_features)]
    
    processed_train = pd.DataFrame(processed_train_data, columns=feature_names)
    processed_val = pd.DataFrame(processed_val_data, index=val_df.index, columns=feature_names)
    processed_test = pd.DataFrame(processed_test_data, index=test_df.index, columns=feature_names)

    # Add the labels back
    processed_train[label_column] = train_labels
    processed_val[label_column] = val_df[label_column].values
    processed_test[label_column] = test_df[label_column].values

    # Save the cleaned data
    dataset_output_dir = os.path.join(output_base_dir, dataset_name)
    os.makedirs(dataset_output_dir, exist_ok=True)
    
    train_path = os.path.join(dataset_output_dir, 'processed_train.csv')
    val_path = os.path.join(dataset_output_dir, 'processed_val.csv')
    test_path = os.path.join(dataset_output_dir, 'processed_test.csv')
    pipeline_path = os.path.join(dataset_output_dir, 'preprocessing_pipeline.joblib')

    processed_train.to_csv(train_path, index=False)
    processed_val.to_csv(val_path, index=False)
    processed_test.to_csv(test_path, index=False)
    print(f"Cleaned training, validation, and test data saved to: {dataset_output_dir}")

    # Save the fitted pipeline
    joblib.dump(final_preprocessor, pipeline_path)
    print(f"Fitted preprocessing pipeline saved to: {pipeline_path}")

    # --- Phase 5: Final Embedding and Model Training (Example) ---
    # This part is conceptual for now, showing how the processed data would be used.
    print("Phase 5: Final embedding and model training (Conceptual)...")
    # final_embedder = FinalEmbedder()
    # final_embedded_train = final_embedder.fit_transform(processed_train[text_column])
    # final_embedded_val = final_embedder.transform(processed_val[text_column])
    # final_embedded_test = final_embedder.transform(processed_test[text_column])
    # print("Data ready for AutoGluon.")
    # Pass to AutoGluon: TabularPredictor(label=label_column).fit(train_data=final_embedded_train, tuning_data=final_embedded_val)
    
    print(f"--- Pipeline finished for dataset: {dataset_name} ---")
    print(f"Total time for {dataset_name}: {time.time() - total_start_time:.2f}s\n")
    print("Data transformation complete.")
    print(f"Time for Data Transformation (Phase 4): {time.time() - phase4_start_time:.2f}s")

    print(f"--- Pipeline finished for dataset: {dataset_name} ---")
    print(f"Total time for {dataset_name}: {time.time() - total_start_time:.2f}s\n")
    return final_preprocessor


