2025-07-27 19:39:08,016 - __main__ - INFO - Creating challenging dataset: casehold_imbalanced (casehold with imbalance challenge)
2025-07-27 19:39:30,012 - __main__ - INFO - Successfully created challenging dataset: casehold_imbalanced
2025-07-27 19:39:30,013 - __main__ - INFO - - Train: 1467 samples
2025-07-27 19:39:30,014 - __main__ - INFO - - Val: 5314 samples
2025-07-27 19:39:30,014 - __main__ - INFO - - Test: 5314 samples
2025-07-27 19:39:30,014 - __main__ - INFO - Creating challenging dataset: anli_r1_outliers (anli_r1 with outliers challenge)
2025-07-27 19:39:42,645 - __main__ - INFO - Successfully created challenging dataset: anli_r1_outliers
2025-07-27 19:39:42,649 - __main__ - INFO - - Train: 2000 samples
2025-07-27 19:39:42,649 - __main__ - INFO - - Val: 1000 samples
2025-07-27 19:39:42,649 - __main__ - INFO - - Test: 1000 samples
2025-07-27 19:39:42,649 - __main__ - INFO - Creating challenging dataset: scienceqa_mixed (scienceqa with mixed challenge)
2025-07-27 19:39:57,295 - __main__ - INFO - Successfully created challenging dataset: scienceqa_mixed
2025-07-27 19:39:57,295 - __main__ - INFO - - Train: 1582 samples
2025-07-27 19:39:57,296 - __main__ - INFO - - Val: 1272 samples
2025-07-27 19:39:57,296 - __main__ - INFO - - Test: 1273 samples
2025-07-27 19:39:57,296 - __main__ - INFO - 
============================================================
2025-07-27 19:39:57,296 - __main__ - INFO - Processing challenging dataset: casehold_imbalanced
2025-07-27 19:39:57,300 - __main__ - INFO - Base dataset: casehold, Challenge: imbalance
2025-07-27 19:39:57,300 - __main__ - INFO - ============================================================
2025-07-27 19:39:57,300 - __main__ - INFO - Using TabularPredictor for text data for casehold_imbalanced
2025-07-27 19:39:57,302 - __main__ - INFO - Using AutoGluon's default models and settings
2025-07-27 19:49:59,504 - __main__ - INFO - Text model training completed for casehold_imbalanced in 602.20 seconds
2025-07-27 19:49:59,504 - autogluon_multi_dataset_training - INFO - Evaluating model for casehold_imbalanced...
2025-07-27 19:49:59,504 - autogluon_multi_dataset_training - INFO - Getting model information for casehold_imbalanced...
2025-07-27 19:49:59,505 - autogluon_multi_dataset_training - INFO - Model info for casehold_imbalanced: {'model_size_mb': 243.97, 'num_parameters': 'N/A', 'model_type': 'TabularPredictor'}
2025-07-27 19:49:59,505 - autogluon_multi_dataset_training - INFO - Saving predictions and creating confusion matrix for casehold_imbalanced...
2025-07-27 19:50:35,530 - autogluon_multi_dataset_training - INFO - Predictions saved to: ./autogluon_challenging_results/predictions/casehold_imbalanced_predictions.csv
2025-07-27 19:50:36,609 - autogluon_multi_dataset_training - INFO - Confusion matrix saved to: ./autogluon_challenging_results/confusion_matrices/casehold_imbalanced_confusion_matrix.png
2025-07-27 19:50:36,759 - autogluon_multi_dataset_training - INFO - Classification report saved to: ./autogluon_challenging_results/confusion_matrices/casehold_imbalanced_classification_report.csv
2025-07-27 19:51:38,369 - autogluon_multi_dataset_training - INFO - Evaluation completed for casehold_imbalanced
2025-07-27 19:51:38,370 - autogluon_multi_dataset_training - INFO - Problem type: multiclass
2025-07-27 19:51:38,370 - autogluon_multi_dataset_training - INFO - Eval metric: accuracy
2025-07-27 19:51:38,370 - autogluon_multi_dataset_training - INFO - Best model: LightGBMXT
2025-07-27 19:51:38,370 - autogluon_multi_dataset_training - INFO - Best validation score: 0.20756492284531428
2025-07-27 19:51:38,371 - autogluon_multi_dataset_training - INFO - Test score: 0.20398946179902144
2025-07-27 19:51:38,371 - autogluon_multi_dataset_training - INFO - Accuracy: 0.188558524651863
2025-07-27 19:51:38,371 - autogluon_multi_dataset_training - INFO - Total models trained: 5
2025-07-27 19:51:38,371 - autogluon_multi_dataset_training - INFO - Model size: 243.97 MB
2025-07-27 19:51:38,371 - autogluon_multi_dataset_training - INFO - Model parameters: N/A
2025-07-27 19:51:38,372 - autogluon_multi_dataset_training - INFO - Top models:
2025-07-27 19:51:38,372 - autogluon_multi_dataset_training - INFO -   1. LightGBMXT: 0.20756492284531428
2025-07-27 19:51:38,372 - autogluon_multi_dataset_training - INFO -   2. KNeighborsUnif: 0.19476853594279261
2025-07-27 19:51:38,372 - autogluon_multi_dataset_training - INFO -   3. KNeighborsDist: 0.1943921716221302
2025-07-27 19:51:38,373 - __main__ - INFO - 
============================================================
2025-07-27 19:51:38,373 - __main__ - INFO - Processing challenging dataset: anli_r1_outliers
2025-07-27 19:51:38,373 - __main__ - INFO - Base dataset: anli_r1, Challenge: outliers
2025-07-27 19:51:38,373 - __main__ - INFO - ============================================================
2025-07-27 19:51:38,374 - __main__ - INFO - Using TabularPredictor for text data for anli_r1_outliers
2025-07-27 19:51:38,376 - __main__ - INFO - Using AutoGluon's default models and settings
2025-07-27 20:01:39,949 - __main__ - INFO - Text model training completed for anli_r1_outliers in 601.58 seconds
2025-07-27 20:01:39,968 - autogluon_multi_dataset_training - INFO - Evaluating model for anli_r1_outliers...
2025-07-27 20:01:39,968 - autogluon_multi_dataset_training - INFO - Getting model information for anli_r1_outliers...
2025-07-27 20:01:39,970 - autogluon_multi_dataset_training - INFO - Model info for anli_r1_outliers: {'model_size_mb': 24.04, 'num_parameters': 'N/A', 'model_type': 'TabularPredictor'}
2025-07-27 20:01:39,970 - autogluon_multi_dataset_training - INFO - Saving predictions and creating confusion matrix for anli_r1_outliers...
2025-07-27 20:01:40,914 - autogluon_multi_dataset_training - INFO - Predictions saved to: ./autogluon_challenging_results/predictions/anli_r1_outliers_predictions.csv
2025-07-27 20:01:41,707 - autogluon_multi_dataset_training - INFO - Confusion matrix saved to: ./autogluon_challenging_results/confusion_matrices/anli_r1_outliers_confusion_matrix.png
2025-07-27 20:01:41,726 - autogluon_multi_dataset_training - INFO - Classification report saved to: ./autogluon_challenging_results/confusion_matrices/anli_r1_outliers_classification_report.csv
2025-07-27 20:01:44,007 - autogluon_multi_dataset_training - INFO - Evaluation completed for anli_r1_outliers
2025-07-27 20:01:44,007 - autogluon_multi_dataset_training - INFO - Problem type: multiclass
2025-07-27 20:01:44,008 - autogluon_multi_dataset_training - INFO - Eval metric: accuracy
2025-07-27 20:01:44,008 - autogluon_multi_dataset_training - INFO - Best model: LightGBMXT
2025-07-27 20:01:44,009 - autogluon_multi_dataset_training - INFO - Best validation score: 0.355
2025-07-27 20:01:44,009 - autogluon_multi_dataset_training - INFO - Test score: 0.336
2025-07-27 20:01:44,009 - autogluon_multi_dataset_training - INFO - Accuracy: 0.336
2025-07-27 20:01:44,010 - autogluon_multi_dataset_training - INFO - Total models trained: 6
2025-07-27 20:01:44,010 - autogluon_multi_dataset_training - INFO - Model size: 24.04 MB
2025-07-27 20:01:44,010 - autogluon_multi_dataset_training - INFO - Model parameters: N/A
2025-07-27 20:01:44,011 - autogluon_multi_dataset_training - INFO - Top models:
2025-07-27 20:01:44,011 - autogluon_multi_dataset_training - INFO -   1. LightGBMXT: 0.355
2025-07-27 20:01:44,011 - autogluon_multi_dataset_training - INFO -   2. WeightedEnsemble_L2: 0.355
2025-07-27 20:01:44,012 - autogluon_multi_dataset_training - INFO -   3. KNeighborsUnif: 0.318
2025-07-27 20:01:44,012 - __main__ - INFO - 
============================================================
2025-07-27 20:01:44,012 - __main__ - INFO - Processing challenging dataset: scienceqa_mixed
2025-07-27 20:01:44,013 - __main__ - INFO - Base dataset: scienceqa, Challenge: mixed
2025-07-27 20:01:44,013 - __main__ - INFO - ============================================================
2025-07-27 20:01:44,013 - __main__ - INFO - Using TabularPredictor for text data for scienceqa_mixed
2025-07-27 20:01:44,015 - __main__ - INFO - Using AutoGluon's default models and settings
2025-07-27 20:11:46,817 - __main__ - INFO - Text model training completed for scienceqa_mixed in 602.80 seconds
2025-07-27 20:11:46,825 - autogluon_multi_dataset_training - INFO - Evaluating model for scienceqa_mixed...
2025-07-27 20:11:46,826 - autogluon_multi_dataset_training - INFO - Getting model information for scienceqa_mixed...
2025-07-27 20:11:46,827 - autogluon_multi_dataset_training - INFO - Model info for scienceqa_mixed: {'model_size_mb': 52.86, 'num_parameters': 'N/A', 'model_type': 'TabularPredictor'}
2025-07-27 20:11:46,830 - autogluon_multi_dataset_training - INFO - Saving predictions and creating confusion matrix for scienceqa_mixed...
2025-07-27 20:11:50,373 - autogluon_multi_dataset_training - INFO - Predictions saved to: ./autogluon_challenging_results/predictions/scienceqa_mixed_predictions.csv
2025-07-27 20:11:51,377 - autogluon_multi_dataset_training - INFO - Confusion matrix saved to: ./autogluon_challenging_results/confusion_matrices/scienceqa_mixed_confusion_matrix.png
2025-07-27 20:11:51,399 - autogluon_multi_dataset_training - INFO - Classification report saved to: ./autogluon_challenging_results/confusion_matrices/scienceqa_mixed_classification_report.csv
2025-07-27 20:12:00,478 - autogluon_multi_dataset_training - INFO - Evaluation completed for scienceqa_mixed
2025-07-27 20:12:00,479 - autogluon_multi_dataset_training - INFO - Problem type: multiclass
2025-07-27 20:12:00,480 - autogluon_multi_dataset_training - INFO - Eval metric: accuracy
2025-07-27 20:12:00,480 - autogluon_multi_dataset_training - INFO - Best model: KNeighborsDist
2025-07-27 20:12:00,480 - autogluon_multi_dataset_training - INFO - Best validation score: 0.404874213836478
2025-07-27 20:12:00,481 - autogluon_multi_dataset_training - INFO - Test score: 0.38884524744697563
2025-07-27 20:12:00,481 - autogluon_multi_dataset_training - INFO - Accuracy: 0.3857030636292223
2025-07-27 20:12:00,482 - autogluon_multi_dataset_training - INFO - Total models trained: 5
2025-07-27 20:12:00,482 - autogluon_multi_dataset_training - INFO - Model size: 52.86 MB
2025-07-27 20:12:00,483 - autogluon_multi_dataset_training - INFO - Model parameters: N/A
2025-07-27 20:12:00,483 - autogluon_multi_dataset_training - INFO - Top models:
2025-07-27 20:12:00,483 - autogluon_multi_dataset_training - INFO -   1. KNeighborsDist: 0.404874213836478
2025-07-27 20:12:00,484 - autogluon_multi_dataset_training - INFO -   2. WeightedEnsemble_L2: 0.42845911949685533
2025-07-27 20:12:00,485 - autogluon_multi_dataset_training - INFO -   3. KNeighborsUnif: 0.40644654088050314
2025-07-27 20:12:00,489 - autogluon_multi_dataset_training - INFO - Results saved to: ./autogluon_challenging_results/autogluon_multi_dataset_results_20250727_201200.csv
