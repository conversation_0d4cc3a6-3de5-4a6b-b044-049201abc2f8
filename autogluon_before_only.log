2025-07-29 02:41:25,894 - __main__ - INFO - Initialized AutoGluonBeforeTrainer
2025-07-29 02:41:25,894 - __main__ - INFO - Output directory: autogluon_before_results/run_20250729_024125
2025-07-29 02:41:25,894 - __main__ - INFO - Time limit per model: 3000 seconds
2025-07-29 02:41:25,894 - __main__ - INFO - Using preset: medium_quality
2025-07-29 02:41:25,894 - __main__ - INFO - 
============================================================
2025-07-29 02:41:25,894 - __main__ - INFO - Processing dataset: anli_r1
2025-07-29 02:41:25,894 - __main__ - INFO - ============================================================
2025-07-29 02:41:25,894 - __main__ - INFO - Loading train_subset dataset from /storage/nammt/autogluon/anli_r1_noisy
2025-07-29 02:41:25,996 - __main__ - INFO - Loaded anli_r1 train_subset dataset:
2025-07-29 02:41:25,996 - __main__ - INFO -   Training samples: 2000
2025-07-29 02:41:25,996 - __main__ - INFO -   Validation samples: 1000
2025-07-29 02:41:25,996 - __main__ - INFO -   Testing samples: 1000
2025-07-29 02:41:25,996 - __main__ - INFO -   Features: 5
2025-07-29 02:41:25,996 - __main__ - INFO -   Target column: label
2025-07-29 02:41:25,997 - __main__ - INFO - Training AutoGluon model for anli_r1...
2025-07-29 02:41:25,997 - __main__ - INFO - Using problem_type=multiclass, eval_metric=accuracy
2025-07-29 02:43:38,969 - __main__ - INFO - Model training completed for anli_r1 in 132.97 seconds
2025-07-29 02:43:38,969 - __main__ - INFO - Evaluating model for anli_r1...
2025-07-29 02:43:42,264 - __main__ - INFO - Predictions saved to: autogluon_before_results/run_20250729_024125/predictions/anli_r1_before_predictions.csv
2025-07-29 02:43:43,075 - __main__ - INFO - Confusion matrix saved to: autogluon_before_results/run_20250729_024125/confusion_matrices/anli_r1_before_confusion_matrix.png
2025-07-29 02:43:43,077 - __main__ - INFO - Accuracy: 0.3890
2025-07-29 02:43:46,152 - __main__ - INFO - Leaderboard:
2025-07-29 02:43:46,153 - __main__ - INFO -   1. LightGBMXT - Score: 0.42
2025-07-29 02:43:46,153 - __main__ - INFO -   2. LightGBMLarge - Score: 0.422
2025-07-29 02:43:46,154 - __main__ - INFO -   3. XGBoost - Score: 0.418
2025-07-29 02:43:46,155 - __main__ - INFO - Leaderboard saved to: autogluon_before_results/run_20250729_024125/anli_r1_before_leaderboard.csv
2025-07-29 02:43:47,725 - __main__ - ERROR - Error evaluating anli_r1: name 'train_df' is not defined
2025-07-29 02:43:47,725 - __main__ - INFO - 
============================================================
2025-07-29 02:43:47,725 - __main__ - INFO - Processing dataset: casehold
2025-07-29 02:43:47,725 - __main__ - INFO - ============================================================
2025-07-29 02:43:47,725 - __main__ - INFO - Loading train_subset dataset from /storage/nammt/autogluon/casehold_imbalanced
2025-07-29 02:43:48,709 - __main__ - INFO - Loaded casehold train_subset dataset:
2025-07-29 02:43:48,709 - __main__ - INFO -   Training samples: 2000
2025-07-29 02:43:48,709 - __main__ - INFO -   Validation samples: 5314
2025-07-29 02:43:48,709 - __main__ - INFO -   Testing samples: 5314
2025-07-29 02:43:48,709 - __main__ - INFO -   Features: 8
2025-07-29 02:43:48,709 - __main__ - INFO -   Target column: label
2025-07-29 02:43:48,709 - __main__ - INFO - Training AutoGluon model for casehold...
2025-07-29 02:43:48,710 - __main__ - INFO - Using problem_type=multiclass, eval_metric=accuracy
2025-07-29 02:54:30,987 - __main__ - INFO - Model training completed for casehold in 642.28 seconds
2025-07-29 02:54:30,987 - __main__ - INFO - Evaluating model for casehold...
2025-07-29 02:55:11,672 - __main__ - INFO - Predictions saved to: autogluon_before_results/run_20250729_024125/predictions/casehold_before_predictions.csv
2025-07-29 02:55:12,541 - __main__ - INFO - Confusion matrix saved to: autogluon_before_results/run_20250729_024125/confusion_matrices/casehold_before_confusion_matrix.png
2025-07-29 02:55:12,543 - __main__ - INFO - Accuracy: 0.2174
2025-07-29 02:55:52,271 - __main__ - INFO - Leaderboard:
2025-07-29 02:55:52,272 - __main__ - INFO -   1. WeightedEnsemble_L2 - Score: 0.2154685735792247
2025-07-29 02:55:52,272 - __main__ - INFO -   2. NeuralNetFastAI - Score: 0.21433948061723748
2025-07-29 02:55:52,272 - __main__ - INFO -   3. XGBoost - Score: 0.1900639819345126
2025-07-29 02:55:52,274 - __main__ - INFO - Leaderboard saved to: autogluon_before_results/run_20250729_024125/casehold_before_leaderboard.csv
2025-07-29 02:56:11,942 - __main__ - ERROR - Error evaluating casehold: name 'train_df' is not defined
2025-07-29 02:56:11,943 - __main__ - INFO - Results saved to: autogluon_before_results/run_20250729_024125/before_results_20250729_025611.json
