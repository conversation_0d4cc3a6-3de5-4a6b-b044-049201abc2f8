2025-07-28 22:50:40,279 - __main__ - INFO - Initialized AutoGluonComparisonTrainer
2025-07-28 22:50:40,279 - __main__ - INFO - Output directory: autogluon_comparison_results/run_20250728_225040
2025-07-28 22:50:40,280 - __main__ - INFO - Time limit per model: 3000 seconds
2025-07-28 22:50:40,280 - __main__ - INFO - Using preset: medium_quality
2025-07-28 22:50:40,280 - __main__ - INFO - 
============================================================
2025-07-28 22:50:40,280 - __main__ - INFO - Processing dataset: anli_r1
2025-07-28 22:50:40,280 - __main__ - INFO - ============================================================
2025-07-28 22:50:40,280 - __main__ - INFO - 
Processing before preprocessing variant...
2025-07-28 22:50:40,280 - __main__ - INFO - Loading train_subset dataset from /storage/nammt/autogluon/anli_r1_noisy
2025-07-28 22:50:40,280 - __main__ - ERROR - Dataset files not found at /storage/nammt/autogluon/anli_r1_noisy for variant train_subset
2025-07-28 22:50:40,280 - __main__ - WARNING - Skipping anli_r1 before preprocessing due to loading error
2025-07-28 22:50:40,280 - __main__ - INFO - 
Processing after preprocessing variant...
2025-07-28 22:50:40,280 - __main__ - INFO - Loading after dataset from /storage/nammt/autogluon/anli_r1_noisy
2025-07-28 22:50:40,986 - __main__ - INFO - Loaded anli_r1 after dataset:
2025-07-28 22:50:40,987 - __main__ - INFO -   Training samples: 2000
2025-07-28 22:50:40,987 - __main__ - INFO -   Validation samples: 1000
2025-07-28 22:50:40,987 - __main__ - INFO -   Testing samples: 1000
2025-07-28 22:50:40,987 - __main__ - INFO -   Features: 388
2025-07-28 22:50:40,987 - __main__ - INFO -   Target column: label
2025-07-28 22:50:40,987 - __main__ - INFO - Training AutoGluon model for anli_r1 (after)...
2025-07-28 22:50:40,989 - __main__ - INFO - Using TabularPredictor for anli_r1 (after)
2025-07-28 22:55:25,286 - __main__ - INFO - Tabular model training completed for anli_r1 (after) in 284.30 seconds
2025-07-28 22:55:25,287 - __main__ - INFO - Evaluating model for anli_r1 (after)...
2025-07-28 22:55:25,287 - __main__ - INFO - Getting model information for anli_r1 (after)...
2025-07-28 22:55:25,289 - __main__ - INFO - Model info for anli_r1 (after): {'model_size_mb': 144.28, 'num_parameters': 'N/A', 'model_type': 'TabularPredictor'}
2025-07-28 22:55:25,289 - __main__ - INFO - Saving predictions and creating confusion matrix for anli_r1 (after)...
2025-07-28 22:55:26,715 - __main__ - INFO - Predictions saved to: autogluon_comparison_results/run_20250728_225040/predictions/anli_r1_after_predictions.csv
2025-07-28 22:55:27,489 - __main__ - INFO - Confusion matrix saved to: autogluon_comparison_results/run_20250728_225040/confusion_matrices/anli_r1_after_confusion_matrix.png
2025-07-28 22:55:27,509 - __main__ - INFO - Classification report saved to: autogluon_comparison_results/run_20250728_225040/confusion_matrices/anli_r1_after_classification_report.csv
2025-07-28 22:55:29,385 - __main__ - INFO - Evaluation completed for anli_r1 (after)
2025-07-28 22:55:29,385 - __main__ - INFO - Problem type: multiclass
2025-07-28 22:55:29,385 - __main__ - INFO - Eval metric: accuracy
2025-07-28 22:55:29,385 - __main__ - INFO - Best model: KNeighborsDist
2025-07-28 22:55:29,385 - __main__ - INFO - Best validation score: 0.331
2025-07-28 22:55:29,386 - __main__ - INFO - Test score: 0.357
2025-07-28 22:55:29,386 - __main__ - INFO - Accuracy: 0.322
2025-07-28 22:55:29,386 - __main__ - INFO - Total models trained: 14
2025-07-28 22:55:29,386 - __main__ - INFO - Model size: 144.28 MB
2025-07-28 22:55:29,386 - __main__ - INFO - Model parameters: N/A
2025-07-28 22:55:29,386 - __main__ - INFO - Top models:
2025-07-28 22:55:29,386 - __main__ - INFO -   1. KNeighborsDist: 0.331
2025-07-28 22:55:29,386 - __main__ - INFO -   2. KNeighborsUnif: 0.335
2025-07-28 22:55:29,386 - __main__ - INFO -   3. RandomForestGini: 0.338
2025-07-28 22:55:29,387 - __main__ - INFO - 
============================================================
2025-07-28 22:55:29,387 - __main__ - INFO - Processing dataset: casehold
2025-07-28 22:55:29,387 - __main__ - INFO - ============================================================
2025-07-28 22:55:29,387 - __main__ - INFO - 
Processing before preprocessing variant...
2025-07-28 22:55:29,387 - __main__ - INFO - Loading train_subset dataset from /storage/nammt/autogluon/casehold_imbalanced
2025-07-28 22:55:29,387 - __main__ - ERROR - Dataset files not found at /storage/nammt/autogluon/casehold_imbalanced for variant train_subset
2025-07-28 22:55:29,387 - __main__ - WARNING - Skipping casehold before preprocessing due to loading error
2025-07-28 22:55:29,388 - __main__ - INFO - 
Processing after preprocessing variant...
2025-07-28 22:55:29,388 - __main__ - INFO - Loading after dataset from /storage/nammt/autogluon/casehold_imbalanced
2025-07-28 22:55:30,620 - __main__ - INFO - Loaded casehold after dataset:
2025-07-28 22:55:30,620 - __main__ - INFO -   Training samples: 9500
2025-07-28 22:55:30,620 - __main__ - INFO -   Validation samples: 5314
2025-07-28 22:55:30,620 - __main__ - INFO -   Testing samples: 5314
2025-07-28 22:55:30,620 - __main__ - INFO -   Features: 384
2025-07-28 22:55:30,620 - __main__ - INFO -   Target column: label
2025-07-28 22:55:30,621 - __main__ - INFO - Training AutoGluon model for casehold (after)...
2025-07-28 22:55:30,621 - __main__ - INFO - Using TabularPredictor for casehold (after)
2025-07-28 23:02:38,847 - __main__ - INFO - Tabular model training completed for casehold (after) in 428.23 seconds
2025-07-28 23:02:38,847 - __main__ - INFO - Evaluating model for casehold (after)...
2025-07-28 23:02:38,847 - __main__ - INFO - Getting model information for casehold (after)...
2025-07-28 23:02:38,849 - __main__ - INFO - Model info for casehold (after): {'model_size_mb': 255.7, 'num_parameters': 'N/A', 'model_type': 'TabularPredictor'}
2025-07-28 23:02:38,849 - __main__ - INFO - Saving predictions and creating confusion matrix for casehold (after)...
2025-07-28 23:02:40,082 - __main__ - INFO - Predictions saved to: autogluon_comparison_results/run_20250728_225040/predictions/casehold_after_predictions.csv
2025-07-28 23:02:41,024 - __main__ - INFO - Confusion matrix saved to: autogluon_comparison_results/run_20250728_225040/confusion_matrices/casehold_after_confusion_matrix.png
2025-07-28 23:02:41,046 - __main__ - INFO - Classification report saved to: autogluon_comparison_results/run_20250728_225040/confusion_matrices/casehold_after_classification_report.csv
2025-07-28 23:02:45,514 - __main__ - INFO - Evaluation completed for casehold (after)
2025-07-28 23:02:45,515 - __main__ - INFO - Problem type: multiclass
2025-07-28 23:02:45,515 - __main__ - INFO - Eval metric: accuracy
2025-07-28 23:02:45,515 - __main__ - INFO - Best model: RandomForestGini
2025-07-28 23:02:45,515 - __main__ - INFO - Best validation score: 0.1900639819345126
2025-07-28 23:02:45,515 - __main__ - INFO - Test score: 0.20436582611968385
2025-07-28 23:02:45,515 - __main__ - INFO - Accuracy: 0.19608581106511103
2025-07-28 23:02:45,515 - __main__ - INFO - Total models trained: 14
2025-07-28 23:02:45,515 - __main__ - INFO - Model size: 255.7 MB
2025-07-28 23:02:45,515 - __main__ - INFO - Model parameters: N/A
2025-07-28 23:02:45,515 - __main__ - INFO - Top models:
2025-07-28 23:02:45,515 - __main__ - INFO -   1. RandomForestGini: 0.1900639819345126
2025-07-28 23:02:45,516 - __main__ - INFO -   2. RandomForestEntr: 0.1900639819345126
2025-07-28 23:02:45,516 - __main__ - INFO -   3. ExtraTreesGini: 0.1900639819345126
2025-07-28 23:02:45,517 - __main__ - INFO - 
============================================================
2025-07-28 23:02:45,517 - __main__ - INFO - Processing dataset: scienceqa
2025-07-28 23:02:45,517 - __main__ - INFO - ============================================================
2025-07-28 23:02:45,517 - __main__ - INFO - 
Processing before preprocessing variant...
2025-07-28 23:02:45,517 - __main__ - INFO - Loading train_subset dataset from /storage/nammt/autogluon/scienceqa_outlier
2025-07-28 23:02:45,517 - __main__ - ERROR - Dataset files not found at /storage/nammt/autogluon/scienceqa_outlier for variant train_subset
2025-07-28 23:02:45,517 - __main__ - WARNING - Skipping scienceqa before preprocessing due to loading error
2025-07-28 23:02:45,518 - __main__ - INFO - 
Processing after preprocessing variant...
2025-07-28 23:02:45,518 - __main__ - INFO - Loading after dataset from /storage/nammt/autogluon/scienceqa_outlier
2025-07-28 23:02:46,148 - __main__ - INFO - Loaded scienceqa after dataset:
2025-07-28 23:02:46,148 - __main__ - INFO -   Training samples: 4220
2025-07-28 23:02:46,148 - __main__ - INFO -   Validation samples: 1272
2025-07-28 23:02:46,148 - __main__ - INFO -   Testing samples: 1273
2025-07-28 23:02:46,148 - __main__ - INFO -   Features: 384
2025-07-28 23:02:46,148 - __main__ - INFO -   Target column: answer
2025-07-28 23:02:46,148 - __main__ - INFO - Training AutoGluon model for scienceqa (after)...
2025-07-28 23:02:46,149 - __main__ - INFO - Using TabularPredictor for scienceqa (after)
2025-07-29 02:15:18,087 - __main__ - INFO - Initialized AutoGluonComparisonTrainer
2025-07-29 02:15:18,087 - __main__ - INFO - Output directory: autogluon_comparison_results/run_20250729_021518
2025-07-29 02:15:18,087 - __main__ - INFO - Time limit per model: 3600 seconds
2025-07-29 02:15:18,087 - __main__ - INFO - Using preset: medium_quality
2025-07-29 02:15:18,087 - __main__ - INFO - 
============================================================
2025-07-29 02:15:18,087 - __main__ - INFO - Processing dataset: anli_r1
2025-07-29 02:15:18,087 - __main__ - INFO - ============================================================
2025-07-29 02:15:18,087 - __main__ - INFO - 
Processing before preprocessing variant...
2025-07-29 02:15:18,088 - __main__ - INFO - Loading train_subset dataset from /storage/nammt/autogluon/anli_r1_noisy
2025-07-29 02:15:18,088 - __main__ - ERROR - Dataset files not found at /storage/nammt/autogluon/anli_r1_noisy for variant train_subset
2025-07-29 02:15:18,088 - __main__ - WARNING - Skipping anli_r1 before preprocessing due to loading error
2025-07-29 02:15:18,088 - __main__ - INFO - 
Processing after preprocessing variant...
2025-07-29 02:15:18,088 - __main__ - INFO - Loading after dataset from /storage/nammt/autogluon/anli_r1_noisy
2025-07-29 02:15:18,794 - __main__ - INFO - Loaded anli_r1 after dataset:
2025-07-29 02:15:18,794 - __main__ - INFO -   Training samples: 2000
2025-07-29 02:15:18,794 - __main__ - INFO -   Validation samples: 1000
2025-07-29 02:15:18,794 - __main__ - INFO -   Testing samples: 1000
2025-07-29 02:15:18,794 - __main__ - INFO -   Features: 388
2025-07-29 02:15:18,794 - __main__ - INFO -   Target column: label
2025-07-29 02:15:18,794 - __main__ - INFO - Training AutoGluon model for anli_r1 (after)...
2025-07-29 02:15:18,795 - __main__ - INFO - Using TabularPredictor for anli_r1 (after)
2025-07-29 02:18:51,878 - __main__ - INFO - Tabular model training completed for anli_r1 (after) in 213.08 seconds
2025-07-29 02:18:51,878 - __main__ - INFO - Evaluating model for anli_r1 (after)...
2025-07-29 02:18:51,878 - __main__ - INFO - Getting model information for anli_r1 (after)...
2025-07-29 02:18:51,880 - __main__ - INFO - Model info for anli_r1 (after): {'model_size_mb': 140.21, 'num_parameters': 'N/A', 'model_type': 'TabularPredictor'}
2025-07-29 02:18:51,881 - __main__ - INFO - Saving predictions and creating confusion matrix for anli_r1 (after)...
2025-07-29 02:18:55,130 - __main__ - INFO - Predictions saved to: autogluon_comparison_results/run_20250729_021518/predictions/anli_r1_after_predictions.csv
2025-07-29 02:18:55,995 - __main__ - INFO - Confusion matrix saved to: autogluon_comparison_results/run_20250729_021518/confusion_matrices/anli_r1_after_confusion_matrix.png
2025-07-29 02:18:56,017 - __main__ - INFO - Classification report saved to: autogluon_comparison_results/run_20250729_021518/confusion_matrices/anli_r1_after_classification_report.csv
2025-07-29 02:18:59,614 - __main__ - INFO - Evaluation completed for anli_r1 (after)
2025-07-29 02:18:59,615 - __main__ - INFO - Problem type: multiclass
2025-07-29 02:18:59,615 - __main__ - INFO - Eval metric: accuracy
2025-07-29 02:18:59,615 - __main__ - INFO - Best model: RandomForestEntr
2025-07-29 02:18:59,615 - __main__ - INFO - Best validation score: 0.33
2025-07-29 02:18:59,615 - __main__ - INFO - Test score: 0.344
2025-07-29 02:18:59,615 - __main__ - INFO - Accuracy: 0.314
2025-07-29 02:18:59,615 - __main__ - INFO - Total models trained: 14
2025-07-29 02:18:59,615 - __main__ - INFO - Model size: 140.21 MB
2025-07-29 02:18:59,615 - __main__ - INFO - Model parameters: N/A
2025-07-29 02:18:59,615 - __main__ - INFO - Top models:
2025-07-29 02:18:59,615 - __main__ - INFO -   1. RandomForestEntr: 0.33
2025-07-29 02:18:59,616 - __main__ - INFO -   2. ExtraTreesGini: 0.338
2025-07-29 02:18:59,616 - __main__ - INFO -   3. ExtraTreesEntr: 0.324
2025-07-29 02:18:59,616 - __main__ - INFO - 
============================================================
2025-07-29 02:18:59,616 - __main__ - INFO - Processing dataset: casehold
2025-07-29 02:18:59,616 - __main__ - INFO - ============================================================
2025-07-29 02:18:59,616 - __main__ - INFO - 
Processing before preprocessing variant...
2025-07-29 02:18:59,617 - __main__ - INFO - Loading train_subset dataset from /storage/nammt/autogluon/casehold_imbalanced
2025-07-29 02:18:59,617 - __main__ - ERROR - Dataset files not found at /storage/nammt/autogluon/casehold_imbalanced for variant train_subset
2025-07-29 02:18:59,617 - __main__ - WARNING - Skipping casehold before preprocessing due to loading error
2025-07-29 02:18:59,617 - __main__ - INFO - 
Processing after preprocessing variant...
2025-07-29 02:18:59,617 - __main__ - INFO - Loading after dataset from /storage/nammt/autogluon/casehold_imbalanced
2025-07-29 02:19:01,699 - __main__ - INFO - Loaded casehold after dataset:
2025-07-29 02:19:01,699 - __main__ - INFO -   Training samples: 9500
2025-07-29 02:19:01,699 - __main__ - INFO -   Validation samples: 5314
2025-07-29 02:19:01,699 - __main__ - INFO -   Testing samples: 5314
2025-07-29 02:19:01,699 - __main__ - INFO -   Features: 384
2025-07-29 02:19:01,700 - __main__ - INFO -   Target column: label
2025-07-29 02:19:01,700 - __main__ - INFO - Training AutoGluon model for casehold (after)...
2025-07-29 02:19:01,700 - __main__ - INFO - Using TabularPredictor for casehold (after)
2025-07-29 02:25:38,347 - __main__ - INFO - Tabular model training completed for casehold (after) in 396.65 seconds
2025-07-29 02:25:38,347 - __main__ - INFO - Evaluating model for casehold (after)...
2025-07-29 02:25:38,347 - __main__ - INFO - Getting model information for casehold (after)...
2025-07-29 02:25:38,349 - __main__ - INFO - Model info for casehold (after): {'model_size_mb': 248.05, 'num_parameters': 'N/A', 'model_type': 'TabularPredictor'}
2025-07-29 02:25:38,349 - __main__ - INFO - Saving predictions and creating confusion matrix for casehold (after)...
2025-07-29 02:25:43,009 - __main__ - INFO - Predictions saved to: autogluon_comparison_results/run_20250729_021518/predictions/casehold_after_predictions.csv
2025-07-29 02:25:43,997 - __main__ - INFO - Confusion matrix saved to: autogluon_comparison_results/run_20250729_021518/confusion_matrices/casehold_after_confusion_matrix.png
2025-07-29 02:25:44,021 - __main__ - INFO - Classification report saved to: autogluon_comparison_results/run_20250729_021518/confusion_matrices/casehold_after_classification_report.csv
2025-07-29 02:25:47,608 - __main__ - INFO - Evaluation completed for casehold (after)
2025-07-29 02:25:47,609 - __main__ - INFO - Problem type: multiclass
2025-07-29 02:25:47,609 - __main__ - INFO - Eval metric: accuracy
2025-07-29 02:25:47,609 - __main__ - INFO - Best model: KNeighborsUnif
2025-07-29 02:25:47,609 - __main__ - INFO - Best validation score: 0.1940158073014678
2025-07-29 02:25:47,609 - __main__ - INFO - Test score: 0.20530673692133986
2025-07-29 02:25:47,609 - __main__ - INFO - Accuracy: 0.19966127211140383
2025-07-29 02:25:47,609 - __main__ - INFO - Total models trained: 14
2025-07-29 02:25:47,609 - __main__ - INFO - Model size: 248.05 MB
2025-07-29 02:25:47,609 - __main__ - INFO - Model parameters: N/A
2025-07-29 02:25:47,609 - __main__ - INFO - Top models:
2025-07-29 02:25:47,610 - __main__ - INFO -   1. KNeighborsUnif: 0.1940158073014678
2025-07-29 02:25:47,610 - __main__ - INFO -   2. NeuralNetFastAI: 0.19665035754610463
2025-07-29 02:25:47,610 - __main__ - INFO -   3. LightGBMXT: 0.19853217914941665
2025-07-29 02:25:47,612 - __main__ - INFO - Detailed results saved to: autogluon_comparison_results/run_20250729_021518/detailed_results_20250729_022547.json
2025-07-29 02:25:47,612 - __main__ - WARNING - No comparisons to save
2025-07-29 11:31:41,739 - __main__ - INFO - Initialized AutoGluonComparisonTrainer
2025-07-29 11:31:41,739 - __main__ - INFO - Output directory: autogluon_comparison_results/run_20250729_113141
2025-07-29 11:31:41,739 - __main__ - INFO - Time limit per model: 3000 seconds
2025-07-29 11:31:41,739 - __main__ - INFO - Using preset: medium_quality
2025-07-29 11:31:41,739 - __main__ - INFO - 
============================================================
2025-07-29 11:31:41,739 - __main__ - INFO - Processing dataset: anli_r1_full
2025-07-29 11:31:41,740 - __main__ - INFO - ============================================================
2025-07-29 11:31:41,740 - __main__ - INFO - 
Processing before preprocessing variant...
2025-07-29 11:31:41,740 - __main__ - INFO - Loading train_subset dataset from /storage/nammt/autogluon/anli_r1_full
2025-07-29 11:31:41,740 - __main__ - ERROR - Dataset files not found at /storage/nammt/autogluon/anli_r1_full for variant train_subset
2025-07-29 11:31:41,740 - __main__ - WARNING - Skipping anli_r1_full before preprocessing due to loading error
2025-07-29 11:31:41,740 - __main__ - INFO - 
Processing after preprocessing variant...
2025-07-29 11:31:41,740 - __main__ - INFO - Loading after dataset from /storage/nammt/autogluon/anli_r1_full
2025-07-29 11:31:43,813 - __main__ - INFO - Loaded anli_r1_full after dataset:
2025-07-29 11:31:43,813 - __main__ - INFO -   Training samples: 16946
2025-07-29 11:31:43,813 - __main__ - INFO -   Validation samples: 1000
2025-07-29 11:31:43,813 - __main__ - INFO -   Testing samples: 1000
2025-07-29 11:31:43,813 - __main__ - INFO -   Features: 384
2025-07-29 11:31:43,813 - __main__ - INFO -   Target column: label
2025-07-29 11:31:43,814 - __main__ - INFO - Training AutoGluon model for anli_r1_full (after)...
2025-07-29 11:31:43,814 - __main__ - INFO - Using TabularPredictor for anli_r1_full (after)
2025-07-29 11:37:58,059 - __main__ - INFO - Tabular model training completed for anli_r1_full (after) in 374.25 seconds
2025-07-29 11:37:58,059 - __main__ - INFO - Evaluating model for anli_r1_full (after)...
2025-07-29 11:37:58,059 - __main__ - INFO - Getting model information for anli_r1_full (after)...
2025-07-29 11:37:58,061 - __main__ - INFO - Model info for anli_r1_full (after): {'model_size_mb': 851.9, 'num_parameters': 'N/A', 'model_type': 'TabularPredictor'}
2025-07-29 11:37:58,061 - __main__ - INFO - Saving predictions and creating confusion matrix for anli_r1_full (after)...
2025-07-29 11:37:59,076 - __main__ - INFO - Predictions saved to: autogluon_comparison_results/run_20250729_113141/predictions/anli_r1_full_after_predictions.csv
2025-07-29 11:37:59,929 - __main__ - INFO - Confusion matrix saved to: autogluon_comparison_results/run_20250729_113141/confusion_matrices/anli_r1_full_after_confusion_matrix.png
2025-07-29 11:37:59,949 - __main__ - INFO - Classification report saved to: autogluon_comparison_results/run_20250729_113141/confusion_matrices/anli_r1_full_after_classification_report.csv
2025-07-29 11:38:03,312 - __main__ - INFO - Evaluation completed for anli_r1_full (after)
2025-07-29 11:38:03,312 - __main__ - INFO - Problem type: multiclass
2025-07-29 11:38:03,312 - __main__ - INFO - Eval metric: accuracy
2025-07-29 11:38:03,312 - __main__ - INFO - Best model: NeuralNetTorch
2025-07-29 11:38:03,313 - __main__ - INFO - Best validation score: 0.327
2025-07-29 11:38:03,313 - __main__ - INFO - Test score: 0.335
2025-07-29 11:38:03,313 - __main__ - INFO - Accuracy: 0.314
2025-07-29 11:38:03,313 - __main__ - INFO - Total models trained: 14
2025-07-29 11:38:03,313 - __main__ - INFO - Model size: 851.9 MB
2025-07-29 11:38:03,313 - __main__ - INFO - Model parameters: N/A
2025-07-29 11:38:03,313 - __main__ - INFO - Top models:
2025-07-29 11:38:03,313 - __main__ - INFO -   1. NeuralNetTorch: 0.327
2025-07-29 11:38:03,313 - __main__ - INFO -   2. LightGBMLarge: 0.335
2025-07-29 11:38:03,313 - __main__ - INFO -   3. LightGBMXT: 0.333
2025-07-29 11:38:03,315 - __main__ - INFO - Detailed results saved to: autogluon_comparison_results/run_20250729_113141/detailed_results_20250729_113803.json
2025-07-29 11:38:03,315 - __main__ - WARNING - No comparisons to save
2025-07-29 12:43:28,101 - __main__ - INFO - Initialized AutoGluonComparisonTrainer
2025-07-29 12:43:28,101 - __main__ - INFO - Output directory: autogluon_comparison_results/run_20250729_124328
2025-07-29 12:43:28,102 - __main__ - INFO - Time limit per model: 3000 seconds
2025-07-29 12:43:28,102 - __main__ - INFO - Using preset: medium_quality
2025-07-29 12:43:28,102 - __main__ - INFO - 
============================================================
2025-07-29 12:43:28,102 - __main__ - INFO - Processing dataset: casehold_full
2025-07-29 12:43:28,102 - __main__ - INFO - ============================================================
2025-07-29 12:43:28,102 - __main__ - INFO - 
Processing before preprocessing variant...
2025-07-29 12:43:28,102 - __main__ - INFO - Loading train_subset dataset from /storage/nammt/autogluon/casehold_full
2025-07-29 12:43:28,102 - __main__ - ERROR - Dataset files not found at /storage/nammt/autogluon/casehold_full for variant train_subset
2025-07-29 12:43:28,102 - __main__ - WARNING - Skipping casehold_full before preprocessing due to loading error
2025-07-29 12:43:28,102 - __main__ - INFO - 
Processing after preprocessing variant...
2025-07-29 12:43:28,102 - __main__ - INFO - Loading after dataset from /storage/nammt/autogluon/casehold_full
2025-07-29 12:43:33,963 - __main__ - INFO - Loaded casehold_full after dataset:
2025-07-29 12:43:33,963 - __main__ - INFO -   Training samples: 43045
2025-07-29 12:43:33,963 - __main__ - INFO -   Validation samples: 5314
2025-07-29 12:43:33,963 - __main__ - INFO -   Testing samples: 5314
2025-07-29 12:43:33,963 - __main__ - INFO -   Features: 384
2025-07-29 12:43:33,963 - __main__ - INFO -   Target column: label
2025-07-29 12:43:33,963 - __main__ - INFO - Training AutoGluon model for casehold_full (after)...
2025-07-29 12:43:33,964 - __main__ - INFO - Using TabularPredictor for casehold_full (after)
2025-07-29 13:02:36,605 - __main__ - INFO - Tabular model training completed for casehold_full (after) in 1142.64 seconds
2025-07-29 13:02:36,606 - __main__ - INFO - Evaluating model for casehold_full (after)...
2025-07-29 13:02:36,606 - __main__ - INFO - Getting model information for casehold_full (after)...
2025-07-29 13:02:36,608 - __main__ - INFO - Model info for casehold_full (after): {'model_size_mb': 3138.15, 'num_parameters': 'N/A', 'model_type': 'TabularPredictor'}
2025-07-29 13:02:36,608 - __main__ - INFO - Saving predictions and creating confusion matrix for casehold_full (after)...
2025-07-29 13:02:41,570 - __main__ - INFO - Predictions saved to: autogluon_comparison_results/run_20250729_124328/predictions/casehold_full_after_predictions.csv
2025-07-29 13:02:42,506 - __main__ - INFO - Confusion matrix saved to: autogluon_comparison_results/run_20250729_124328/confusion_matrices/casehold_full_after_confusion_matrix.png
2025-07-29 13:02:42,529 - __main__ - INFO - Classification report saved to: autogluon_comparison_results/run_20250729_124328/confusion_matrices/casehold_full_after_classification_report.csv
2025-07-29 13:02:57,546 - __main__ - INFO - Evaluation completed for casehold_full (after)
2025-07-29 13:02:57,547 - __main__ - INFO - Problem type: multiclass
2025-07-29 13:02:57,547 - __main__ - INFO - Eval metric: accuracy
2025-07-29 13:02:57,547 - __main__ - INFO - Best model: XGBoost
2025-07-29 13:02:57,547 - __main__ - INFO - Best validation score: 0.21019947308995107
2025-07-29 13:02:57,547 - __main__ - INFO - Test score: 0.2118931125329319
2025-07-29 13:02:57,561 - __main__ - INFO - Accuracy: 0.20342491531802784
2025-07-29 13:02:57,561 - __main__ - INFO - Total models trained: 14
2025-07-29 13:02:57,561 - __main__ - INFO - Model size: 3138.15 MB
2025-07-29 13:02:57,562 - __main__ - INFO - Model parameters: N/A
2025-07-29 13:02:57,562 - __main__ - INFO - Top models:
2025-07-29 13:02:57,562 - __main__ - INFO -   1. XGBoost: 0.21019947308995107
2025-07-29 13:02:57,562 - __main__ - INFO -   2. NeuralNetFastAI: 0.20681219420398947
2025-07-29 13:02:57,562 - __main__ - INFO -   3. LightGBMXT: 0.2122694768535943
2025-07-29 13:02:57,565 - __main__ - INFO - Detailed results saved to: autogluon_comparison_results/run_20250729_124328/detailed_results_20250729_130257.json
2025-07-29 13:02:57,566 - __main__ - WARNING - No comparisons to save
