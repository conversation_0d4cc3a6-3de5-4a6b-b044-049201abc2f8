{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 1) To set up your own data cleaning pipeline"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["#Load the datasets\n", "def read_dataset(name): #when only one dataset is provided as input\n", "    import pandas as pd\n", "    if name == \"gpsa\":\n", "        df = pd.read_csv('../datasets/googleplaystore.csv', sep=',', encoding ='ISO-8859-1')\n", "    elif name == \"gpsu\":\n", "        df = pd.read_csv('../datasets/googleplaystore_reviews.csv', sep=',',encoding = 'ISO-8859-1')  \n", "    elif name == \"titanic\":\n", "        df = pd.read_csv('../datasets/titanic/titanic_train.csv', sep=',', encoding ='ISO-8859-1')\n", "    elif name == \"house\":\n", "        df = pd.read_csv('../datasets/house/house_train.csv', sep=',', encoding ='ISO-8859-1')\n", "    else: \n", "        raise ValueError('Invalid dataset name')               \n", "    return df\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PassengerId</th>\n", "      <th>Survived</th>\n", "      <th>Pclass</th>\n", "      <th>Name</th>\n", "      <th>Sex</th>\n", "      <th>Age</th>\n", "      <th>SibSp</th>\n", "      <th>Parch</th>\n", "      <th>Ticket</th>\n", "      <th>Fare</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Embarked</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>22.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>A/5 21171</td>\n", "      <td>7.2500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...</td>\n", "      <td>female</td>\n", "      <td>38.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>PC 17599</td>\n", "      <td>71.2833</td>\n", "      <td>C85</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>26.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>STON/O2. 3101282</td>\n", "      <td>7.9250</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>35.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>113803</td>\n", "      <td>53.1000</td>\n", "      <td>C123</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>35.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>373450</td>\n", "      <td>8.0500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>6</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>330877</td>\n", "      <td>8.4583</td>\n", "      <td>NaN</td>\n", "      <td>Q</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>54.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>17463</td>\n", "      <td>51.8625</td>\n", "      <td>E46</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>male</td>\n", "      <td>2.0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>349909</td>\n", "      <td>21.0750</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>9</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>27.0</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>347742</td>\n", "      <td>11.1333</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>10</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>14.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>237736</td>\n", "      <td>30.0708</td>\n", "      <td>NaN</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>11</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>PP 9549</td>\n", "      <td>16.7000</td>\n", "      <td>G6</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON>, Miss<PERSON></td>\n", "      <td>female</td>\n", "      <td>58.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>113783</td>\n", "      <td>26.5500</td>\n", "      <td>C103</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>13</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>20.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>A/5. 2151</td>\n", "      <td>8.0500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>14</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>39.0</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>347082</td>\n", "      <td>31.2750</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>15</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>14.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>350406</td>\n", "      <td>7.8542</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>16</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>55.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>248706</td>\n", "      <td>16.0000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>17</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, <PERSON><PERSON></td>\n", "      <td>male</td>\n", "      <td>2.0</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>382652</td>\n", "      <td>29.1250</td>\n", "      <td>NaN</td>\n", "      <td>Q</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>18</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>244373</td>\n", "      <td>13.0000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>19</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON><PERSON>...</td>\n", "      <td>female</td>\n", "      <td>31.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>345763</td>\n", "      <td>18.0000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>20</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. <PERSON></td>\n", "      <td>female</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2649</td>\n", "      <td>7.2250</td>\n", "      <td>NaN</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>21</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>35.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>239865</td>\n", "      <td>26.0000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>22</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>34.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>248698</td>\n", "      <td>13.0000</td>\n", "      <td>D56</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>23</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> \"<PERSON>\"</td>\n", "      <td>female</td>\n", "      <td>15.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>330923</td>\n", "      <td>8.0292</td>\n", "      <td>NaN</td>\n", "      <td>Q</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>24</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>28.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>113788</td>\n", "      <td>35.5000</td>\n", "      <td>A6</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>25</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>8.0</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>349909</td>\n", "      <td>21.0750</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>26</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>...</td>\n", "      <td>female</td>\n", "      <td>38.0</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "      <td>347077</td>\n", "      <td>31.3875</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>27</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>ir, Mr. <PERSON><PERSON></td>\n", "      <td>male</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2631</td>\n", "      <td>7.2250</td>\n", "      <td>NaN</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>28</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>19.0</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>19950</td>\n", "      <td>263.0000</td>\n", "      <td>C23 C25 C27</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>29</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> \"<PERSON><PERSON>\"</td>\n", "      <td>female</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>330959</td>\n", "      <td>7.8792</td>\n", "      <td>NaN</td>\n", "      <td>Q</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>349216</td>\n", "      <td>7.8958</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>861</th>\n", "      <td>862</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>21.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>28134</td>\n", "      <td>11.5000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>862</th>\n", "      <td>863</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON>, Mrs. <PERSON> (<PERSON>..</td>\n", "      <td>female</td>\n", "      <td>48.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>17466</td>\n", "      <td>25.9292</td>\n", "      <td>D17</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>863</th>\n", "      <td>864</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, <PERSON><PERSON> \"<PERSON>\"</td>\n", "      <td>female</td>\n", "      <td>NaN</td>\n", "      <td>8</td>\n", "      <td>2</td>\n", "      <td>CA. 2343</td>\n", "      <td>69.5500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>864</th>\n", "      <td>865</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>24.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>233866</td>\n", "      <td>13.0000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>865</th>\n", "      <td>866</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON>, Mrs. (<PERSON><PERSON><PERSON>)</td>\n", "      <td>female</td>\n", "      <td>42.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>236852</td>\n", "      <td>13.0000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>866</th>\n", "      <td>867</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON> y <PERSON>, Miss. Asuncion</td>\n", "      <td>female</td>\n", "      <td>27.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>SC/PARIS 2149</td>\n", "      <td>13.8583</td>\n", "      <td>NaN</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>867</th>\n", "      <td>868</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON> II</td>\n", "      <td>male</td>\n", "      <td>31.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>PC 17590</td>\n", "      <td>50.4958</td>\n", "      <td>A24</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>868</th>\n", "      <td>869</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>345777</td>\n", "      <td>9.5000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>869</th>\n", "      <td>870</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON>, <PERSON><PERSON></td>\n", "      <td>male</td>\n", "      <td>4.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>347742</td>\n", "      <td>11.1333</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>870</th>\n", "      <td>871</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>26.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>349248</td>\n", "      <td>7.8958</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>871</th>\n", "      <td>872</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)</td>\n", "      <td>female</td>\n", "      <td>47.0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>11751</td>\n", "      <td>52.5542</td>\n", "      <td>D35</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>872</th>\n", "      <td>873</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON><PERSON></td>\n", "      <td>male</td>\n", "      <td>33.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>695</td>\n", "      <td>5.0000</td>\n", "      <td>B51 B53 B55</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>873</th>\n", "      <td>874</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>47.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>345765</td>\n", "      <td>9.0000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>874</th>\n", "      <td>875</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>28.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>P/PP 3381</td>\n", "      <td>24.0000</td>\n", "      <td>NaN</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>875</th>\n", "      <td>876</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON>, <PERSON><PERSON> \"<PERSON>\"</td>\n", "      <td>female</td>\n", "      <td>15.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2667</td>\n", "      <td>7.2250</td>\n", "      <td>NaN</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>876</th>\n", "      <td>877</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>20.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7534</td>\n", "      <td>9.8458</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>877</th>\n", "      <td>878</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>19.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>349212</td>\n", "      <td>7.8958</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>878</th>\n", "      <td>879</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>NaN</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>349217</td>\n", "      <td>7.8958</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>879</th>\n", "      <td>880</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>56.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>11767</td>\n", "      <td>83.1583</td>\n", "      <td>C50</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>880</th>\n", "      <td>881</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td><PERSON>, Mrs. <PERSON> (<PERSON><PERSON><PERSON>)</td>\n", "      <td>female</td>\n", "      <td>25.0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>230433</td>\n", "      <td>26.0000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>881</th>\n", "      <td>882</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>33.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>349257</td>\n", "      <td>7.8958</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>882</th>\n", "      <td>883</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON>, Miss. G<PERSON></td>\n", "      <td>female</td>\n", "      <td>22.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7552</td>\n", "      <td>10.5167</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>883</th>\n", "      <td>884</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>28.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>C.A./SOTON 34068</td>\n", "      <td>10.5000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>884</th>\n", "      <td>885</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>25.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>SOTON/OQ 392076</td>\n", "      <td>7.0500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>885</th>\n", "      <td>886</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, Mrs. <PERSON> (Margaret <PERSON>)</td>\n", "      <td>female</td>\n", "      <td>39.0</td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>382652</td>\n", "      <td>29.1250</td>\n", "      <td>NaN</td>\n", "      <td>Q</td>\n", "    </tr>\n", "    <tr>\n", "      <th>886</th>\n", "      <td>887</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>male</td>\n", "      <td>27.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>211536</td>\n", "      <td>13.0000</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>887</th>\n", "      <td>888</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>19.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>112053</td>\n", "      <td>30.0000</td>\n", "      <td>B42</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>888</th>\n", "      <td>889</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, <PERSON><PERSON> \"<PERSON>\"</td>\n", "      <td>female</td>\n", "      <td>NaN</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>W./C. 6607</td>\n", "      <td>23.4500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>889</th>\n", "      <td>890</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>26.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>111369</td>\n", "      <td>30.0000</td>\n", "      <td>C148</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>890</th>\n", "      <td>891</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>32.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>370376</td>\n", "      <td>7.7500</td>\n", "      <td>NaN</td>\n", "      <td>Q</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>891 rows × 12 columns</p>\n", "</div>"], "text/plain": ["     PassengerId  Survived  Pclass  \\\n", "0              1         0       3   \n", "1              2         1       1   \n", "2              3         1       3   \n", "3              4         1       1   \n", "4              5         0       3   \n", "5              6         0       3   \n", "6              7         0       1   \n", "7              8         0       3   \n", "8              9         1       3   \n", "9             10         1       2   \n", "10            11         1       3   \n", "11            12         1       1   \n", "12            13         0       3   \n", "13            14         0       3   \n", "14            15         0       3   \n", "15            16         1       2   \n", "16            17         0       3   \n", "17            18         1       2   \n", "18            19         0       3   \n", "19            20         1       3   \n", "20            21         0       2   \n", "21            22         1       2   \n", "22            23         1       3   \n", "23            24         1       1   \n", "24            25         0       3   \n", "25            26         1       3   \n", "26            27         0       3   \n", "27            28         0       1   \n", "28            29         1       3   \n", "29            30         0       3   \n", "..           ...       ...     ...   \n", "861          862         0       2   \n", "862          863         1       1   \n", "863          864         0       3   \n", "864          865         0       2   \n", "865          866         1       2   \n", "866          867         1       2   \n", "867          868         0       1   \n", "868          869         0       3   \n", "869          870         1       3   \n", "870          871         0       3   \n", "871          872         1       1   \n", "872          873         0       1   \n", "873          874         0       3   \n", "874          875         1       2   \n", "875          876         1       3   \n", "876          877         0       3   \n", "877          878         0       3   \n", "878          879         0       3   \n", "879          880         1       1   \n", "880          881         1       2   \n", "881          882         0       3   \n", "882          883         0       3   \n", "883          884         0       2   \n", "884          885         0       3   \n", "885          886         0       3   \n", "886          887         0       2   \n", "887          888         1       1   \n", "888          889         0       3   \n", "889          890         1       1   \n", "890          891         0       3   \n", "\n", "                                                  Name     Sex   Age  SibSp  \\\n", "0                              <PERSON><PERSON>, Mr. <PERSON>    male  22.0      1   \n", "1    C<PERSON>ngs, Mrs. <PERSON> (Florence Briggs Th...  female  38.0      1   \n", "2                               <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female  26.0      0   \n", "3         <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female  35.0      1   \n", "4                             <PERSON>, Mr. <PERSON>    male  35.0      0   \n", "5                                     <PERSON>, Mr. <PERSON>    male   NaN      0   \n", "6                              <PERSON>, Mr. <PERSON>    male  54.0      0   \n", "7                       <PERSON><PERSON>, <PERSON><PERSON><PERSON>    male   2.0      3   \n", "8    <PERSON>, Mrs. <PERSON> (<PERSON>)  female  27.0      0   \n", "9                  <PERSON><PERSON>, Mrs<PERSON> <PERSON> (<PERSON>)  female  14.0      1   \n", "10                     <PERSON><PERSON>, <PERSON><PERSON>  female   4.0      1   \n", "11                            <PERSON><PERSON>, <PERSON><PERSON>  female  58.0      0   \n", "12                      <PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>    male  20.0      0   \n", "13                         <PERSON><PERSON>, <PERSON>. <PERSON>    male  39.0      1   \n", "14                <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>  female  14.0      0   \n", "15                    <PERSON><PERSON><PERSON>, Mrs. (<PERSON>)   female  55.0      0   \n", "16                                <PERSON>, <PERSON><PERSON>    male   2.0      4   \n", "17                        <PERSON>, Mr. <PERSON>    male   NaN      0   \n", "18   <PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON><PERSON>...  female  31.0      1   \n", "19                             <PERSON><PERSON><PERSON>, Mrs. <PERSON><PERSON>  female   NaN      0   \n", "20                                <PERSON><PERSON><PERSON>, Mr. <PERSON>    male  35.0      0   \n", "21                               <PERSON><PERSON>, Mr. <PERSON>    male  34.0      0   \n", "22                         <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> \"<PERSON>\"  female  15.0      0   \n", "23                        <PERSON><PERSON><PERSON>, Mr. <PERSON>    male  28.0      0   \n", "24                       <PERSON><PERSON>, <PERSON><PERSON>  female   8.0      3   \n", "25   <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>...  female  38.0      1   \n", "26                             <PERSON><PERSON>, Mr. <PERSON><PERSON>N      0   \n", "27                      <PERSON>, Mr. <PERSON>    male  19.0      3   \n", "28                       <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> \"<PERSON><PERSON>\"  female   NaN      0   \n", "29                                 <PERSON><PERSON><PERSON>, Mr. <PERSON><PERSON>    male   NaN      0   \n", "..                                                 ...     ...   ...    ...   \n", "861                        <PERSON>, Mr. <PERSON>    male  21.0      1   \n", "862  <PERSON>, Mrs. <PERSON> (<PERSON>..  female  48.0      0   \n", "863                  <PERSON>, <PERSON><PERSON> \"<PERSON>\"  female   NaN      8   \n", "864                             <PERSON>, Mr. <PERSON>    male  24.0      0   \n", "865                           <PERSON><PERSON>, Mrs. (<PERSON><PERSON><PERSON>)  female  42.0      0   \n", "866                       <PERSON><PERSON> <PERSON>, Miss. Asuncion  female  27.0      1   \n", "867               <PERSON><PERSON>, Mr. <PERSON> II    male  31.0      0   \n", "868                        <PERSON>, Mr. <PERSON>    male   NaN      0   \n", "869                    <PERSON>, <PERSON><PERSON>    male   4.0      1   \n", "870                                  <PERSON><PERSON><PERSON>, Mr. <PERSON>    male  26.0      0   \n", "871   <PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)  female  47.0      1   \n", "872                           <PERSON><PERSON>, Mr. <PERSON><PERSON>    male  33.0      0   \n", "873                        <PERSON><PERSON>, Mr. <PERSON>    male  47.0      0   \n", "874              <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female  28.0      1   \n", "875                   <PERSON><PERSON><PERSON>, <PERSON><PERSON> \"<PERSON>\"  female  15.0      0   \n", "876                      <PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>    male  20.0      0   \n", "877                               <PERSON><PERSON><PERSON>, Mr. <PERSON><PERSON>    male  19.0      0   \n", "878                                 <PERSON><PERSON><PERSON>, Mr. <PERSON>   NaN      0   \n", "879      <PERSON>, Mrs. <PERSON> (<PERSON>)  female  56.0      0   \n", "880       <PERSON>, Mrs. <PERSON> (<PERSON><PERSON><PERSON>)  female  25.0      0   \n", "881                                 <PERSON><PERSON>, Mr. <PERSON>    male  33.0      0   \n", "882                       <PERSON><PERSON><PERSON>, <PERSON><PERSON>  female  22.0      0   \n", "883                      <PERSON><PERSON>, Mr. <PERSON>    male  28.0      0   \n", "884                             <PERSON><PERSON><PERSON>, Mr. <PERSON>    male  25.0      0   \n", "885               <PERSON>, Mrs. <PERSON> (<PERSON>)  female  39.0      0   \n", "886                              <PERSON><PERSON>, <PERSON><PERSON>    male  27.0      0   \n", "887                       <PERSON>, <PERSON><PERSON>  female  19.0      0   \n", "888           <PERSON>, <PERSON><PERSON> \"<PERSON>\"  female   NaN      1   \n", "889                              <PERSON><PERSON>, Mr. <PERSON>    male  26.0      0   \n", "890                                <PERSON><PERSON>, Mr. <PERSON>    male  32.0      0   \n", "\n", "     Parch            Ticket      Fare        Cabin Embarked  \n", "0        0         A/5 21171    7.2500          NaN        S  \n", "1        0          PC 17599   71.2833          C85        C  \n", "2        0  STON/O2. 3101282    7.9250          NaN        S  \n", "3        0            113803   53.1000         C123        S  \n", "4        0            373450    8.0500          NaN        S  \n", "5        0            330877    8.4583          NaN        Q  \n", "6        0             17463   51.8625          E46        S  \n", "7        1            349909   21.0750          NaN        S  \n", "8        2            347742   11.1333          NaN        S  \n", "9        0            237736   30.0708          NaN        C  \n", "10       1           PP 9549   16.7000           G6        S  \n", "11       0            113783   26.5500         C103        S  \n", "12       0         A/5. 2151    8.0500          NaN        S  \n", "13       5            347082   31.2750          NaN        S  \n", "14       0            350406    7.8542          NaN        S  \n", "15       0            248706   16.0000          NaN        S  \n", "16       1            382652   29.1250          NaN        Q  \n", "17       0            244373   13.0000          NaN        S  \n", "18       0            345763   18.0000          NaN        S  \n", "19       0              2649    7.2250          NaN        C  \n", "20       0            239865   26.0000          NaN        S  \n", "21       0            248698   13.0000          D56        S  \n", "22       0            330923    8.0292          NaN        Q  \n", "23       0            113788   35.5000           A6        S  \n", "24       1            349909   21.0750          NaN        S  \n", "25       5            347077   31.3875          NaN        S  \n", "26       0              2631    7.2250          NaN        C  \n", "27       2             19950  263.0000  C23 C25 C27        S  \n", "28       0            330959    7.8792          NaN        Q  \n", "29       0            349216    7.8958          NaN        S  \n", "..     ...               ...       ...          ...      ...  \n", "861      0             28134   11.5000          NaN        S  \n", "862      0             17466   25.9292          D17        S  \n", "863      2          CA. 2343   69.5500          NaN        S  \n", "864      0            233866   13.0000          NaN        S  \n", "865      0            236852   13.0000          NaN        S  \n", "866      0     SC/PARIS 2149   13.8583          NaN        C  \n", "867      0          PC 17590   50.4958          A24        S  \n", "868      0            345777    9.5000          NaN        S  \n", "869      1            347742   11.1333          NaN        S  \n", "870      0            349248    7.8958          NaN        S  \n", "871      1             11751   52.5542          D35        S  \n", "872      0               695    5.0000  B51 B53 B55        S  \n", "873      0            345765    9.0000          NaN        S  \n", "874      0         P/PP 3381   24.0000          NaN        C  \n", "875      0              2667    7.2250          NaN        C  \n", "876      0              7534    9.8458          NaN        S  \n", "877      0            349212    7.8958          NaN        S  \n", "878      0            349217    7.8958          NaN        S  \n", "879      1             11767   83.1583          C50        C  \n", "880      1            230433   26.0000          NaN        S  \n", "881      0            349257    7.8958          NaN        S  \n", "882      0              7552   10.5167          NaN        S  \n", "883      0  C.A./SOTON 34068   10.5000          NaN        S  \n", "884      0   SOTON/OQ 392076    7.0500          NaN        S  \n", "885      5            382652   29.1250          NaN        Q  \n", "886      0            211536   13.0000          NaN        S  \n", "887      0            112053   30.0000          B42        S  \n", "888      2        W./C. 6607   23.4500          NaN        S  \n", "889      0            111369   30.0000         C148        C  \n", "890      0            370376    7.7500          NaN        Q  \n", "\n", "[891 rows x 12 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["read_dataset(\"titanic\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Loading your data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Profiling datasets\n", "      Attribute     Type  Num. Missing Values  Num. Unique Values           Sknewness  Kurtosis\n", "0   PassengerId    int64                  0.0               891.0                   0      -1.2\n", "1      Survived    int64                  0.0                 2.0            0.477717  -1.77179\n", "2        Pclass    int64                  0.0                 3.0           -0.629486  -1.27957\n", "3           Age  float64                177.0                89.0  0.3882898514698658  0.168637\n", "4         SibSp    int64                  0.0                 7.0             3.68913   17.7735\n", "5         Parch    int64                  0.0                 7.0             2.74449   9.71661\n", "6          Fare  float64                  0.0               248.0             4.77925   33.2043\n", "7          Name   object                  0.0               891.0                 N/A       N/A\n", "8           Sex   object                  0.0                 2.0                 N/A       N/A\n", "9        Ticket   object                  0.0               681.0                 N/A       N/A\n", "10        Cabin   object                687.0               148.0                 N/A       N/A\n", "11     Embarked   object                  2.0                 4.0                 N/A       N/A\n"]}], "source": ["import learn2clean.loading.reader as rd \n", "import learn2clean.normalization.normalizer as nl \n", "import pandas as pd\n", "\n", "# executing profiling function for one dataset as input\n", "rd.profile_summary(read_dataset('titanic'), plot=False)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    0\n", "1    1\n", "2    1\n", "3    1\n", "4    0\n", "Name: Survived, dtype: int64"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "read_dataset('titanic')['Survived'].head() # the target variable is categorical (bool)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Reading csv : titanic_train.csv ...\n", "Reading data ...\n", "CPU time: 2.596506118774414 seconds\n", "Profiling datasets\n", "      Attribute     Type  Num. Missing Values  Num. Unique Values           Sknewness  Kurtosis\n", "0   PassengerId  float64                  0.0               891.0                   0      -1.2\n", "1      Survived  float64                  0.0                 2.0            0.477717  -1.77179\n", "2        Pclass  float64                  0.0                 3.0           -0.629486  -1.27957\n", "3           Age  float64                177.0                89.0  0.3882898514698658  0.168637\n", "4         SibSp  float64                  0.0                 7.0             3.68913   17.7735\n", "5         Parch  float64                  0.0                 7.0             2.74449   9.71661\n", "6          Fare  float64                  0.0               248.0             4.77925   33.2043\n", "7          Name   object                  0.0               891.0                 N/A       N/A\n", "8           Sex   object                  0.0                 2.0                 N/A       N/A\n", "9        Ticket   object                  0.0               681.0                 N/A       N/A\n", "10        Cabin   object                687.0               148.0                 N/A       N/A\n", "11     Embarked   object                  2.0                 4.0                 N/A       N/A\n", "\n", "Reading csv : test.csv ...\n", "Reading data ...\n", "CPU time: 0.032224178314208984 seconds\n", "Profiling datasets\n", "      Attribute     Type  Num. Missing Values  Num. Unique Values            Sknewness   Kurtosis\n", "0   PassengerId  float64                  0.0               418.0                    0   -1.20001\n", "1        Pclass  float64                  0.0                 3.0            -0.532252    -1.3805\n", "2           Age  float64                 86.0                80.0  0.45529229694892764  0.0645088\n", "3         SibSp  float64                  0.0                 7.0              4.15336    26.1685\n", "4         Parch  float64                  0.0                 8.0              4.63774    31.0237\n", "5          Fare  float64                  1.0               170.0   3.6739366758439074    17.6931\n", "6          Name   object                  0.0               418.0                  N/A        N/A\n", "7           Sex   object                  0.0                 2.0                  N/A        N/A\n", "8        Ticket   object                  0.0               363.0                  N/A        N/A\n", "9         Cabin   object                327.0                77.0                  N/A        N/A\n", "10     Embarked   object                  0.0                 3.0                  N/A        N/A\n", "\n", "> Number of common features : 11\n", "\n", "gathering and crunching for train and test datasets ...\n", "reindexing for train and test datasets ...\n", "\n", "> Number of categorical features in the training set: 5\n", "> Number of numerical features in the training set: 6\n", "> Number of training samples : 891\n", "> Number of test samples : 418\n", "\n", "> Top sparse features (% missing values on train set):\n", "Cabin       77.1\n", "Age         19.9\n", "Embarked     0.2\n", "dtype: float64\n", "\n", "> Task : classification\n", "0.0    549\n", "1.0    342\n", "Name: Survived, dtype: int64\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Embarked</th>\n", "      <th>Fare</th>\n", "      <th>Name</th>\n", "      <th>Parch</th>\n", "      <th>PassengerId</th>\n", "      <th>Pclass</th>\n", "      <th>Sex</th>\n", "      <th>SibSp</th>\n", "      <th>Ticket</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>22.0</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "      <td>7.2500</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>0.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td>male</td>\n", "      <td>1.0</td>\n", "      <td>A/5 21171</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>38.0</td>\n", "      <td>C85</td>\n", "      <td>C</td>\n", "      <td>71.2833</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (Florence <PERSON>)</td>\n", "      <td>0.0</td>\n", "      <td>2.0</td>\n", "      <td>1.0</td>\n", "      <td>female</td>\n", "      <td>1.0</td>\n", "      <td>PC 17599</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>26.0</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "      <td>7.9250</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td>3.0</td>\n", "      <td>female</td>\n", "      <td>0.0</td>\n", "      <td>STON/O2. 3101282</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>35.0</td>\n", "      <td>C123</td>\n", "      <td>S</td>\n", "      <td>53.1000</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>0.0</td>\n", "      <td>4.0</td>\n", "      <td>1.0</td>\n", "      <td>female</td>\n", "      <td>1.0</td>\n", "      <td>113803</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>35.0</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "      <td>8.0500</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>0.0</td>\n", "      <td>5.0</td>\n", "      <td>3.0</td>\n", "      <td>male</td>\n", "      <td>0.0</td>\n", "      <td>373450</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Age Cabin Embarked     Fare  \\\n", "0  22.0   NaN        S   7.2500   \n", "1  38.0   C85        C  71.2833   \n", "2  26.0   NaN        S   7.9250   \n", "3  35.0  C123        S  53.1000   \n", "4  35.0   NaN        S   8.0500   \n", "\n", "                                                  Name  Parch  PassengerId  \\\n", "0                              <PERSON><PERSON>, <PERSON><PERSON> <PERSON>    0.0          1.0   \n", "1  <PERSON><PERSON><PERSON>s, Mrs. <PERSON> (<PERSON>)    0.0          2.0   \n", "2                               <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>    0.0          3.0   \n", "3         <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)    0.0          4.0   \n", "4                             <PERSON>, Mr. <PERSON>    0.0          5.0   \n", "\n", "   Pclass     Sex  SibSp            Ticket  \n", "0     3.0    male    1.0         A/5 21171  \n", "1     1.0  female    1.0          PC 17599  \n", "2     3.0  female    0.0  STON/O2. 3101282  \n", "3     1.0  female    1.0            113803  \n", "4     3.0    male    0.0            373450  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# no encoding on the target variable\n", "d_not_enc = rd.Reader(sep=',',verbose=True, encoding=False) \n", "\n", "# when you have two datasets as inputs: train and test datasets\n", "titanic  = [\"../datasets/titanic/titanic_train.csv\", \"../datasets/titanic/test.csv\"]\n", "titanic_not_encoded = d_not_enc.train_test_split(titanic, 'Survived')\n", "titanic_not_encoded['train'].head()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Reading csv : titanic_train.csv ...\n", "Reading data ...\n", "CPU time: 0.04419302940368652 seconds\n", "Profiling datasets\n", "      Attribute     Type  Num. Missing Values  Num. Unique Values           Sknewness  Kurtosis\n", "0   PassengerId  float64                  0.0               891.0                   0      -1.2\n", "1      Survived  float64                  0.0                 2.0            0.477717  -1.77179\n", "2        Pclass  float64                  0.0                 3.0           -0.629486  -1.27957\n", "3           Age  float64                177.0                89.0  0.3882898514698658  0.168637\n", "4         SibSp  float64                  0.0                 7.0             3.68913   17.7735\n", "5         Parch  float64                  0.0                 7.0             2.74449   9.71661\n", "6          Fare  float64                  0.0               248.0             4.77925   33.2043\n", "7          Name   object                  0.0               891.0                 N/A       N/A\n", "8           Sex   object                  0.0                 2.0                 N/A       N/A\n", "9        Ticket   object                  0.0               681.0                 N/A       N/A\n", "10        Cabin   object                687.0               148.0                 N/A       N/A\n", "11     Embarked   object                  2.0                 4.0                 N/A       N/A\n", "\n", "> Number of categorical features in the training set: 5\n", "> Number of numerical features in the training set: 7\n", "> Number of data samples : 891\n", "\n", "> Top sparse features (% missing values on dataset set):\n", "Cabin       77.0\n", "Age         19.1\n", "Embarked     0.3\n", "dtype: float64\n", "\n", "> Task : classification\n", "0.0    383\n", "1.0    213\n", "Name: Survived, dtype: int64\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PassengerId</th>\n", "      <th>Survived</th>\n", "      <th>Pclass</th>\n", "      <th>Name</th>\n", "      <th>Sex</th>\n", "      <th>Age</th>\n", "      <th>SibSp</th>\n", "      <th>Parch</th>\n", "      <th>Ticket</th>\n", "      <th>Fare</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Embarked</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>860</th>\n", "      <td>861.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>41.0</td>\n", "      <td>2.0</td>\n", "      <td>0.0</td>\n", "      <td>350026</td>\n", "      <td>14.1083</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>468</th>\n", "      <td>469.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td><PERSON><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>36209</td>\n", "      <td>7.7250</td>\n", "      <td>NaN</td>\n", "      <td>Q</td>\n", "    </tr>\n", "    <tr>\n", "      <th>474</th>\n", "      <td>475.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td><PERSON><PERSON>, Miss. <PERSON></td>\n", "      <td>female</td>\n", "      <td>22.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7553</td>\n", "      <td>9.8375</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>183.0</td>\n", "      <td>0.0</td>\n", "      <td>3.0</td>\n", "      <td><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>male</td>\n", "      <td>9.0</td>\n", "      <td>4.0</td>\n", "      <td>2.0</td>\n", "      <td>347077</td>\n", "      <td>31.3875</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>643</th>\n", "      <td>644.0</td>\n", "      <td>1.0</td>\n", "      <td>3.0</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>NaN</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1601</td>\n", "      <td>56.4958</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     PassengerId  Survived  Pclass                                   Name  \\\n", "860        861.0       0.0     3.0                <PERSON>, Mr. <PERSON>   \n", "468        469.0       0.0     3.0                     <PERSON><PERSON><PERSON>, Mr. <PERSON>   \n", "474        475.0       0.0     3.0            Strandberg, Miss. <PERSON>   \n", "182        183.0       0.0     3.0  <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>   \n", "643        644.0       1.0     3.0                        <PERSON><PERSON>, Mr<PERSON>   \n", "\n", "        Sex   Age  SibSp  Parch  Ticket     Fare Cabin Embarked  \n", "860    male  41.0    2.0    0.0  350026  14.1083   NaN        S  \n", "468    male   NaN    0.0    0.0   36209   7.7250   NaN        Q  \n", "474  female  22.0    0.0    0.0    7553   9.8375   NaN        S  \n", "182    male   9.0    4.0    2.0  347077  31.3875   NaN        S  \n", "643    male   NaN    0.0    0.0    1601  56.4958   NaN        S  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "# otherwise for only one dataset as input, \n", "# train_test_split function will split the input dataset into train/test dataset\n", "titanic_train_only  = [\"../datasets/titanic/titanic_train.csv\"]\n", "titanic_train_only_not_encoded = d_not_enc.train_test_split(titanic_train_only, 'Survived')\n", "titanic_train_only_not_encoded['train'].head() "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Normalize your data"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"Fare\" original variable\n", " 0     7.2500\n", "1    71.2833\n", "2     7.9250\n", "3    53.1000\n", "4     8.0500\n", "Name: Fare, dtype: float64\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.03120589256286621 seconds\n", "\n", "\"Fare\" normalized variable\n", " 0    0.014151\n", "1    0.139136\n", "2    0.015469\n", "3    0.103644\n", "4    0.015713\n", "Name: Fare, dtype: float64\n"]}], "source": ["# >> Examples of normalization\n", "# The choice for the normalizer : 'ZS', 'MM','DS' or 'Log10'\n", "#    Available strategies=\n", "#       - 'ZS' z-score normalization\n", "#       - 'M<PERSON>' MinMax scaling\n", "#       - 'DS' decimal scaling\n", "#       - 'Log10 log10 scaling\n", "\n", "import learn2clean.normalization.normalizer as nl \n", "\n", "# MM normalization with exclude = None, all numeric variables will be normalized\n", "n1= nl.Normalizer(titanic_not_encoded.copy(),strategy='MM',exclude=None, verbose =False)\n", "print('\"Fare\" original variable\\n',titanic_not_encoded['train']['Fare'].head())\n", "print('\"Fare\" normalized variable\\n', n1.transform()['train']['Fare'].head())\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.03693270683288574 seconds\n", "\n", "ZS normalized variables\n", "         Age      Fare     Parch  PassengerId    Pclass     SibSp Cabin  \\\n", "0 -0.530005 -0.502163 -0.473408    -1.729137  0.826913  0.432550   NaN   \n", "1  0.571430  0.786404 -0.473408    -1.725251 -1.565228  0.432550   C85   \n", "2 -0.254646 -0.488580 -0.473408    -1.721365  0.826913 -0.474279   NaN   \n", "3  0.364911  0.420494 -0.473408    -1.717480 -1.565228  0.432550  C123   \n", "4  0.364911 -0.486064 -0.473408    -1.713594  0.826913 -0.474279   NaN   \n", "\n", "  Embarked                                                 Name     Sex  \\\n", "0        <PERSON>, Mr. <PERSON>    male   \n", "1        <PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", "2        <PERSON>, <PERSON><PERSON>  female   \n", "3        <PERSON>         <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", "4        <PERSON>, Mr. <PERSON>    male   \n", "\n", "             Ticket  \n", "0         A/5 21171  \n", "1          PC 17599  \n", "2  STON/O2. 3101282  \n", "3            113803  \n", "4            373450  \n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.03208112716674805 seconds\n", "\n", "DS normalized variables\n", "         Age      Fare         Parch   PassengerId        Pclass         SibSp  \\\n", "0  0.305556  0.103943  1.000000e-07  1.000000e-07  9.999999e-01  7.777778e-01   \n", "1  0.740741  0.869598  1.000000e-07  1.086957e-03  1.000000e-07  7.777778e-01   \n", "2  0.444444  0.222222  1.000000e-07  2.173913e-03  9.999999e-01  1.000000e-07   \n", "3  0.685185  0.817049  1.000000e-07  3.260870e-03  1.000000e-07  7.777778e-01   \n", "4  0.685185  0.227696  1.000000e-07  4.347826e-03  9.999999e-01  1.000000e-07   \n", "\n", "  <PERSON><PERSON><PERSON> Embarked                                                 Name     Sex  \\\n", "0   <PERSON><PERSON>, Mr. <PERSON>    male   \n", "1   C85        <PERSON>, Mrs. <PERSON> (Florence <PERSON>)  female   \n", "2   <PERSON><PERSON>, <PERSON><PERSON>  female   \n", "3  C123        <PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", "4   <PERSON><PERSON>, Mr. <PERSON>    male   \n", "\n", "             Ticket  \n", "0         A/5 21171  \n", "1          PC 17599  \n", "2  STON/O2. 3101282  \n", "3            113803  \n", "4            373450  \n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.022478818893432617 seconds\n", "\n", "Log10 normalized variables none excluded in train dataset\n", " 0    4.0\n", "1    4.0\n", "2    4.0\n", "3    4.0\n", "4    4.0\n", "Name: Fare, dtype: float64\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.024877071380615234 seconds\n", "\n", "Log10 normalized variables none excluded in test dataset\n", " 0    2.0\n", "1    2.0\n", "2    2.0\n", "3    2.0\n", "4    2.0\n", "Name: Fare, dtype: float64\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.025292158126831055 seconds\n", "\n", "Log10 normalized variables Lot<PERSON><PERSON> excluded in train dataset \n", " 0     7.2500\n", "1    71.2833\n", "2     7.9250\n", "3    53.1000\n", "4     8.0500\n", "Name: Fare, dtype: float64\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.022368192672729492 seconds\n", "\n", "Log10 normalized variables <PERSON><PERSON><PERSON> excluded  in test dataset \n", " 0     7.8292\n", "1     7.0000\n", "2     9.6875\n", "3     8.6625\n", "4    12.2875\n", "Name: Fare, dtype: float64\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.026147127151489258 seconds\n", "\n", "Log10 normalized variables none excluded\n", " 0    1.0\n", "1    1.0\n", "2    1.0\n", "3    1.0\n", "4    1.0\n", "Name: Fare, dtype: float64\n", "Log10 normalized variables SalePrice excluded\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.02881932258605957 seconds\n", "\n"]}, {"data": {"text/plain": ["0    1.0\n", "1    1.0\n", "2    1.0\n", "3    1.0\n", "4    1.0\n", "Name: Pclass, dtype: float64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "#ZS normalization\n", "n3= nl.Normalizer(titanic_not_encoded.copy(),strategy='ZS',exclude=None, verbose = False)\n", "print('ZS normalized variables\\n',n3.transform()['train'].head())\n", "\n", "#DS scaling\n", "n4= nl.Normalizer(titanic_not_encoded.copy(),strategy='DS',exclude=None, verbose = False)\n", "print('DS normalized variables\\n',n4.transform()['train'].head())\n", "\n", "#Log10 scaling\n", "n5= nl.Normalizer(titanic_not_encoded.copy(),strategy='Log10',exclude=None, verbose = False)\n", "#print('Log10 normalized variables\\n',n5.transform()['train'].head())\n", "print('Log10 normalized variables none excluded in train dataset\\n',n5.transform()['train']['Fare'].head())\n", "print('Log10 normalized variables none excluded in test dataset\\n',n5.transform()['test']['Fare'].head())\n", "\n", "#Log10 scaling  excluding target variable 'Fare'\n", "n6= nl.Normalizer(titanic_not_encoded.copy(),strategy='Log10',exclude='Fare',verbose = False)\n", "\n", "print(\"Log10 normalized variables 'Fare' excluded in train dataset \\n\",n6.transform()['train']['Fare'].head())\n", "print(\"Log10 normalized variables 'Fare' excluded  in test dataset \\n\",n6.transform()['test']['Fare'].head())\n", "\n", "n7= nl.Normalizer(titanic_not_encoded.copy(),strategy='Log10',exclude='Fare', verbose = False)\n", "print('Log10 normalized variables none excluded\\n', n5.transform()['train']['Fare'].head())\n", "print(\"Log10 normalized variables 'Pclass' excluded\\n\")\n", "n7.transform()['train']['Pclass'].head()\n", "# here P<PERSON><PERSON> is not excluded from normalization!\n", "\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Reading csv : titanic_train.csv ...\n", "Reading data ...\n", "CPU time: 0.03417491912841797 seconds\n", "Profiling datasets\n", "      Attribute     Type  Num. Missing Values  Num. Unique Values           Sknewness  Kurtosis\n", "0   PassengerId  float64                  0.0               891.0                   0      -1.2\n", "1      Survived  float64                  0.0                 2.0            0.477717  -1.77179\n", "2        Pclass  float64                  0.0                 3.0           -0.629486  -1.27957\n", "3           Age  float64                177.0                89.0  0.3882898514698658  0.168637\n", "4         SibSp  float64                  0.0                 7.0             3.68913   17.7735\n", "5         Parch  float64                  0.0                 7.0             2.74449   9.71661\n", "6          Fare  float64                  0.0               248.0             4.77925   33.2043\n", "7          Name   object                  0.0               891.0                 N/A       N/A\n", "8           Sex   object                  0.0                 2.0                 N/A       N/A\n", "9        Ticket   object                  0.0               681.0                 N/A       N/A\n", "10        Cabin   object                687.0               148.0                 N/A       N/A\n", "11     Embarked   object                  2.0                 4.0                 N/A       N/A\n", "\n", "Reading csv : test.csv ...\n", "Reading data ...\n", "CPU time: 0.029849767684936523 seconds\n", "Profiling datasets\n", "      Attribute     Type  Num. Missing Values  Num. Unique Values            Sknewness   Kurtosis\n", "0   PassengerId  float64                  0.0               418.0                    0   -1.20001\n", "1        Pclass  float64                  0.0                 3.0            -0.532252    -1.3805\n", "2           Age  float64                 86.0                80.0  0.45529229694892764  0.0645088\n", "3         SibSp  float64                  0.0                 7.0              4.15336    26.1685\n", "4         Parch  float64                  0.0                 8.0              4.63774    31.0237\n", "5          Fare  float64                  1.0               170.0   3.6739366758439074    17.6931\n", "6          Name   object                  0.0               418.0                  N/A        N/A\n", "7           Sex   object                  0.0                 2.0                  N/A        N/A\n", "8        Ticket   object                  0.0               363.0                  N/A        N/A\n", "9         Cabin   object                327.0                77.0                  N/A        N/A\n", "10     Embarked   object                  0.0                 3.0                  N/A        N/A\n", "\n", "> Number of common features : 11\n", "\n", "gathering and crunching for train and test datasets ...\n", "reindexing for train and test datasets ...\n", "\n", "> Number of categorical features in the training set: 5\n", "> Number of numerical features in the training set: 6\n", "> Number of training samples : 891\n", "> Number of test samples : 418\n", "\n", "> Top sparse features (% missing values on train set):\n", "Cabin       77.1\n", "Age         19.9\n", "Embarked     0.2\n", "dtype: float64\n", "\n", "> Task : classification\n", "0.0    549\n", "1.0    342\n", "Name: Survived, dtype: int64\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.03432297706604004 seconds\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Age</th>\n", "      <th>Fare</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Embarked</th>\n", "      <th>Name</th>\n", "      <th>Parch</th>\n", "      <th>PassengerId</th>\n", "      <th>Pclass</th>\n", "      <th>Sex</th>\n", "      <th>SibSp</th>\n", "      <th>Ticket</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>22.0</td>\n", "      <td>0.103943</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>male</td>\n", "      <td>1</td>\n", "      <td>A/5 21171</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>38.0</td>\n", "      <td>0.869598</td>\n", "      <td>C85</td>\n", "      <td>C</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (Florence <PERSON>)</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>female</td>\n", "      <td>1</td>\n", "      <td>PC 17599</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>26.0</td>\n", "      <td>0.222222</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>female</td>\n", "      <td>0</td>\n", "      <td>STON/O2. 3101282</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>35.0</td>\n", "      <td>0.817049</td>\n", "      <td>C123</td>\n", "      <td>S</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>0</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>female</td>\n", "      <td>1</td>\n", "      <td>113803</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>35.0</td>\n", "      <td>0.227696</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>0</td>\n", "      <td>5</td>\n", "      <td>3</td>\n", "      <td>male</td>\n", "      <td>0</td>\n", "      <td>373450</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Age      Fare Cabin Embarked  \\\n", "0  22.0  0.103943   NaN        S   \n", "1  38.0  0.869598   C85        C   \n", "2  26.0  0.222222   NaN        S   \n", "3  35.0  0.817049  C123        S   \n", "4  35.0  0.227696   NaN        S   \n", "\n", "                                                  Name Parch PassengerId  \\\n", "0                              <PERSON><PERSON>, <PERSON><PERSON> <PERSON>     0           1   \n", "1  <PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0           2   \n", "2                               <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>     0           3   \n", "3         <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0           4   \n", "4                             <PERSON>, Mr. <PERSON>     0           5   \n", "\n", "  Pclass     Sex SibSp            Ticket  \n", "0      3    male     1         A/5 21171  \n", "1      1  female     1          PC 17599  \n", "2      3  female     0  STON/O2. 3101282  \n", "3      1  female     1            113803  \n", "4      3    male     0            373450  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# To prevent that and avoid normalization for more than one numeric variables, \n", "# please change the data type into 'object' data type right after loading \n", "# and before normalization and learn2clean pipeline\n", "\n", "titanic  = [\"../datasets/titanic/titanic_train.csv\", \"../datasets/titanic/test.csv\"]\n", "titanic_not_encoded = d_not_enc.train_test_split(titanic, 'Survived')\n", "\n", "titanic_not_encoded['train']['Pclass'] = titanic_not_encoded['train']['Pclass'].astype('object')\n", "titanic_not_encoded['train']['PassengerId'] = titanic_not_encoded['train']['PassengerId'].astype('object')\n", "titanic_not_encoded['train']['SibSp'] = titanic_not_encoded['train']['SibSp'].astype('object')\n", "titanic_not_encoded['train']['Parch'] = titanic_not_encoded['train']['Parch'].astype('object')\n", "\n", "titanic_not_encoded['test']['Pclass'] = titanic_not_encoded['test']['Pclass'].astype('object')\n", "titanic_not_encoded['test']['PassengerId'] = titanic_not_encoded['test']['PassengerId'].astype('object')\n", "titanic_not_encoded['test']['SibSp'] = titanic_not_encoded['test']['SibSp'].astype('object')\n", "titanic_not_encoded['test']['Parch'] = titanic_not_encoded['test']['Parch'].astype('object')\n", "\n", "normalized_titanic = nl.Normalizer(titanic_not_encoded.copy(),strategy='DS',exclude='Age', verbose = False).transform()\n", "normalized_titanic['train'].head()\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Replace missing values"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Number of missing values 866\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "Most frequent value for  Age is: 24.0\n", "Most frequent value for  Fare is: 0.4444444444444444\n", "Most frequent value for  Cabin is: C23 C25 C27\n", "Most frequent value for  Embarked is: S\n", "Most frequent value for  Name is: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON><PERSON><PERSON>\n", "Most frequent value for  Parch is: 0.0\n", "Most frequent value for  PassengerId is: 891.0\n", "Most frequent value for  Pclass is: 3.0\n", "Most frequent value for  Sex is: male\n", "Most frequent value for  SibSp is: 0.0\n", "Most frequent value for  Ticket is: 347082\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Fare', 'Cabin']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "Most frequent value for  Age is: 24.0\n", "Most frequent value for  Fare is: 0.6330980722637733\n", "Most frequent value for  Cabin is: B57 B59 B63 B66\n", "Most frequent value for  Embarked is: S\n", "Most frequent value for  Name is: <PERSON><PERSON>, Mr. <PERSON><PERSON><PERSON>\n", "Most frequent value for  Parch is: 0.0\n", "Most frequent value for  PassengerId is: 1023.0\n", "Most frequent value for  Pclass is: 3.0\n", "Most frequent value for  Sex is: male\n", "Most frequent value for  SibSp is: 0.0\n", "Most frequent value for  Ticket is: PC 17608\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "Imputation done -- CPU time: 0.11125493049621582 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Fare', 'Cabin']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "Imputation done -- CPU time: 0.07637977600097656 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "Dataset size reduced from 891 to 183\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Fare', 'Cabin']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "Dataset size reduced from 418 to 87\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "Imputation done -- CPU time: 0.06551194190979004 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Fare', 'Cabin']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.08994817733764648 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Using TensorFlow backend.\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Fare', 'Cabin']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 2.363224983215332 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Fare', 'Cabin']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.07814908027648926 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Fare', 'Cabin']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.19629192352294922 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Fare', 'Cabin']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.08721804618835449 seconds\n", "\n"]}], "source": ["#>> Examples for missing value imputation\n", "# Available strategies:\n", "#            - 'EM': only for numerical variables; imputation based on\n", "#                expectation maximization\n", "#            - 'MICE': only for numerical variables  missing at random (MAR);\n", "#                Multivariate Imputation by Chained Equations\n", "#            - 'KNN', only for numerical variables; k-nearest neighbor\n", "#                imputation (k=4) which weights samples using the mean squared\n", "#                difference on features for which two rows both have observed\n", "#                data\n", "#            - 'RAND', 'MF': both for numerical and categorical variables;\n", "#                replace missing values by randomly selected value in the \n", "#                variable domain or by the most frequent value in the variable\n", "#                domain respectively\n", "#            - 'MEAN', 'MEDIAN': only for numerical variables; replace missing\n", "#                values by mean or median of the numerical variable respectvely\n", "#            - or 'DROP' remove the row with at least one missing value\n", "\n", "import learn2clean.imputation.imputer as imp\n", "\n", "# replace missing values by the most frequent ones in the training and testing datasets\n", "\n", "print('Number of missing values',normalized_titanic['train'].isnull().sum().sum()) \n", "imp1 = imp.Imputer(normalized_titanic.copy(),strategy='MF', verbose=True).transform()\n", "imp2 = imp.Imputer(normalized_titanic.copy(),strategy='RAND', verbose=True).transform()\n", "imp3 = imp.Imputer(normalized_titanic.copy(),strategy='DROP', verbose=True).transform()\n", "imp4 = imp.Imputer(normalized_titanic.copy(),strategy='MEAN', verbose=True).transform() # for Age only numerical variable with missing values\n", "imp5 = imp.Imputer(normalized_titanic.copy(),strategy='KNN', verbose=True).transform()\n", "imp6 = imp.Imputer(normalized_titanic.copy(),strategy='MEDIAN', verbose=True).transform() # for Age only numerical variable with missing values\n", "imp6 = imp.Imputer(normalized_titanic.copy(),strategy='EM', verbose=True).transform()\n", "imp6 = imp.Imputer(normalized_titanic.copy(),strategy='MICE', verbose=True).transform()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Detect outliers and remove them"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "232 outlying rows have been removed:\n", "with indexes: [1, 3, 6, 13, 23, 25, 27, 31, 34, 35, 43, 50, 52, 54, 55, 59, 61, 62, 71, 72, 74, 83, 86, 88, 92, 96, 97, 102, 110, 118, 119, 120, 124, 137, 139, 145, 147, 151, 155, 159, 164, 166, 169, 170, 174, 180, 182, 183, 185, 195, 201, 209, 215, 218, 224, 230, 233, 245, 248, 256, 257, 258, 261, 262, 266, 268, 269, 270, 275, 290, 291, 297, 298, 299, 305, 306, 307, 309, 310, 311, 318, 319, 324, 325, 329, 332, 334, 336, 337, 339, 341, 351, 356, 366, 369, 370, 373, 375, 377, 380, 383, 385, 386, 390, 393, 412, 416, 434, 435, 436, 438, 445, 449, 453, 457, 462, 475, 480, 484, 486, 492, 493, 496, 498, 504, 505, 509, 513, 515, 520, 523, 527, 537, 539, 540, 541, 542, 544, 549, 550, 556, 557, 558, 571, 577, 581, 583, 585, 587, 591, 596, 599, 602, 607, 608, 609, 610, 615, 618, 621, 625, 627, 632, 638, 641, 643, 645, 647, 655, 659, 660, 665, 669, 670, 671, 678, 679, 681, 683, 684, 685, 686, 689, 690, 692, 698, 700, 708, 710, 712, 716, 720, 724, 730, 736, 737, 741, 742, 745, 748, 754, 759, 763, 765, 766, 779, 781, 789, 792, 793, 802, 809, 813, 817, 820, 824, 826, 827, 829, 835, 838, 842, 846, 848, 849, 850, 853, 856, 863, 867, 871, 879]\n", "\n", "Outliers:\n", "      Age        Cabin Embarked      Fare  \\\n", "1    38.0          C85        C   71.2833   \n", "3    35.0         C123        S   53.1000   \n", "6    54.0          E46        S   51.8625   \n", "13   39.0          NaN        S   31.2750   \n", "23   28.0           A6        S   35.5000   \n", "25   38.0          NaN        S   31.3875   \n", "27   19.0  C23 C25 C27        S  263.0000   \n", "31    NaN          B78        C  146.5208   \n", "34   28.0          NaN        C   82.1708   \n", "35   42.0          NaN        S   52.0000   \n", "43    3.0          NaN        C   41.5792   \n", "50    7.0          NaN        S   39.6875   \n", "52   49.0          D33        C   76.7292   \n", "54   65.0          B30        C   61.9792   \n", "55    NaN          C52        S   35.5000   \n", "59   11.0          NaN        S   46.9000   \n", "61   38.0          B28      NaN   80.0000   \n", "62   45.0          C83        S   83.4750   \n", "71   16.0          NaN        S   46.9000   \n", "72   21.0          NaN        S   73.5000   \n", "74   32.0          NaN        S   56.4958   \n", "83   28.0          NaN        S   47.1000   \n", "86   16.0          NaN        S   34.3750   \n", "88   23.0  C23 C25 C27        S  263.0000   \n", "92   46.0          E31        S   61.1750   \n", "96   71.0           A5        C   34.6542   \n", "97   23.0      D10 D12        C   63.3583   \n", "102  21.0          D26        S   77.2875   \n", "110  47.0         C110        S   52.0000   \n", "118  24.0      B58 B60        C  247.5208   \n", "..    ...          ...      ...       ...   \n", "763  36.0      B96 B98        S  120.0000   \n", "765  51.0          D11        S   77.9583   \n", "766   NaN          NaN        C   39.6000   \n", "779  43.0           B3        S  211.3375   \n", "781  17.0          B20        S   57.0000   \n", "789  46.0      B82 B84        C   79.2000   \n", "792   NaN          NaN        S   69.5500   \n", "793   NaN          NaN        C   30.6958   \n", "802  11.0      B96 B98        S  120.0000   \n", "809  33.0           E8        S   53.1000   \n", "813   6.0          NaN        S   31.2750   \n", "817  31.0          NaN        C   37.0042   \n", "820  52.0          B69        S   93.5000   \n", "824   2.0          NaN        S   39.6875   \n", "826   NaN          NaN        S   56.4958   \n", "827   1.0          NaN        C   37.0042   \n", "829  62.0          B28      NaN   80.0000   \n", "835  39.0          E49        C   83.1583   \n", "838  32.0          NaN        S   56.4958   \n", "842  30.0          NaN        C   31.0000   \n", "846   NaN          NaN        S   69.5500   \n", "848  28.0          NaN        S   33.0000   \n", "849   NaN          C92        C   89.1042   \n", "850   4.0          NaN        S   31.2750   \n", "853  16.0          D28        S   39.4000   \n", "856  45.0          NaN        S  164.8667   \n", "863   NaN          NaN        S   69.5500   \n", "867  31.0          A24        S   50.4958   \n", "871  47.0          D35        S   52.5542   \n", "879  56.0          C50        C   83.1583   \n", "\n", "                                                          Name Parch  \\\n", "1          <PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", "3                 <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", "6                                      <PERSON>, <PERSON>. <PERSON>     0   \n", "13                                 <PERSON><PERSON>, <PERSON>. <PERSON>     5   \n", "23                                <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", "25   <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     5   \n", "27                              <PERSON>, Mr. <PERSON>     2   \n", "31              <PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", "34                                     <PERSON>, Mr. <PERSON>     0   \n", "35                              <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", "43                    <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>     2   \n", "50                                  <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>     1   \n", "52                    <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     0   \n", "54                              <PERSON><PERSON><PERSON>, Mr. <PERSON><PERSON><PERSON>     1   \n", "55                                           <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", "59                          <PERSON>, <PERSON><PERSON>     2   \n", "61                                         <PERSON><PERSON>, <PERSON><PERSON>     0   \n", "62                                 <PERSON>, Mr. <PERSON>     0   \n", "71                                  <PERSON>, <PERSON><PERSON>     2   \n", "72                                        <PERSON>, <PERSON><PERSON>     0   \n", "74                                               <PERSON>, <PERSON><PERSON>     0   \n", "83                                     <PERSON><PERSON>, <PERSON><PERSON>     0   \n", "86                                      <PERSON>, Mr. <PERSON>     3   \n", "88                                  <PERSON>, <PERSON><PERSON>     2   \n", "92                                 <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", "96                                   <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", "97                             <PERSON><PERSON>, Mr. <PERSON>     1   \n", "102                                  <PERSON>, Mr. <PERSON>     1   \n", "110                             <PERSON>, <PERSON>. <PERSON>     0   \n", "118                                   <PERSON>, Mr. <PERSON><PERSON><PERSON>     1   \n", "..                                                         ...   ...   \n", "763                  <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     2   \n", "765                       <PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", "766                                  <PERSON><PERSON><PERSON>, <PERSON>. <PERSON>     0   \n", "779      <PERSON>, Mrs. <PERSON> (<PERSON>)     1   \n", "781                  <PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", "789                                   <PERSON>, Mr. <PERSON>     0   \n", "792                                    <PERSON>, <PERSON><PERSON>     2   \n", "793                                   <PERSON><PERSON>, Mr. <PERSON>     0   \n", "802                        <PERSON>, <PERSON><PERSON> II     2   \n", "809             <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     0   \n", "813                         <PERSON><PERSON>, <PERSON><PERSON><PERSON>     2   \n", "817                                         <PERSON><PERSON>, Mr. <PERSON>     1   \n", "820         <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     1   \n", "824                               <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>     1   \n", "826                                               <PERSON>, <PERSON>. <PERSON>     0   \n", "827                                      <PERSON><PERSON>, <PERSON><PERSON>     2   \n", "829                  <PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", "835                                <PERSON>, <PERSON><PERSON>     1   \n", "838                                            <PERSON>, <PERSON><PERSON>     0   \n", "842                                    <PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", "846                                   <PERSON>, Mr. <PERSON>     2   \n", "848                                          <PERSON>, <PERSON><PERSON>     1   \n", "849               <PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON><PERSON>)     0   \n", "850                    <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>     2   \n", "853                                  <PERSON>, <PERSON><PERSON>     1   \n", "856                 <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     1   \n", "863                          <PERSON>, <PERSON><PERSON> \"<PERSON>\"     2   \n", "867                       <PERSON><PERSON>, Mr. <PERSON>     0   \n", "871           <PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     1   \n", "879              <PERSON>, Mrs. <PERSON> (<PERSON>)     1   \n", "\n", "    PassengerId Pclass     Sex SibSp           Ticket  \n", "1             2      1  female     1         PC 17599  \n", "3             4      1  female     1           113803  \n", "6             7      1    male     0            17463  \n", "13           14      3    male     1           347082  \n", "23           24      1    male     0           113788  \n", "25           26      3  female     1           347077  \n", "27           28      1    male     3            19950  \n", "31           32      1  female     1         PC 17569  \n", "34           35      1    male     1         PC 17604  \n", "35           36      1    male     1           113789  \n", "43           44      2  female     1    SC/Paris 2123  \n", "50           51      3    male     4          3101295  \n", "52           53      1  female     1         PC 17572  \n", "54           55      1    male     0           113509  \n", "55           56      1    male     0            19947  \n", "59           60      3    male     5          CA 2144  \n", "61           62      1  female     0           113572  \n", "62           63      1    male     1            36973  \n", "71           72      3  female     5          CA 2144  \n", "72           73      2    male     0     S.O.C. 14879  \n", "74           75      3    male     0             1601  \n", "83           84      1    male     0           113059  \n", "86           87      3    male     1       W./C. 6608  \n", "88           89      1  female     3            19950  \n", "92           93      1    male     1      W.E.P. 5734  \n", "96           97      1    male     0         PC 17754  \n", "97           98      1    male     0         PC 17759  \n", "102         103      1    male     0            35281  \n", "110         111      1    male     0           110465  \n", "118         119      1    male     0         PC 17558  \n", "..          ...    ...     ...   ...              ...  \n", "763         764      1  female     1           113760  \n", "765         766      1  female     1            13502  \n", "766         767      1    male     0           112379  \n", "779         780      1  female     0            24160  \n", "781         782      1  female     1            17474  \n", "789         790      1    male     0         PC 17593  \n", "792         793      3  female     8         CA. 2343  \n", "793         794      1    male     0         PC 17600  \n", "802         803      1    male     1           113760  \n", "809         810      1  female     1           113806  \n", "813         814      3  female     4           347082  \n", "817         818      2    male     1  S.C./PARIS 2079  \n", "820         821      1  female     1            12749  \n", "824         825      3    male     4          3101295  \n", "826         827      3    male     0             1601  \n", "827         828      2    male     0  S.C./PARIS 2079  \n", "829         830      1  female     0           113572  \n", "835         836      1  female     1         PC 17756  \n", "838         839      3    male     0             1601  \n", "842         843      1  female     0           113798  \n", "846         847      3    male     8         CA. 2343  \n", "848         849      2    male     0           248727  \n", "849         850      1  female     1            17453  \n", "850         851      3    male     4           347082  \n", "853         854      1  female     0         PC 17592  \n", "856         857      1  female     1            36928  \n", "863         864      3  female     8         CA. 2343  \n", "867         868      1    male     0         PC 17590  \n", "871         872      1  female     1            11751  \n", "879         880      1  female     0            11767  \n", "\n", "[232 rows x 11 columns]\n", "\n", "* For test dataset\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0 outlying rows have been removed:\n", "Outlier detection and removal done -- CPU time: 0.04443717002868652 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "891 outlying rows have been removed\n", "* For test dataset\n", "418 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.030585050582885742 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing valueshave been removed using DROP.\n", "40 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing valueshave been removed using DROP.\n", "40 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.020792007446289062 seconds\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n"]}, {"data": {"text/plain": ["{'train':       <PERSON> Embarked  \\\n", " 1    38.0   71.2833              C85        C   \n", " 6    54.0   51.8625              E46        S   \n", " 10    4.0   16.7000               G6        S   \n", " 11   58.0   26.5500             C103        S   \n", " 21   34.0   13.0000              D56        S   \n", " 23   28.0   35.5000               A6        S   \n", " 27   19.0  263.0000      C23 C25 C27        S   \n", " 52   49.0   76.7292              D33        C   \n", " 54   65.0   61.9792              B30        C   \n", " 62   45.0   83.4750              C83        S   \n", " 75   25.0    7.6500            F G73        S   \n", " 88   23.0  263.0000      C23 C25 C27        S   \n", " 92   46.0   61.1750              E31        S   \n", " 96   71.0   34.6542               A5        C   \n", " 97   23.0   63.3583          D10 D12        C   \n", " 110  47.0   52.0000             C110        S   \n", " 118  24.0  247.5208          B58 B60        C   \n", " 136  19.0   26.2833              D47        S   \n", " 137  37.0   53.1000             C123        S   \n", " 139  24.0   79.2000              B86        C   \n", " 151  22.0   66.6000               C2        S   \n", " 170  61.0   33.5000              B19        S   \n", " 177  50.0   28.7125              C49        C   \n", " 183   1.0   39.0000               F4        S   \n", " 194  44.0   27.7208               B4        C   \n", " 195  58.0  146.5208              B80        C   \n", " 205   2.0   10.4625               G6        S   \n", " 209  40.0   31.0000              A31        C   \n", " 215  31.0  113.2750              D36        C   \n", " 218  32.0   76.2917              D15        C   \n", " ..    ...       ...              ...      ...   \n", " 715  19.0    7.6500            F G73        S   \n", " 717  27.0   10.5000             E101        S   \n", " 724  27.0   53.1000               E8        S   \n", " 730  29.0  211.3375               B5        S   \n", " 737  35.0  512.3292             B101        C   \n", " 741  36.0   78.8500              C46        S   \n", " 742  21.0  262.3750  B57 B59 B63 B66        C   \n", " 745  70.0   71.0000              B22        S   \n", " 748  19.0   53.1000              D30        S   \n", " 751   6.0   12.4750             E121        S   \n", " 759  33.0   86.5000              B77        S   \n", " 763  36.0  120.0000          B96 B98        S   \n", " 772  57.0   10.5000              E77        S   \n", " 781  17.0   57.0000              B20        S   \n", " 782  29.0   30.0000               D6        S   \n", " 789  46.0   79.2000          B82 B84        C   \n", " 796  49.0   25.9292              D17        S   \n", " 802  11.0  120.0000          B96 B98        S   \n", " 806  39.0    0.0000              A36        S   \n", " 809  33.0   53.1000               E8        S   \n", " 820  52.0   93.5000              B69        S   \n", " 835  39.0   83.1583              E49        C   \n", " 853  16.0   39.4000              D28        S   \n", " 857  51.0   26.5500              E17        S   \n", " 867  31.0   50.4958              A24        S   \n", " 871  47.0   52.5542              D35        S   \n", " 872  33.0    5.0000      B51 B53 B55        S   \n", " 879  56.0   83.1583              C50        C   \n", " 887  19.0   30.0000              B42        S   \n", " 889  26.0   30.0000             C148        C   \n", " \n", "                                                          Name Parch  \\\n", " 1         <PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 6                                     <PERSON>, <PERSON>. <PERSON>     0   \n", " 10                            <PERSON><PERSON>, <PERSON><PERSON>     1   \n", " 11                                   <PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 21                                      <PERSON><PERSON>, <PERSON>. <PERSON>     0   \n", " 23                               <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 27                             <PERSON>, Mr. <PERSON>     2   \n", " 52                   <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     0   \n", " 54                             <PERSON><PERSON><PERSON>, Mr. <PERSON><PERSON><PERSON>     1   \n", " 62                                <PERSON>, Mr. <PERSON>     0   \n", " 75                                    <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>     0   \n", " 88                                 <PERSON>, <PERSON><PERSON>     2   \n", " 92                                <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 96                                  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 97                            <PERSON><PERSON>, Mr. <PERSON>     1   \n", " 110                            <PERSON>, <PERSON>. <PERSON>     0   \n", " 118                                  <PERSON>, Mr. <PERSON><PERSON><PERSON>     1   \n", " 136                              <PERSON><PERSON>, <PERSON><PERSON>     2   \n", " 137                               <PERSON><PERSON><PERSON>, <PERSON>. <PERSON>     0   \n", " 139                                        <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 151                         P<PERSON>s, Mrs. <PERSON> (<PERSON>)     0   \n", " 170                                 <PERSON>, <PERSON><PERSON>     0   \n", " 177                                <PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 183                                 <PERSON>, <PERSON><PERSON>     1   \n", " 194                 <PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 195                                      <PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 205                                <PERSON><PERSON>, <PERSON><PERSON> Tel<PERSON>     1   \n", " 209                                          <PERSON><PERSON>, <PERSON>. <PERSON>     0   \n", " 215                                   <PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 218                                     <PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " ..                                                        ...   ...   \n", " 715                <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 717                       <PERSON><PERSON><PERSON>, <PERSON><PERSON> \"<PERSON><PERSON>\"     0   \n", " 724                             <PERSON>, Mr. <PERSON>     0   \n", " 730                             <PERSON>, <PERSON><PERSON>     0   \n", " 737                                    <PERSON><PERSON>, Mr. <PERSON><PERSON>     0   \n", " 741                             <PERSON>, Mr. <PERSON><PERSON>     0   \n", " 742                     <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON> \"<PERSON><PERSON><PERSON>\"     2   \n", " 745                              <PERSON>, Capt. <PERSON>     1   \n", " 748                                 <PERSON>, <PERSON>. <PERSON>     0   \n", " 751                                       <PERSON>, <PERSON><PERSON>     1   \n", " 759  <PERSON><PERSON>, the <PERSON>. of (<PERSON>-<PERSON>)     0   \n", " 763                 <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     2   \n", " 772                                         <PERSON>, Mrs. (<PERSON>)     0   \n", " 781                 <PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 782                                    <PERSON>, Mr. <PERSON>     0   \n", " 789                                  <PERSON>, Mr. <PERSON>     0   \n", " 796                               Leader, Dr. <PERSON> (Farnham)     0   \n", " 802                       <PERSON>, <PERSON><PERSON> II     2   \n", " 806                                    <PERSON>, Mr. <PERSON>     0   \n", " 809            <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     0   \n", " 820        <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     1   \n", " 835                               <PERSON>, <PERSON><PERSON>     1   \n", " 853                                 <PERSON>, <PERSON><PERSON>     1   \n", " 857                                    <PERSON>, Mr. <PERSON>      0   \n", " 867                      <PERSON><PERSON>, Mr. <PERSON>     0   \n", " 871          <PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     1   \n", " 872                                  <PERSON><PERSON>, <PERSON>. <PERSON><PERSON>     0   \n", " 879             <PERSON>, Mrs. <PERSON> (<PERSON>)     1   \n", " 887                              <PERSON>, <PERSON><PERSON>     0   \n", " 889                                     <PERSON><PERSON>, <PERSON>. <PERSON>     0   \n", " \n", "     PassengerId Pclass     Sex SibSp       Ticket  \n", " 1             2      1  female     1     PC 17599  \n", " 6             7      1    male     0        17463  \n", " 10           11      3  female     1      PP 9549  \n", " 11           12      1  female     0       113783  \n", " 21           22      2    male     0       248698  \n", " 23           24      1    male     0       113788  \n", " 27           28      1    male     3        19950  \n", " 52           53      1  female     1     PC 17572  \n", " 54           55      1    male     0       113509  \n", " 62           63      1    male     1        36973  \n", " 75           76      3    male     0       348123  \n", " 88           89      1  female     3        19950  \n", " 92           93      1    male     1  W.E.P. 5734  \n", " 96           97      1    male     0     PC 17754  \n", " 97           98      1    male     0     PC 17759  \n", " 110         111      1    male     0       110465  \n", " 118         119      1    male     0     PC 17558  \n", " 136         137      1  female     0        11752  \n", " 137         138      1    male     1       113803  \n", " 139         140      1    male     0     PC 17593  \n", " 151         152      1  female     1       113776  \n", " 170         171      1    male     0       111240  \n", " 177         178      1  female     0     PC 17595  \n", " 183         184      2    male     2       230136  \n", " 194         195      1  female     0     PC 17610  \n", " 195         196      1  female     0     PC 17569  \n", " 205         206      3  female     0       347054  \n", " 209         210      1    male     0       112277  \n", " 215         216      1  female     1        35273  \n", " 218         219      1  female     0        11813  \n", " ..          ...    ...     ...   ...          ...  \n", " 715         716      3    male     0       348124  \n", " 717         718      2  female     0        34218  \n", " 724         725      1    male     1       113806  \n", " 730         731      1  female     0        24160  \n", " 737         738      1    male     0     PC 17755  \n", " 741         742      1    male     1        19877  \n", " 742         743      1  female     2     PC 17608  \n", " 745         746      1    male     1    WE/P 5735  \n", " 748         749      1    male     1       113773  \n", " 751         752      3    male     0       392096  \n", " 759         760      1  female     0       110152  \n", " 763         764      1  female     1       113760  \n", " 772         773      2  female     0  S.O./P.P. 3  \n", " 781         782      1  female     1        17474  \n", " 782         783      1    male     0       113501  \n", " 789         790      1    male     0     PC 17593  \n", " 796         797      1  female     0        17465  \n", " 802         803      1    male     1       113760  \n", " 806         807      1    male     0       112050  \n", " 809         810      1  female     1       113806  \n", " 820         821      1  female     1        12749  \n", " 835         836      1  female     1     PC 17756  \n", " 853         854      1  female     0     PC 17592  \n", " 857         858      1    male     0       113055  \n", " 867         868      1    male     0     PC 17590  \n", " 871         872      1  female     1        11751  \n", " 872         873      1    male     0          695  \n", " 879         880      1  female     0        11767  \n", " 887         888      1  female     0       112053  \n", " 889         890      1    male     0       111369  \n", " \n", " [143 rows x 11 columns],\n", " 'test':       <PERSON>      <PERSON> Embarked  \\\n", " 34   30.0   57.7500              C78        C   \n", " 68   31.0   28.5375              C53        C   \n", " 69   60.0  263.0000      C23 C25 C27        S   \n", " 73   28.5   27.7208              D43        C   \n", " 74   35.0  211.5000             C130        C   \n", " 75   32.5  211.5000             C132        C   \n", " 77   55.0   25.7000             C101        S   \n", " 81   67.0  221.7792          C55 C57        S   \n", " 96   76.0   78.8500              C46        S   \n", " 114  63.0  221.7792          C55 C57        S   \n", " 117   1.0   16.7000               G6        S   \n", " 131  53.0   28.5000              C51        C   \n", " 142  61.0  262.3750  B57 B59 B63 B66        C   \n", " 156  29.0  221.7792              C97        S   \n", " 177  54.0   55.4417             C116        C   \n", " 178  36.0   39.0000               F4        S   \n", " 181  37.0   83.1583              E52        C   \n", " 182  18.0   53.1000              D30        S   \n", " 196   6.0  134.5000              E34        C   \n", " 202  47.0  227.5250          C62 C64        C   \n", " 208  33.0   27.7208              A11        C   \n", " 215  42.0   42.5000              B11        S   \n", " 222  22.0   10.5000              F33        S   \n", " 234  39.0   71.2833              C85        C   \n", " 236  64.0   75.2500              D37        C   \n", " 239  48.0  106.4250              C86        C   \n", " 242  45.0  134.5000              E34        C   \n", " 270  46.0   75.2417               C6        C   \n", " 305  64.0   26.5500              B26        S   \n", " 306  30.0  151.5500          C22 C26        S   \n", " 308  55.0   93.5000              B69        S   \n", " 314  55.0  135.6333              C32        C   \n", " 316  57.0  146.5208              B78        C   \n", " 322  26.0   13.0000               F2        S   \n", " 326  12.0   39.0000               F4        S   \n", " 335  30.0   26.0000             C106        S   \n", " 343  58.0  512.3292      B51 B53 B55        C   \n", " 350  45.0   63.3583          D10 D12        C   \n", " 355  50.0   26.0000              E60        S   \n", " 356  59.0   51.4792             C101        S   \n", " 372  49.0    0.0000      B52 B54 B56        S   \n", " 378  55.0   50.0000              C39        S   \n", " 391  51.0   39.4000              D28        S   \n", " 395  18.0   60.0000              C31        S   \n", " 400  30.0  164.8667               C7        S   \n", " 411  37.0   90.0000              C78        Q   \n", " 414  39.0  108.9000             C105        C   \n", " \n", "                                                                 Name Parch  \\\n", " 34                                          <PERSON><PERSON>, Mr. <PERSON>     0   \n", " 68                                   <PERSON>, Mr. <PERSON>     0   \n", " 69                               <PERSON>, Mrs. <PERSON> (<PERSON>)     4   \n", " 73                                   <PERSON><PERSON> y <PERSON>, <PERSON><PERSON>     0   \n", " 74                                              <PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 75                                                <PERSON>, Mr. <PERSON>     0   \n", " 77              <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     0   \n", " 81                                                <PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 96            <PERSON>, Mrs. <PERSON><PERSON> (<PERSON>)     0   \n", " 114                           <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     0   \n", " 117                                  <PERSON><PERSON>, <PERSON><PERSON>     1   \n", " 131                                        <PERSON>, <PERSON><PERSON> IV     0   \n", " 142                                       <PERSON><PERSON><PERSON>, Mr. <PERSON>     3   \n", " 156                                                <PERSON>, <PERSON><PERSON>     0   \n", " 177                                  <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 178                 <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     3   \n", " 181                                 <PERSON>, Mr. <PERSON> Jr     1   \n", " 182   <PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 196                                  <PERSON><PERSON><PERSON>, <PERSON><PERSON>     2   \n", " 202                                           <PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 208                                    <PERSON>, <PERSON><PERSON>     0   \n", " 215                                            <PERSON>, Mr. <PERSON>     0   \n", " 222                                       <PERSON>, Mrs. (<PERSON>)     0   \n", " 234                                        <PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 236                                         <PERSON>, Mr. <PERSON>     0   \n", " 239                      <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     0   \n", " 242                                     <PERSON><PERSON><PERSON>, Mr. <PERSON>     1   \n", " 270                                     <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 305       <PERSON>, Mrs. <PERSON> (<PERSON>)     1   \n", " 306                             <PERSON>, Mr. <PERSON>     2   \n", " 308                                       <PERSON><PERSON>, Mr. <PERSON>     1   \n", " 314                            <PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 316                                    <PERSON>, Mr. <PERSON>     0   \n", " 322                                               <PERSON><PERSON><PERSON>, <PERSON>. Israel     0   \n", " 326                                     <PERSON>, <PERSON><PERSON>     1   \n", " 335                                         <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 343  <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     1   \n", " 350                     <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     1   \n", " 355                                         <PERSON>, Mr. <PERSON>     0   \n", " 356                   <PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 372                                          <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 378                                      <PERSON><PERSON>, Mr. <PERSON>     0   \n", " 391                   <PERSON>, Mrs. <PERSON> (<PERSON>)     1   \n", " 395                   <PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 400                                          <PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 411                  <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 414                                     <PERSON><PERSON>, Dona. Fermina     0   \n", " \n", "     PassengerId Pclass     Sex SibSp       Ticket  \n", " 34          926      1    male     1        13236  \n", " 68          960      1    male     0         2543  \n", " 69          961      1  female     1        19950  \n", " 73          965      1    male     0     PC 17562  \n", " 74          966      1  female     0       113503  \n", " 75          967      1    male     0       113503  \n", " 77          969      1  female     2        11770  \n", " 81          973      1    male     1     PC 17483  \n", " 96          988      1  female     1        19877  \n", " 114        1006      1  female     1     PC 17483  \n", " 117        1009      3  female     1      PP 9549  \n", " 131        1023      1    male     0       113780  \n", " 142        1034      1    male     1     PC 17608  \n", " 156        1048      1  female     0     PC 17483  \n", " 177        1069      1    male     1        11778  \n", " 178        1070      2  female     0       230136  \n", " 181        1073      1    male     1     PC 17756  \n", " 182        1074      1  female     1       113773  \n", " 196        1088      1    male     0        16966  \n", " 202        1094      1    male     1     PC 17757  \n", " 208        1100      1  female     0     PC 17613  \n", " 215        1107      1    male     0       113038  \n", " 222        1114      2  female     0  W./C. 14266  \n", " 234        1126      1    male     1     PC 17599  \n", " 236        1128      1    male     1       110813  \n", " 239        1131      1  female     1     PC 17761  \n", " 242        1134      1    male     1        16966  \n", " 270        1162      1    male     0        13050  \n", " 305        1197      1  female     1       112901  \n", " 306        1198      1    male     1       113781  \n", " 308        1200      1    male     1        12749  \n", " 314        1206      1  female     0     PC 17760  \n", " 316        1208      1    male     1     PC 17569  \n", " 322        1214      2    male     0       244368  \n", " 326        1218      2  female     2       230136  \n", " 335        1227      1    male     0       110469  \n", " 343        1235      1  female     0     PC 17755  \n", " 350        1242      1  female     0     PC 17759  \n", " 355        1247      1    male     0       113044  \n", " 356        1248      1  female     2        11769  \n", " 372        1264      1    male     0       112058  \n", " 378        1270      1    male     0          680  \n", " 391        1283      1  female     0     PC 17592  \n", " 395        1287      1  female     1        13695  \n", " 400        1292      1  female     0        36928  \n", " 411        1303      1  female     1        19928  \n", " 414        1306      1  female     0     PC 17758  ,\n", " 'target': 0      0.0\n", " 1      1.0\n", " 2      1.0\n", " 3      1.0\n", " 4      0.0\n", " 5      0.0\n", " 6      0.0\n", " 7      0.0\n", " 8      1.0\n", " 9      1.0\n", " 10     1.0\n", " 11     1.0\n", " 12     0.0\n", " 13     0.0\n", " 14     0.0\n", " 15     1.0\n", " 16     0.0\n", " 17     1.0\n", " 18     0.0\n", " 19     1.0\n", " 20     0.0\n", " 21     1.0\n", " 22     1.0\n", " 23     1.0\n", " 24     0.0\n", " 25     1.0\n", " 26     0.0\n", " 27     0.0\n", " 28     1.0\n", " 29     0.0\n", "       ... \n", " 861    0.0\n", " 862    1.0\n", " 863    0.0\n", " 864    0.0\n", " 865    1.0\n", " 866    1.0\n", " 867    0.0\n", " 868    0.0\n", " 869    1.0\n", " 870    0.0\n", " 871    1.0\n", " 872    0.0\n", " 873    0.0\n", " 874    1.0\n", " 875    1.0\n", " 876    0.0\n", " 877    0.0\n", " 878    0.0\n", " 879    1.0\n", " 880    1.0\n", " 881    0.0\n", " 882    0.0\n", " 883    0.0\n", " 884    0.0\n", " 885    0.0\n", " 886    0.0\n", " 887    1.0\n", " 888    0.0\n", " 889    1.0\n", " 890    0.0\n", " Name: Survived, Length: 891, dtype: float64,\n", " 'target_test': Series([], Name: Survived, dtype: float64)}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# >> Examples for outlier detection and removal\n", "# Available strategies =\n", "#            'ZS': detects outliers using the robust Zscore as a function\n", "#            of median and median absolute deviation (MAD)\n", "#            'IQR': detects outliers using Q1 and Q3 +/- 1.5*InterQuartile Range\n", "#            'LOF': detects outliers using Local Outlier Factor\n", "\n", "                \n", "import learn2clean.outlier_detection.outlier_detector as out\n", "\n", "#to remove rows having 30% and more ZSB-based outling values among the numerical variables\n", "out1=out.Outlier_detector(titanic_not_encoded.copy(), strategy='ZSB', threshold = .3, verbose=True)\n", "out1.transform()\n", "\n", "#to remove rows having at least one IQR-based outlying value using threshold '-1'\n", "out2=out.Outlier_detector(titanic_not_encoded.copy(), strategy='IQR', threshold = -1, verbose=False)\n", "out2.transform()\n", "\n", "#to remove rows having 40% and more ZSB-based outling values among the numerical variables; \n", "# since LOF requires non missing values, rows with NaN are also removed\n", "out3=out.Outlier_detector(titanic_not_encoded.copy(), strategy='LOF', threshold = .4, verbose=False)\n", "out3.transform()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Detect duplicates and remove them"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "To detect duplicates, rows with missing values are removed using DROP\n", "Total number of rows: 891\n", "Number of duplicated rows: 0\n", "After deduplication: Number of rows: 891\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "To detect duplicates, rows with missing values are removed using DROP\n", "Total number of rows: 418\n", "Number of duplicated rows: 0\n", "After deduplication: Number of rows: 418\n", "Deduplication done -- CPU time: 0.028828859329223633 seconds\n", "\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "Number of duplicate rows removed: 0\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "Number of duplicate rows removed: 0\n", "Deduplication done -- CPU time: 0.13759875297546387 seconds\n", "\n"]}, {"data": {"text/plain": ["{'train':       <PERSON> Embarked      Far<PERSON>  \\\n", " 0    22.0          NaN        S    7.2500   \n", " 1    38.0          C85        C   71.2833   \n", " 2    26.0          NaN        S    7.9250   \n", " 3    35.0         C123        S   53.1000   \n", " 4    35.0          NaN        S    8.0500   \n", " 5     NaN          NaN        Q    8.4583   \n", " 6    54.0          E46        S   51.8625   \n", " 7     2.0          NaN        S   21.0750   \n", " 8    27.0          NaN        S   11.1333   \n", " 9    14.0          NaN        C   30.0708   \n", " 10    4.0           G6        S   16.7000   \n", " 11   58.0         C103        S   26.5500   \n", " 12   20.0          NaN        S    8.0500   \n", " 13   39.0          NaN        S   31.2750   \n", " 14   14.0          NaN        S    7.8542   \n", " 15   55.0          NaN        S   16.0000   \n", " 16    2.0          NaN        Q   29.1250   \n", " 17    NaN          NaN        S   13.0000   \n", " 18   31.0          NaN        S   18.0000   \n", " 19    NaN          NaN        C    7.2250   \n", " 20   35.0          NaN        S   26.0000   \n", " 21   34.0          D56        S   13.0000   \n", " 22   15.0          NaN        Q    8.0292   \n", " 23   28.0           A6        S   35.5000   \n", " 24    8.0          NaN        S   21.0750   \n", " 25   38.0          NaN        S   31.3875   \n", " 26    NaN          NaN        C    7.2250   \n", " 27   19.0  C23 C25 C27        S  263.0000   \n", " 28    NaN          NaN        Q    7.8792   \n", " 29    NaN          NaN        S    7.8958   \n", " ..    ...          ...      ...       ...   \n", " 861  21.0          NaN        S   11.5000   \n", " 862  48.0          D17        S   25.9292   \n", " 863   NaN          NaN        S   69.5500   \n", " 864  24.0          NaN        S   13.0000   \n", " 865  42.0          NaN        S   13.0000   \n", " 866  27.0          NaN        C   13.8583   \n", " 867  31.0          A24        S   50.4958   \n", " 868   NaN          NaN        S    9.5000   \n", " 869   4.0          NaN        S   11.1333   \n", " 870  26.0          NaN        S    7.8958   \n", " 871  47.0          D35        S   52.5542   \n", " 872  33.0  B51 B53 B55        S    5.0000   \n", " 873  47.0          NaN        S    9.0000   \n", " 874  28.0          NaN        C   24.0000   \n", " 875  15.0          NaN        C    7.2250   \n", " 876  20.0          NaN        S    9.8458   \n", " 877  19.0          NaN        S    7.8958   \n", " 878   NaN          NaN        S    7.8958   \n", " 879  56.0          C50        C   83.1583   \n", " 880  25.0          NaN        S   26.0000   \n", " 881  33.0          NaN        S    7.8958   \n", " 882  22.0          NaN        S   10.5167   \n", " 883  28.0          NaN        S   10.5000   \n", " 884  25.0          NaN        S    7.0500   \n", " 885  39.0          NaN        Q   29.1250   \n", " 886  27.0          NaN        S   13.0000   \n", " 887  19.0          B42        S   30.0000   \n", " 888   NaN          NaN        S   23.4500   \n", " 889  26.0         C148        C   30.0000   \n", " 890  32.0          NaN        Q    7.7500   \n", " \n", "                                                           Name Parch  \\\n", " 0                                      <PERSON><PERSON>, <PERSON><PERSON> <PERSON>     0   \n", " 1          <PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 2                                       <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 3                 <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 4                                     <PERSON>, Mr. <PERSON>     0   \n", " 5                                             <PERSON>, <PERSON><PERSON>     0   \n", " 6                                      <PERSON>, <PERSON>. <PERSON>     0   \n", " 7                               <PERSON><PERSON>, <PERSON><PERSON><PERSON>     1   \n", " 8            <PERSON>, Mrs. <PERSON> (<PERSON>)     2   \n", " 9                          <PERSON><PERSON>, Mrs<PERSON> (<PERSON>)     0   \n", " 10                             <PERSON><PERSON>, <PERSON><PERSON>     1   \n", " 11                                    <PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 12                              <PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 13                                 <PERSON><PERSON>, <PERSON>. <PERSON>     5   \n", " 14                        <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>     0   \n", " 15                            <PERSON><PERSON><PERSON>, Mrs. (<PERSON>)      0   \n", " 16                                        <PERSON>, <PERSON><PERSON>     1   \n", " 17                                <PERSON>, Mr. <PERSON>     0   \n", " 18     <PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON><PERSON>)     0   \n", " 19                                     <PERSON><PERSON><PERSON>, Mrs. <PERSON>     0   \n", " 20                                        <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 21                                       <PERSON><PERSON>, <PERSON>. <PERSON>     0   \n", " 22                                 <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> \"<PERSON>\"     0   \n", " 23                                <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 24                               <PERSON><PERSON>, <PERSON><PERSON>     1   \n", " 25   <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     5   \n", " 26                                     <PERSON><PERSON>, Mr. <PERSON><PERSON>     0   \n", " 27                              <PERSON>, Mr. <PERSON>     2   \n", " 28                               <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> \"<PERSON><PERSON>\"     0   \n", " 29                                         <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>     0   \n", " ..                                                         ...   ...   \n", " 861                                <PERSON>, Mr. <PERSON>     0   \n", " 862        <PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 863                          <PERSON>, <PERSON><PERSON> \"<PERSON>\"     2   \n", " 864                                     <PERSON>, Mr. <PERSON>     0   \n", " 865                                   <PERSON><PERSON>, Mrs. (<PERSON><PERSON><PERSON>)     0   \n", " 866                               <PERSON><PERSON>, Miss<PERSON> Asuncion     0   \n", " 867                       <PERSON><PERSON>, Mr. <PERSON>     0   \n", " 868                                <PERSON>, <PERSON><PERSON>     0   \n", " 869                            <PERSON>, <PERSON><PERSON>     1   \n", " 870                                          <PERSON><PERSON><PERSON>, Mr<PERSON>     0   \n", " 871           <PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     1   \n", " 872                                   <PERSON><PERSON>, <PERSON>. <PERSON><PERSON>     0   \n", " 873                                <PERSON><PERSON>, Mr. <PERSON>     0   \n", " 874                      <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 875                           <PERSON><PERSON><PERSON>, <PERSON><PERSON> \"<PERSON>\"     0   \n", " 876                              <PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 877                                       <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 878                                         <PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 879              <PERSON>, Mrs. <PERSON> (<PERSON>)     1   \n", " 880               <PERSON>, Mrs. <PERSON> (<PERSON><PERSON><PERSON>)     1   \n", " 881                                         <PERSON><PERSON>, <PERSON><PERSON> <PERSON>     0   \n", " 882                               <PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 883                              <PERSON><PERSON>, Mr. <PERSON>     0   \n", " 884                                     <PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 885                       <PERSON>, Mrs. <PERSON> (<PERSON>)     5   \n", " 886                                      <PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 887                               <PERSON>, <PERSON><PERSON>     0   \n", " 888                   <PERSON>, <PERSON><PERSON> \"<PERSON>\"     2   \n", " 889                                      <PERSON><PERSON>, <PERSON>. <PERSON>     0   \n", " 890                                        <PERSON><PERSON>, <PERSON>. <PERSON>     0   \n", " \n", "     PassengerId Pclass     Sex SibSp            Ticket  \n", " 0             1      3    male     1         A/5 21171  \n", " 1             2      1  female     1          PC 17599  \n", " 2             3      3  female     0  STON/O2. 3101282  \n", " 3             4      1  female     1            113803  \n", " 4             5      3    male     0            373450  \n", " 5             6      3    male     0            330877  \n", " 6             7      1    male     0             17463  \n", " 7             8      3    male     3            349909  \n", " 8             9      3  female     0            347742  \n", " 9            10      2  female     1            237736  \n", " 10           11      3  female     1           PP 9549  \n", " 11           12      1  female     0            113783  \n", " 12           13      3    male     0         A/5. 2151  \n", " 13           14      3    male     1            347082  \n", " 14           15      3  female     0            350406  \n", " 15           16      2  female     0            248706  \n", " 16           17      3    male     4            382652  \n", " 17           18      2    male     0            244373  \n", " 18           19      3  female     1            345763  \n", " 19           20      3  female     0              2649  \n", " 20           21      2    male     0            239865  \n", " 21           22      2    male     0            248698  \n", " 22           23      3  female     0            330923  \n", " 23           24      1    male     0            113788  \n", " 24           25      3  female     3            349909  \n", " 25           26      3  female     1            347077  \n", " 26           27      3    male     0              2631  \n", " 27           28      1    male     3             19950  \n", " 28           29      3  female     0            330959  \n", " 29           30      3    male     0            349216  \n", " ..          ...    ...     ...   ...               ...  \n", " 861         862      2    male     1             28134  \n", " 862         863      1  female     0             17466  \n", " 863         864      3  female     8          CA. 2343  \n", " 864         865      2    male     0            233866  \n", " 865         866      2  female     0            236852  \n", " 866         867      2  female     1     SC/PARIS 2149  \n", " 867         868      1    male     0          PC 17590  \n", " 868         869      3    male     0            345777  \n", " 869         870      3    male     1            347742  \n", " 870         871      3    male     0            349248  \n", " 871         872      1  female     1             11751  \n", " 872         873      1    male     0               695  \n", " 873         874      3    male     0            345765  \n", " 874         875      2  female     1         P/PP 3381  \n", " 875         876      3  female     0              2667  \n", " 876         877      3    male     0              7534  \n", " 877         878      3    male     0            349212  \n", " 878         879      3    male     0            349217  \n", " 879         880      1  female     0             11767  \n", " 880         881      2  female     0            230433  \n", " 881         882      3    male     0            349257  \n", " 882         883      3  female     0              7552  \n", " 883         884      2    male     0  C.A./SOTON 34068  \n", " 884         885      3    male     0   SOTON/OQ 392076  \n", " 885         886      3  female     0            382652  \n", " 886         887      2    male     0            211536  \n", " 887         888      1  female     0            112053  \n", " 888         889      3  female     1        W./C. 6607  \n", " 889         890      1    male     0            111369  \n", " 890         891      3    male     0            370376  \n", " \n", " [891 rows x 11 columns],\n", " 'test':       <PERSON> Embarked      Far<PERSON>  \\\n", " 0    34.5              NaN        Q    7.8292   \n", " 1    47.0              NaN        S    7.0000   \n", " 2    62.0              NaN        Q    9.6875   \n", " 3    27.0              NaN        S    8.6625   \n", " 4    22.0              NaN        S   12.2875   \n", " 5    14.0              NaN        S    9.2250   \n", " 6    30.0              NaN        Q    7.6292   \n", " 7    26.0              NaN        S   29.0000   \n", " 8    18.0              NaN        C    7.2292   \n", " 9    21.0              NaN        S   24.1500   \n", " 10    NaN              NaN        S    7.8958   \n", " 11   46.0              NaN        S   26.0000   \n", " 12   23.0              B45        S   82.2667   \n", " 13   63.0              NaN        S   26.0000   \n", " 14   47.0              E31        S   61.1750   \n", " 15   24.0              NaN        C   27.7208   \n", " 16   35.0              NaN        Q   12.3500   \n", " 17   21.0              NaN        C    7.2250   \n", " 18   27.0              NaN        S    7.9250   \n", " 19   45.0              NaN        C    7.2250   \n", " 20   55.0              NaN        C   59.4000   \n", " 21    9.0              NaN        S    3.1708   \n", " 22    NaN              NaN        S   31.6833   \n", " 23   21.0              NaN        C   61.3792   \n", " 24   48.0  B57 B59 B63 B66        C  262.3750   \n", " 25   50.0              NaN        S   14.5000   \n", " 26   22.0              B36        C   61.9792   \n", " 27   22.5              NaN        C    7.2250   \n", " 28   41.0              A21        S   30.5000   \n", " 29    NaN              NaN        C   21.6792   \n", " ..    ...              ...      ...       ...   \n", " 388  21.0              NaN        Q    7.7500   \n", " 389   6.0              NaN        S   21.0750   \n", " 390  23.0              B24        S   93.5000   \n", " 391  51.0              D28        S   39.4000   \n", " 392  13.0              NaN        S   20.2500   \n", " 393  47.0              NaN        S   10.5000   \n", " 394  29.0              NaN        S   22.0250   \n", " 395  18.0              C31        S   60.0000   \n", " 396  24.0              NaN        Q    7.2500   \n", " 397  48.0              B41        C   79.2000   \n", " 398  22.0              NaN        S    7.7750   \n", " 399  31.0              NaN        Q    7.7333   \n", " 400  30.0               C7        S  164.8667   \n", " 401  38.0              NaN        S   21.0000   \n", " 402  22.0              NaN        C   59.4000   \n", " 403  17.0              NaN        S   47.1000   \n", " 404  43.0              D40        C   27.7208   \n", " 405  20.0              D38        C   13.8625   \n", " 406  23.0              NaN        S   10.5000   \n", " 407  50.0              C80        C  211.5000   \n", " 408   NaN              NaN        Q    7.7208   \n", " 409   3.0              NaN        S   13.7750   \n", " 410   NaN              NaN        Q    7.7500   \n", " 411  37.0              C78        Q   90.0000   \n", " 412  28.0              NaN        S    7.7750   \n", " 413   NaN              NaN        S    8.0500   \n", " 414  39.0             C105        C  108.9000   \n", " 415  38.5              NaN        S    7.2500   \n", " 416   NaN              NaN        S    8.0500   \n", " 417   NaN              NaN        C   22.3583   \n", " \n", "                                                                 Name Parch  \\\n", " 0                                                   <PERSON>, <PERSON><PERSON>     0   \n", " 1                                   <PERSON>, Mrs<PERSON> (<PERSON>)     0   \n", " 2                                          <PERSON><PERSON>, Mr. <PERSON>     0   \n", " 3                                                   <PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 4                       <PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON><PERSON>)     1   \n", " 5                                         <PERSON><PERSON>, <PERSON>. <PERSON>     0   \n", " 6                                               <PERSON>, <PERSON><PERSON>     0   \n", " 7                                       <PERSON>, Mr. <PERSON>     1   \n", " 8                          <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 9                                            <PERSON>, Mr. <PERSON>     0   \n", " 10                                                  <PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 11                                        <PERSON>, Mr. <PERSON>     0   \n", " 12                     <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     0   \n", " 13                                              <PERSON>, Mr. <PERSON>     0   \n", " 14           <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " <PERSON>                     <PERSON>, Mrs<PERSON> <PERSON> (<PERSON><PERSON><PERSON>)     0   \n", " 16                                                 <PERSON><PERSON>, <PERSON><PERSON> <PERSON>     0   \n", " 17                                                 <PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 18                                      <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 19                             <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>\")\"     0   \n", " 20                                            <PERSON>, <PERSON><PERSON>     0   \n", " 21                                         <PERSON>, <PERSON><PERSON> <PERSON><PERSON>     1   \n", " 22                              <PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)     0   \n", " 23                                   <PERSON>, Mr. <PERSON> II     1   \n", " 24                   <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     3   \n", " 25                                           <PERSON><PERSON>, <PERSON>. <PERSON>     0   \n", " 26                                      <PERSON><PERSON><PERSON>, <PERSON><PERSON>     1   \n", " 27                                                 <PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 28                                           <PERSON>, Mr. <PERSON>     0   \n", " 29                                                 <PERSON><PERSON>, <PERSON><PERSON>     0   \n", " ..                                                               ...   ...   \n", " 388                                             <PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 389                                      <PERSON><PERSON>, <PERSON><PERSON>     1   \n", " 390                                       <PERSON>, Mr. <PERSON>     0   \n", " 391                   <PERSON>, Mrs. <PERSON> (<PERSON>)     1   \n", " 392                                    <PERSON>, <PERSON><PERSON>     2   \n", " 393                                             <PERSON>, Mr. <PERSON>     0   \n", " 394                                         <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>     1   \n", " 395                   <PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 396                                             <PERSON>, <PERSON><PERSON> <PERSON>     0   \n", " 397  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON><PERSON> (<PERSON><PERSON>)     1   \n", " 398                                   <PERSON><PERSON>-<PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 399                                         <PERSON><PERSON>, Mr. <PERSON>     0   \n", " 400                                          <PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 401                                                  <PERSON>, Mr. <PERSON>     0   \n", " 402                                   <PERSON>, <PERSON><PERSON>     1   \n", " 403                                           <PERSON><PERSON>, Mr. <PERSON>     0   \n", " 404                                     <PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>     0   \n", " 405                     <PERSON><PERSON>, Mr. <PERSON> (Baron <PERSON>\")\"     0   \n", " 406                                        <PERSON>, Mr. <PERSON>     0   \n", " 407                                       <PERSON><PERSON>, Mr. <PERSON>     1   \n", " 408                                  <PERSON><PERSON><PERSON>, <PERSON><PERSON>\"\"     0   \n", " 409                                        <PERSON>, <PERSON><PERSON>     1   \n", " 410                                           <PERSON><PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 411                  <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)     0   \n", " 412                                   <PERSON><PERSON>, <PERSON><PERSON>     0   \n", " 413                                               <PERSON><PERSON><PERSON>, Mr<PERSON>     0   \n", " 414                                     <PERSON><PERSON>, Dona. Fermina     0   \n", " 415                                     <PERSON>, Mr. <PERSON>     0   \n", " 416                                              <PERSON>, Mr. <PERSON>     0   \n", " 417                                         <PERSON>, <PERSON><PERSON>     1   \n", " \n", "     PassengerId Pclass     Sex SibSp              Ticket  \n", " 0           892      3    male     0              330911  \n", " 1           893      3  female     1              363272  \n", " 2           894      2    male     0              240276  \n", " 3           895      3    male     0              315154  \n", " 4           896      3  female     1             3101298  \n", " 5           897      3    male     0                7538  \n", " 6           898      3  female     0              330972  \n", " 7           899      2    male     1              248738  \n", " 8           900      3  female     0                2657  \n", " 9           901      3    male     2           A/4 48871  \n", " 10          902      3    male     0              349220  \n", " 11          903      1    male     0                 694  \n", " 12          904      1  female     1               21228  \n", " 13          905      2    male     1               24065  \n", " 14          906      1  female     1         W.E.P. 5734  \n", " 15          907      2  female     1       SC/PARIS 2167  \n", " 16          908      2    male     0              233734  \n", " 17          909      3    male     0                2692  \n", " 18          910      3  female     1    STON/O2. 3101270  \n", " 19          911      3  female     0                2696  \n", " 20          912      1    male     1            PC 17603  \n", " 21          913      3    male     0             C 17368  \n", " 22          914      1  female     0            PC 17598  \n", " 23          915      1    male     0            PC 17597  \n", " 24          916      1  female     1            PC 17608  \n", " 25          917      3    male     1           A/5. 3337  \n", " 26          918      1  female     0              113509  \n", " 27          919      3    male     0                2698  \n", " 28          920      1    male     0              113054  \n", " 29          921      3    male     2                2662  \n", " ..          ...    ...     ...   ...                 ...  \n", " 388        1280      3    male     0              364858  \n", " 389        1281      3    male     3              349909  \n", " 390        1282      1    male     0               12749  \n", " 391        1283      1  female     0            PC 17592  \n", " 392        1284      3    male     0           C.A. 2673  \n", " 393        1285      2    male     0          C.A. 30769  \n", " 394        1286      3    male     3              315153  \n", " 395        1287      1  female     1               13695  \n", " 396        1288      3    male     0              371109  \n", " 397        1289      1  female     1               13567  \n", " 398        1290      3    male     0              347065  \n", " 399        1291      3    male     0               21332  \n", " 400        1292      1  female     0               36928  \n", " 401        1293      2    male     1               28664  \n", " 402        1294      1  female     0              112378  \n", " 403        1295      1    male     0              113059  \n", " 404        1296      1    male     1               17765  \n", " 405        1297      2    male     0       SC/PARIS 2166  \n", " 406        1298      2    male     1               28666  \n", " 407        1299      1    male     1              113503  \n", " 408        1300      3  female     0              334915  \n", " 409        1301      3  female     1  SOTON/O.Q. 3101315  \n", " 410        1302      3  female     0              365237  \n", " 411        1303      1  female     1               19928  \n", " 412        1304      3  female     0              347086  \n", " 413        1305      3    male     0           A.5. 3236  \n", " 414        1306      1  female     0            PC 17758  \n", " 415        1307      3    male     0  SOTON/O.Q. 3101262  \n", " 416        1308      3    male     0              359309  \n", " 417        1309      3    male     1                2668  \n", " \n", " [418 rows x 11 columns],\n", " 'target': 0      0.0\n", " 1      1.0\n", " 2      1.0\n", " 3      1.0\n", " 4      0.0\n", " 5      0.0\n", " 6      0.0\n", " 7      0.0\n", " 8      1.0\n", " 9      1.0\n", " 10     1.0\n", " 11     1.0\n", " 12     0.0\n", " 13     0.0\n", " 14     0.0\n", " 15     1.0\n", " 16     0.0\n", " 17     1.0\n", " 18     0.0\n", " 19     1.0\n", " 20     0.0\n", " 21     1.0\n", " 22     1.0\n", " 23     1.0\n", " 24     0.0\n", " 25     1.0\n", " 26     0.0\n", " 27     0.0\n", " 28     1.0\n", " 29     0.0\n", "       ... \n", " 861    0.0\n", " 862    1.0\n", " 863    0.0\n", " 864    0.0\n", " 865    1.0\n", " 866    1.0\n", " 867    0.0\n", " 868    0.0\n", " 869    1.0\n", " 870    0.0\n", " 871    1.0\n", " 872    0.0\n", " 873    0.0\n", " 874    1.0\n", " 875    1.0\n", " 876    0.0\n", " 877    0.0\n", " 878    0.0\n", " 879    1.0\n", " 880    1.0\n", " 881    0.0\n", " 882    0.0\n", " 883    0.0\n", " 884    0.0\n", " 885    0.0\n", " 886    0.0\n", " 887    1.0\n", " 888    0.0\n", " 889    1.0\n", " 890    0.0\n", " Name: Survived, Length: 891, dtype: float64,\n", " 'target_test': Series([], Name: Survived, dtype: float64)}"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# >> Examples for duplicate detection and removal\n", "# titanic has no exact duplicate anyway\n", "# Available strategies =\n", "#        'ED':  exact duplicate detection/removal or\n", "#        'AD':  for aproximate duplicate records detection and removal\n", "#        based on <PERSON><PERSON><PERSON> similarity \n", "\n", "# import the Duplicate_detector class\n", "import learn2clean.duplicate_detection.duplicate_detector as dup\n", "\n", "#Remove exact duplicates with 'ED' strategy of the Duplicate_detector class\n", "\n", "dup.Duplicate_detector(titanic_not_encoded.copy(), strategy='ED', verbose=False).transform()\n", "\n", "#Remove approximate duplicates with thresholding <PERSON><PERSON><PERSON> similarity \n", "# using 'AD'strategy of the Duplicate_detector class\n", "dup.Duplicate_detector(titanic_not_encoded.copy(), strategy='AD', threshold = .3, verbose=False).transform()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Detect inconsistencies"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>Consistency checking\n", "* For train dataset\n", "Constraints from the file: titanic_example_constraints.tdda\n", "Constraints passing: 51\n", "\n", "Constraints failing: 5\n", "\n", "FIELDS:\n", "\n", "Survived: 5 failures  0 passes  type ✗  min ✗  max ✗  sign ✗  max_nulls ✗\n", "\n", "Age: 0 failures  4 passes  type ✓  min ✓  max ✓  sign ✓\n", "\n", "Cabin: 0 failures  3 passes  type ✓  min_length ✓  max_length ✓\n", "\n", "Embarked: 0 failures  4 passes  type ✓  min_length ✓  max_length ✓  allowed_values ✓\n", "\n", "Fare: 0 failures  5 passes  type ✓  min ✓  max ✓  sign ✓  max_nulls ✓\n", "\n", "Name: 0 failures  5 passes  type ✓  min_length ✓  max_length ✓  max_nulls ✓  no_duplicates ✓\n", "\n", "Parch: 0 failures  5 passes  type ✓  min ✓  max ✓  sign ✓  max_nulls ✓\n", "\n", "PassengerId: 0 failures  6 passes  type ✓  min ✓  max ✓  sign ✓  max_nulls ✓  no_duplicates ✓\n", "\n", "Pclass: 0 failures  5 passes  type ✓  min ✓  max ✓  sign ✓  max_nulls ✓\n", "\n", "Sex: 0 failures  5 passes  type ✓  min_length ✓  max_length ✓  max_nulls ✓  allowed_values ✓\n", "\n", "SibSp: 0 failures  5 passes  type ✓  min ✓  max ✓  sign ✓  max_nulls ✓\n", "\n", "Ticket: 0 failures  4 passes  type ✓  min_length ✓  max_length ✓  max_nulls ✓\n", "\n", "SUMMARY:\n", "\n", "Constraints passing: 51\n", "Constraints failing: 5\n", "          field  failures  passes   type    min min_length    max max_length  \\\n", "0      Survived         5       0  False  False        NaN  False        NaN   \n", "1           Age         0       4   True   True        NaN   True        NaN   \n", "2         Cabin         0       3   True    NaN       True    NaN       True   \n", "3      Embarked         0       4   True    NaN       True    NaN       True   \n", "4          Fare         0       5   True   True        NaN   True        NaN   \n", "5          Name         0       5   True    NaN       True    NaN       True   \n", "6         Parch         0       5   True   True        NaN   True        NaN   \n", "7   PassengerId         0       6   True   True        NaN   True        NaN   \n", "8        Pclass         0       5   True   True        NaN   True        NaN   \n", "9           Sex         0       5   True    NaN       True    NaN       True   \n", "10        SibSp         0       5   True   True        NaN   True        NaN   \n", "11       Ticket         0       4   True    NaN       True    NaN       True   \n", "\n", "     sign max_nulls no_duplicates allowed_values  \n", "0   False     False           NaN            NaN  \n", "1    True       NaN           NaN            NaN  \n", "2     NaN       NaN           NaN            NaN  \n", "3     NaN       NaN           NaN           True  \n", "4    True      True           NaN            NaN  \n", "5     NaN      True          True            NaN  \n", "6    True      True           NaN            NaN  \n", "7    True      True          True            NaN  \n", "8    True      True           NaN            NaN  \n", "9     NaN      True           NaN           True  \n", "10   True      True           NaN            NaN  \n", "11    NaN      True           NaN            NaN  \n", "Row index with constraint failure:\n", "\n", "[]\n", "* For test dataset\n", "Constraints from the file: titanic_example_constraints.tdda\n", "Constraints passing: 47\n", "\n", "Constraints failing: 9\n", "\n", "FIELDS:\n", "\n", "Survived: 5 failures  0 passes  type ✗  min ✗  max ✗  sign ✗  max_nulls ✗\n", "\n", "Age: 1 failure  3 passes  type ✓  min ✗  max ✓  sign ✓\n", "\n", "Cabin: 0 failures  3 passes  type ✓  min_length ✓  max_length ✓\n", "\n", "Embarked: 0 failures  4 passes  type ✓  min_length ✓  max_length ✓  allowed_values ✓\n", "\n", "Fare: 1 failure  4 passes  type ✓  min ✓  max ✓  sign ✓  max_nulls ✗\n", "\n", "Name: 0 failures  5 passes  type ✓  min_length ✓  max_length ✓  max_nulls ✓  no_duplicates ✓\n", "\n", "Parch: 1 failure  4 passes  type ✓  min ✓  max ✗  sign ✓  max_nulls ✓\n", "\n", "PassengerId: 1 failure  5 passes  type ✓  min ✓  max ✗  sign ✓  max_nulls ✓  no_duplicates ✓\n", "\n", "Pclass: 0 failures  5 passes  type ✓  min ✓  max ✓  sign ✓  max_nulls ✓\n", "\n", "Sex: 0 failures  5 passes  type ✓  min_length ✓  max_length ✓  max_nulls ✓  allowed_values ✓\n", "\n", "SibSp: 0 failures  5 passes  type ✓  min ✓  max ✓  sign ✓  max_nulls ✓\n", "\n", "Ticket: 0 failures  4 passes  type ✓  min_length ✓  max_length ✓  max_nulls ✓\n", "\n", "SUMMARY:\n", "\n", "Constraints passing: 47\n", "Constraints failing: 9\n", "          field  failures  passes   type    min min_length    max max_length  \\\n", "0      Survived         5       0  False  False        NaN  False        NaN   \n", "1           Age         1       3   True  False        NaN   True        NaN   \n", "2         Cabin         0       3   True    NaN       True    NaN       True   \n", "3      Embarked         0       4   True    NaN       True    NaN       True   \n", "4          Fare         1       4   True   True        NaN   True        NaN   \n", "5          Name         0       5   True    NaN       True    NaN       True   \n", "6         Parch         1       4   True   True        NaN  False        NaN   \n", "7   PassengerId         1       5   True   True        NaN  False        NaN   \n", "8        Pclass         0       5   True   True        NaN   True        NaN   \n", "9           Sex         0       5   True    NaN       True    NaN       True   \n", "10        SibSp         0       5   True   True        NaN   True        NaN   \n", "11       Ticket         0       4   True    NaN       True    NaN       True   \n", "\n", "     sign max_nulls no_duplicates allowed_values  \n", "0   False     False           NaN            NaN  \n", "1    True       NaN           NaN            NaN  \n", "2     NaN       NaN           NaN            NaN  \n", "3     NaN       NaN           NaN           True  \n", "4    True     False           NaN            NaN  \n", "5     NaN      True          True            NaN  \n", "6    True      True           NaN            NaN  \n", "7    True      True          True            NaN  \n", "8    True      True           NaN            NaN  \n", "9     NaN      True           NaN           True  \n", "10   True      True           NaN            NaN  \n", "11    NaN      True           NaN            NaN  \n", "Row index with constraint failure:\n", "\n", "[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 352, 353, 354, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417]\n", "Consistency checking done -- CPU time: 0.07398724555969238 seconds\n", ">>Consistency checking\n", "* For train dataset\n", "No violation on variable Sex for pattern# 1 as '^[a-z]{4,6}$'\n", "Number of pattern violations on variable' Embarked 'for pattern# 2 as '^[Z|S]$' : 247\n", "Indexes of rows to be removed: [1, 5, 9, 16, 19, 22, 26, 28, 30, 31, 32, 34, 36, 39, 42, 43, 44, 46, 47, 48, 52, 54, 57, 60, 64, 65, 73, 82, 96, 97, 109, 111, 114, 116, 118, 122, 125, 126, 128, 130, 135, 139, 140, 143, 155, 156, 171, 174, 177, 181, 186, 188, 194, 195, 196, 198, 203, 207, 208, 209, 214, 215, 218, 240, 241, 244, 245, 255, 256, 258, 260, 264, 273, 274, 278, 280, 285, 289, 291, 292, 295, 296, 299, 300, 301, 303, 306, 307, 308, 309, 310, 311, 319, 322, 325, 329, 330, 337, 352, 354, 358, 359, 361, 362, 364, 366, 367, 368, 369, 370, 373, 375, 377, 378, 380, 381, 388, 389, 393, 411, 412, 420, 421, 428, 448, 452, 453, 455, 459, 468, 469, 473, 484, 487, 493, 495, 496, 501, 502, 505, 510, 513, 517, 522, 523, 524, 525, 531, 532, 533, 537, 539, 544, 547, 550, 552, 553, 556, 557, 560, 568, 573, 578, 581, 583, 584, 587, 591, 593, 598, 599, 604, 608, 612, 613, 620, 622, 626, 629, 632, 641, 644, 645, 647, 653, 654, 657, 659, 661, 679, 680, 681, 685, 691, 693, 697, 698, 700, 702, 703, 709, 710, 716, 718, 727, 731, 737, 742, 749, 762, 766, 767, 768, 773, 776, 778, 780, 787, 789, 790, 793, 798, 803, 817, 825, 827, 828, 830, 832, 835, 839, 842, 843, 847, 849, 852, 858, 859, 866, 874, 875, 879, 885, 889, 890]\n", "* For test dataset\n", "No violation on variable Sex for pattern# 1 as '^[a-z]{4,6}$'\n", "Number of pattern violations on variable' Embarked 'for pattern# 2 as '^[Z|S]$' : 148\n", "Indexes of rows to be removed: [0, 2, 6, 8, 15, 16, 17, 19, 20, 23, 24, 26, 27, 29, 34, 35, 40, 46, 47, 48, 51, 54, 55, 59, 63, 64, 66, 68, 70, 73, 74, 75, 79, 80, 84, 85, 86, 88, 94, 100, 102, 104, 106, 107, 110, 111, 112, 113, 115, 116, 118, 121, 122, 124, 127, 131, 133, 136, 142, 150, 151, 160, 161, 166, 168, 171, 173, 177, 179, 181, 183, 184, 193, 196, 200, 202, 205, 206, 208, 216, 218, 220, 224, 225, 227, 233, 234, 236, 237, 239, 240, 242, 249, 252, 256, 264, 270, 271, 272, 273, 274, 282, 283, 288, 291, 292, 296, 297, 301, 304, 311, 313, 314, 315, 316, 321, 327, 331, 332, 333, 337, 339, 343, 347, 350, 358, 361, 364, 366, 368, 369, 371, 375, 380, 381, 388, 396, 397, 399, 402, 404, 405, 407, 408, 410, 411, 414, 417]\n", "Consistency checking done -- CPU time: 0.03586006164550781 seconds\n"]}, {"data": {"text/plain": ["{'train':            <PERSON><PERSON><PERSON> Embarked  \\\n", " 0            NaN        S   \n", " 2            NaN        S   \n", " 3           C123        S   \n", " 4            NaN        S   \n", " 6            E46        S   \n", " 7            NaN        S   \n", " 8            NaN        S   \n", " 10            G6        S   \n", " 11          C103        S   \n", " 12           NaN        S   \n", " 13           NaN        S   \n", " 14           NaN        S   \n", " 15           NaN        S   \n", " 17           NaN        S   \n", " 18           NaN        S   \n", " 20           NaN        S   \n", " 21           D56        S   \n", " 23            A6        S   \n", " 24           NaN        S   \n", " 25           NaN        S   \n", " 27   C23 C25 C27        S   \n", " 29           NaN        S   \n", " 33           NaN        S   \n", " 35           NaN        S   \n", " 37           NaN        S   \n", " 38           NaN        S   \n", " 40           NaN        S   \n", " 41           NaN        S   \n", " 45           NaN        S   \n", " 49           NaN        S   \n", " ..           ...      ...   \n", " 851          NaN        S   \n", " 853          D28        S   \n", " 854          NaN        S   \n", " 855          NaN        S   \n", " 856          NaN        S   \n", " 857          E17        S   \n", " 860          NaN        S   \n", " 861          NaN        S   \n", " 862          D17        S   \n", " 863          NaN        S   \n", " 864          NaN        S   \n", " 865          NaN        S   \n", " 867          A24        S   \n", " 868          NaN        S   \n", " 869          NaN        S   \n", " 870          NaN        S   \n", " 871          D35        S   \n", " 872  B51 B53 B55        S   \n", " 873          NaN        S   \n", " 876          NaN        S   \n", " 877          NaN        S   \n", " 878          NaN        S   \n", " 880          NaN        S   \n", " 881          NaN        S   \n", " 882          NaN        S   \n", " 883          NaN        S   \n", " 884          NaN        S   \n", " 886          NaN        S   \n", " 887          B42        S   \n", " 888          NaN        S   \n", " \n", "                                                           Name     Sex  \\\n", " 0                                      <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 2                                       <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female   \n", " 3                 <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 4                                     <PERSON>, Mr. <PERSON>    male   \n", " 6                                      <PERSON>, Mr. <PERSON>    male   \n", " 7                               <PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>    male   \n", " 8            <PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 10                             <PERSON><PERSON>, <PERSON><PERSON>  female   \n", " 11                                    <PERSON><PERSON>, <PERSON><PERSON>  female   \n", " 12                              <PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 13                                 <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 14                        <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON>  female   \n", " 15                            <PERSON><PERSON><PERSON>, Mrs. (<PERSON>)   female   \n", " 17                                <PERSON>, Mr. <PERSON>    male   \n", " 18     <PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON><PERSON>)  female   \n", " 20                                        <PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 21                                       <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 23                                <PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 24                               <PERSON><PERSON>, <PERSON><PERSON>  female   \n", " 25   <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)  female   \n", " 27                              <PERSON>, Mr. <PERSON>    male   \n", " 29                                         <PERSON><PERSON><PERSON>, Mr. <PERSON><PERSON>    male   \n", " 33                                       <PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 35                              <PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 37                                    Cann, Mr. <PERSON>    male   \n", " 38                          <PERSON><PERSON>, Miss. <PERSON>  female   \n", " 40              <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 41    <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 45                                    <PERSON>, Mr. <PERSON>    male   \n", " 49               Arnold<PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)  female   \n", " ..                                                         ...     ...   \n", " 851                                        <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 853                                  <PERSON>, <PERSON><PERSON>  female   \n", " 854              <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)  female   \n", " 855                                 <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 856                 <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 857                                     <PERSON>, Mr. <PERSON>     male   \n", " 860                                    <PERSON>, Mr. <PERSON>    male   \n", " 861                                <PERSON>, Mr. <PERSON>    male   \n", " 862        <PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 863                          <PERSON>, <PERSON><PERSON> <PERSON> \"<PERSON>\"  female   \n", " 864                                     <PERSON>, Mr. <PERSON>    male   \n", " 865                                   <PERSON><PERSON>, Mrs. (<PERSON><PERSON><PERSON>)  female   \n", " 867                       <PERSON><PERSON>, Mr. <PERSON> II    male   \n", " 868                                <PERSON>, Mr. <PERSON><PERSON>    male   \n", " 869                            <PERSON>, <PERSON>. <PERSON>   \n", " 870                                          <PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 871           <PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)  female   \n", " 872                                   <PERSON><PERSON>, Mr. <PERSON><PERSON>    male   \n", " 873                                <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 876                              <PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 877                                       <PERSON><PERSON><PERSON>, Mr. <PERSON><PERSON><PERSON>    male   \n", " 878                                         <PERSON><PERSON><PERSON>, Mr. <PERSON><PERSON>    male   \n", " 880               <PERSON>, Mrs. <PERSON> (<PERSON><PERSON><PERSON>)  female   \n", " 881                                         <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 882                               <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>  female   \n", " 883                              <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 884                                     <PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 886                                      <PERSON><PERSON>, <PERSON><PERSON>    male   \n", " 887                               <PERSON>, <PERSON><PERSON>  female   \n", " 888                   <PERSON>, <PERSON><PERSON> <PERSON> \"<PERSON>\"  female   \n", " \n", "                Ticket  \n", " 0           A/5 21171  \n", " 2    STON/O2. 3101282  \n", " 3              113803  \n", " 4              373450  \n", " 6               17463  \n", " 7              349909  \n", " 8              347742  \n", " 10            PP 9549  \n", " 11             113783  \n", " 12          A/5. 2151  \n", " 13             347082  \n", " 14             350406  \n", " 15             248706  \n", " 17             244373  \n", " 18             345763  \n", " 20             239865  \n", " 21             248698  \n", " 23             113788  \n", " 24             349909  \n", " 25             347077  \n", " 27              19950  \n", " 29             349216  \n", " 33         C.A. 24579  \n", " 35             113789  \n", " 37         A./5. 2152  \n", " 38             345764  \n", " 40               7546  \n", " 41              11668  \n", " 45    S.C./A.4. 23567  \n", " 49             349237  \n", " ..                ...  \n", " 851            347060  \n", " 853          PC 17592  \n", " 854            244252  \n", " 855            392091  \n", " 856             36928  \n", " 857            113055  \n", " 860            350026  \n", " 861             28134  \n", " 862             17466  \n", " 863          CA. 2343  \n", " 864            233866  \n", " 865            236852  \n", " 867          PC 17590  \n", " 868            345777  \n", " 869            347742  \n", " 870            349248  \n", " 871             11751  \n", " 872               695  \n", " 873            345765  \n", " 876              7534  \n", " 877            349212  \n", " 878            349217  \n", " 880            230433  \n", " 881            349257  \n", " 882              7552  \n", " 883  C.A./SOTON 34068  \n", " 884   SOTON/OQ 392076  \n", " 886            211536  \n", " 887            112053  \n", " 888        W./C. 6607  \n", " \n", " [646 rows x 5 columns], 'test':            <PERSON><PERSON><PERSON>  \\\n", " 1            NaN        S   \n", " 3            NaN        S   \n", " 4            NaN        S   \n", " 5            NaN        S   \n", " 7            NaN        S   \n", " 9            NaN        S   \n", " 10           NaN        S   \n", " 11           NaN        S   \n", " 12           B45        S   \n", " 13           NaN        S   \n", " 14           E31        S   \n", " 18           NaN        S   \n", " 21           NaN        S   \n", " 22           NaN        S   \n", " 25           NaN        S   \n", " 28           A21        S   \n", " 30           NaN        S   \n", " 31           NaN        S   \n", " 32           NaN        S   \n", " 33           NaN        S   \n", " 36           NaN        S   \n", " 37           NaN        S   \n", " 38           NaN        S   \n", " 39           NaN        S   \n", " 41           D34        S   \n", " 42           NaN        S   \n", " 43           NaN        S   \n", " 44           D19        S   \n", " 45           NaN        S   \n", " 49           NaN        S   \n", " ..           ...      ...   \n", " 372  B52 B54 B56        S   \n", " 373          NaN        S   \n", " 374          A34        S   \n", " 376          NaN        S   \n", " 377          NaN        S   \n", " 378          C39        S   \n", " 379          NaN        S   \n", " 382          NaN        S   \n", " 383          NaN        S   \n", " 384          NaN        S   \n", " 385          NaN        S   \n", " 386          NaN        S   \n", " 387          NaN        S   \n", " 389          NaN        S   \n", " 390          B24        S   \n", " 391          D28        S   \n", " 392          NaN        S   \n", " 393          NaN        S   \n", " 394          NaN        S   \n", " 395          C31        S   \n", " 398          NaN        S   \n", " 400           C7        S   \n", " 401          NaN        S   \n", " 403          NaN        S   \n", " 406          NaN        S   \n", " 409          NaN        S   \n", " 412          NaN        S   \n", " 413          NaN        S   \n", " 415          NaN        S   \n", " 416          NaN        S   \n", " \n", "                                                         Name     Sex  \\\n", " 1                           <PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 3                                           <PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 4               <PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON><PERSON>)  female   \n", " 5                                 <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 7                               <PERSON>, Mr. <PERSON>    male   \n", " 9                                    <PERSON>, Mr. <PERSON>    male   \n", " 10                                          <PERSON><PERSON><PERSON>, Mr. <PERSON><PERSON>    male   \n", " 11                                <PERSON>, Mr. <PERSON>    male   \n", " 12             <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)  female   \n", " 13                                      <PERSON>, Mr. <PERSON>    male   \n", " 14   <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 18                              <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female   \n", " 21                                 <PERSON>, <PERSON><PERSON> <PERSON><PERSON>   \n", " 22                      <PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)  female   \n", " 25                                   <PERSON><PERSON>, Mr. <PERSON> A    male   \n", " 28                                   <PERSON>, Mr. <PERSON>    male   \n", " 30                              <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 31                             <PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 32                  Dean, Mrs. <PERSON> (<PERSON>)  female   \n", " 33         <PERSON>, Mrs. <PERSON> (<PERSON>\" <PERSON>)\"  female   \n", " 36                                       <PERSON>, <PERSON><PERSON> A  female   \n", " 37                                        <PERSON><PERSON><PERSON>, <PERSON><PERSON>  female   \n", " 38                                           Sap, Mr. <PERSON>    male   \n", " 39                                             He<PERSON>, Mr. <PERSON>    male   \n", " 41                               <PERSON>, Mr. <PERSON>    male   \n", " 42                                     <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 43                     <PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 44          <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 45                           <PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 49            Coutts, Mrs. <PERSON> (<PERSON><PERSON><PERSON>\" <PERSON><PERSON><PERSON>)\"  female   \n", " ..                                                       ...     ...   \n", " 372                                  <PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 373                                   <PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 374                    <PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 376                                        <PERSON><PERSON>, Miss. <PERSON>  female   \n", " 377                             <PERSON><PERSON><PERSON>, Mr. <PERSON>\"\"    male   \n", " 378                              <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 379                              <PERSON><PERSON><PERSON>, <PERSON>. <PERSON>    male   \n", " 382                               <PERSON><PERSON><PERSON>, Mrs. <PERSON> (Emma)  female   \n", " 383                      <PERSON><PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 384                           <PERSON>, Mr. <PERSON>\"\"    male   \n", " 385                                       <PERSON>, <PERSON><PERSON>  female   \n", " 386                           <PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 387                                          <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 389                              <PERSON><PERSON>, <PERSON><PERSON>    male   \n", " 390                               <PERSON>, Mr. <PERSON>    male   \n", " 391           Lines, Mrs. <PERSON> (<PERSON>)  female   \n", " 392                            <PERSON>, <PERSON>. <PERSON>    male   \n", " 393                                     <PERSON>, Mr. <PERSON>    male   \n", " 394                                 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 395           <PERSON>, Mrs. <PERSON> (<PERSON>)  female   \n", " 398                           <PERSON><PERSON>-<PERSON><PERSON><PERSON>, Mr. <PERSON><PERSON> A    male   \n", " 400                                  <PERSON><PERSON>, <PERSON><PERSON>  female   \n", " 401                                          <PERSON>, Mr. <PERSON>    male   \n", " 403                                   <PERSON><PERSON>, Mr. <PERSON>    male   \n", " 406                                <PERSON>, Mr. <PERSON>    male   \n", " 409                                <PERSON>, <PERSON><PERSON>  female   \n", " 412                           <PERSON><PERSON>, <PERSON><PERSON>  female   \n", " 413                                       <PERSON><PERSON><PERSON>, Mr. <PERSON><PERSON>    male   \n", " 415                             <PERSON><PERSON><PERSON>, Mr. <PERSON>    male   \n", " 416                                      <PERSON>, Mr. <PERSON>    male   \n", " \n", "                  Ticket  \n", " 1                363272  \n", " 3                315154  \n", " 4               3101298  \n", " 5                  7538  \n", " 7                248738  \n", " 9             A/4 48871  \n", " 10               349220  \n", " 11                  694  \n", " 12                21228  \n", " 13                24065  \n", " 14          W.E.P. 5734  \n", " 18     STON/O2. 3101270  \n", " 21              C 17368  \n", " 22             PC 17598  \n", " 25            A/5. 3337  \n", " 28               113054  \n", " 30           SC/AH 3085  \n", " 31           C.A. 31029  \n", " 32            C.A. 2315  \n", " 33           W./C. 6607  \n", " 36               342712  \n", " 37               315087  \n", " 38               345768  \n", " 39                 1601  \n", " 41               113778  \n", " 42   SOTON/O.Q. 3101263  \n", " 43               237249  \n", " 44                11753  \n", " 45    STON/O 2. 3101291  \n", " 49           C.A. 37671  \n", " ..                  ...  \n", " 372              112058  \n", " 373              248746  \n", " 374               33638  \n", " 376              315152  \n", " 377               29107  \n", " 378                 680  \n", " 379              347077  \n", " 382              364498  \n", " 383              376566  \n", " 384       SC/PARIS 2159  \n", " 385              220845  \n", " 386              349911  \n", " 387              244346  \n", " 389              349909  \n", " 390               12749  \n", " 391            PC 17592  \n", " 392           C.A. 2673  \n", " 393          C.A. 30769  \n", " 394              315153  \n", " 395               13695  \n", " 398              347065  \n", " 400               36928  \n", " 401               28664  \n", " 403              113059  \n", " 406               28666  \n", " 409  SOTON/O.Q. 3101315  \n", " 412              347086  \n", " 413           A.5. 3236  \n", " 415  SOTON/O.Q. 3101262  \n", " 416              359309  \n", " \n", " [270 rows x 5 columns], 'target': 0      0\n", " 1      1\n", " 2      1\n", " 3      1\n", " 4      0\n", " 5      0\n", " 6      0\n", " 7      0\n", " 8      1\n", " 9      1\n", " 10     1\n", " 11     1\n", " 12     0\n", " 13     0\n", " 14     0\n", " 15     1\n", " 16     0\n", " 17     1\n", " 18     0\n", " 19     1\n", " 20     0\n", " 21     1\n", " 22     1\n", " 23     1\n", " 24     0\n", " 25     1\n", " 26     0\n", " 27     0\n", " 28     1\n", " 29     0\n", "       ..\n", " 861    0\n", " 862    1\n", " 863    0\n", " 864    0\n", " 865    1\n", " 866    1\n", " 867    0\n", " 868    0\n", " 869    1\n", " 870    0\n", " 871    1\n", " 872    0\n", " 873    0\n", " 874    1\n", " 875    1\n", " 876    0\n", " 877    0\n", " 878    0\n", " 879    1\n", " 880    1\n", " 881    0\n", " 882    0\n", " 883    0\n", " 884    0\n", " 885    0\n", " 886    0\n", " 887    1\n", " 888    0\n", " 889    1\n", " 890    0\n", " Name: Survived, Length: 891, dtype: int64, 'target_test': Series([], Name: Survived, dtype: int64)}"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# >> Examples for inconsistency detection \n", "# Available consistency checking strategies :\n", "#            - 'CC': checks whether the data satisfy the constraints\n", "#                specified in a 'file_name'_constraint.tdda stored in 'save' directory\n", "#            - 'PC': checks whether the data satisfy the patterns\n", "#                specified in 'file_name'_patterns.txt stored in 'save' directory\n", "\n", "import learn2clean.loading.reader as rd \n", "d_not_enc = rd.Reader(sep=',',verbose=False, encoding=False)                 \n", "titanic  = [\"../datasets/titanic/titanic_train.csv\", \"../datasets/titanic/test.csv\"]\n", "titanic_not_encoded = d_not_enc.train_test_split(titanic, 'Survived')\n", "                \n", "# import the Consistency_checker class\n", "import learn2clean.consistency_checking.consistency_checker as cc\n", "\n", "# discover the constraints from the input (train) dataset and store them in a file entitled 'titanic_discovered'_constraint.tdda in the 'save' directory\n", "#cc.constraint_discovery(read_dataset('titanic'), file_name='titanic_discovered')\n", "\n", "# discover the patterns from the input (train) dataset and store them in a file entitled 'titanic_discovered'_patterns.txt in the 'save' directory\n", "#cc.pattern_discovery(read_dataset('titanic'), file_name='titanic_discovered')\n", "\n", "# detect pattern violations with respect to a given file of patterns entitled 'titanic_example'__constraint.tdda\" stored in the 'save' directory\n", "\n", "cc.Consistency_checker(titanic_not_encoded, strategy='CC', file_name='titanic_example',verbose=True).transform()\n", "\n", "# detect pattern violations with respect to a given file of patterns entitled 'titanic_example'_patterns.txt\" stored in the 'save' directory\n", "\n", "cc.Consistency_checker(titanic_not_encoded, strategy='PC', file_name='titanic_example',verbose=True).transform()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Select features"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Reading csv : titanic_train.csv ...\n", "Reading data ...\n", "CPU time: 0.04526114463806152 seconds\n", "Profiling datasets\n", "      Attribute     Type  Num. Missing Values  Num. Unique Values           Sknewness  Kurtosis\n", "0   PassengerId  float64                  0.0               891.0                   0      -1.2\n", "1      Survived  float64                  0.0                 2.0            0.477717  -1.77179\n", "2        Pclass  float64                  0.0                 3.0           -0.629486  -1.27957\n", "3           Age  float64                177.0                89.0  0.3882898514698658  0.168637\n", "4         SibSp  float64                  0.0                 7.0             3.68913   17.7735\n", "5         Parch  float64                  0.0                 7.0             2.74449   9.71661\n", "6          Fare  float64                  0.0               248.0             4.77925   33.2043\n", "7          Name   object                  0.0               891.0                 N/A       N/A\n", "8           Sex   object                  0.0                 2.0                 N/A       N/A\n", "9        Ticket   object                  0.0               681.0                 N/A       N/A\n", "10        Cabin   object                687.0               148.0                 N/A       N/A\n", "11     Embarked   object                  2.0                 4.0                 N/A       N/A\n", "\n", "Reading csv : test.csv ...\n", "Reading data ...\n", "CPU time: 0.031436920166015625 seconds\n", "Profiling datasets\n", "      Attribute     Type  Num. Missing Values  Num. Unique Values            Sknewness   Kurtosis\n", "0   PassengerId  float64                  0.0               418.0                    0   -1.20001\n", "1        Pclass  float64                  0.0                 3.0            -0.532252    -1.3805\n", "2           Age  float64                 86.0                80.0  0.45529229694892764  0.0645088\n", "3         SibSp  float64                  0.0                 7.0              4.15336    26.1685\n", "4         Parch  float64                  0.0                 8.0              4.63774    31.0237\n", "5          Fare  float64                  1.0               170.0   3.6739366758439074    17.6931\n", "6          Name   object                  0.0               418.0                  N/A        N/A\n", "7           Sex   object                  0.0                 2.0                  N/A        N/A\n", "8        Ticket   object                  0.0               363.0                  N/A        N/A\n", "9         Cabin   object                327.0                77.0                  N/A        N/A\n", "10     Embarked   object                  0.0                 3.0                  N/A        N/A\n", "\n", "> Number of common features : 11\n", "\n", "gathering and crunching for train and test datasets ...\n", "reindexing for train and test datasets ...\n", "\n", "> Number of categorical features in the training set: 5\n", "> Number of numerical features in the training set: 6\n", "> Number of training samples : 891\n", "> Number of test samples : 418\n", "\n", "> Top sparse features (% missing values on train set):\n", "Cabin       77.1\n", "Age         19.9\n", "Embarked     0.2\n", "dtype: float64\n", "\n", "> Task : classification\n", "0.0    549\n", "1.0    342\n", "Name: Survived, dtype: int64\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Apply MR feature selection with missing threshold= 0.1\n", "             missing_fraction\n", "Cabin                0.771044\n", "Age                  0.198653\n", "Embarked             0.002245\n", "Fare                 0.000000\n", "Name                 0.000000\n", "Parch                0.000000\n", "PassengerId          0.000000\n", "Pclass               0.000000\n", "Sex                  0.000000\n", "SibSp                0.000000\n", "Ticket               0.000000\n", "2 features with greater than 0.10 missing values.\n", "\n", "List of variables to be removed : ['Age', 'Cabin']\n", "List of variables to be keep\n", "['Parch', 'Ticket', 'SibSp', 'Pc<PERSON>', 'Name', 'Embarked', 'Fare', 'Sex', 'PassengerId']\n", "After feature selection:\n", "9 features remain\n", "['Parch', 'Ticket', 'SibSp', 'Pc<PERSON>', 'Name', 'Embarked', 'Fare', 'Sex', 'PassengerId']\n", "Feature selection done -- CPU time: 0.008201837539672852 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Apply LC feature selection with threshold= 0.2\n", "Correlation matrix\n", "                  Age      Fare     Parch  PassengerId    Pclass     SibSp\n", "Age          1.000000  0.096067 -0.189119     0.036847 -0.369226 -0.308247\n", "Fare         0.096067  1.000000  0.216225     0.012658 -0.549500  0.159651\n", "Parch       -0.189119  0.216225  1.000000    -0.001652  0.018443  0.414838\n", "PassengerId  0.036847  0.012658 -0.001652     1.000000 -0.035144 -0.057527\n", "Pclass      -0.369226 -0.549500  0.018443    -0.035144  1.000000  0.083081\n", "SibSp       -0.308247  0.159651  0.414838    -0.057527  0.083081  1.000000\n", "3 features with linear correlation greater than 0.20.\n", "\n", "List of correlated variables to be removed : ['Parch', 'Pclass', 'SibSp']\n", "List of numerical variables to be keep\n", "['Fare', 'Age', 'PassengerId']\n", "After feature selection:\n", "8 features remain\n", "['Ticket', 'Name', 'Embarked', 'Fare', '<PERSON>abin', 'Sex', 'Age', 'PassengerId']\n", "Feature selection done -- CPU time: 0.022053956985473633 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply L1 feature selection with threshold= 0.3\n", "After feature selection:\n", "6 features remain\n", "['Parch', 'SibSp', 'Pc<PERSON>', 'Fare', 'Age', 'PassengerId']\n", "Feature selection done -- CPU time: 0.010128974914550781 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply IMP feature selection with threshold= 0.4\n", "and keep variable SibSp\n", "After feature selection:\n", "5 features remain\n", "['SibSp', 'P<PERSON><PERSON>', 'Fare', 'Age', 'PassengerId']\n", "Feature selection done -- CPU time: 0.2556779384613037 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply Tree-based feature selection \n", "Best features to keep ['Age', 'Fare', 'PassengerId']\n", "and keep variable Pclass\n", "After feature selection:\n", "4 features remain\n", "['Fare', 'P<PERSON>lass', 'Age', 'PassengerId']\n", "Feature selection done -- CPU time: 0.068511962890625 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply WR feature selection\n", "Input variables must be non-negative. WR feature selection is only applied to positive variables.\n", "Best features to keep ['Age', 'Fare', 'Parch', 'PassengerId', 'Pclass', 'SibSp']\n", "After feature selection:\n", "6 features remain\n", "['Parch', 'SibSp', 'Pc<PERSON>', 'Fare', 'Age', 'PassengerId']\n", "Feature selection done -- CPU time: 0.014444589614868164 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply WR feature selection\n", "Input variables must be non-negative. WR feature selection is only applied to positive variables.\n", "Best features to keep ['Age', 'Fare', 'Parch', 'PassengerId', 'Pclass', 'SibSp']\n", "and keep variable Sex\n", "After feature selection:\n", "7 features remain\n", "['Parch', 'SibSp', 'Pc<PERSON>', 'Sex', 'Fare', 'Age', 'PassengerId']\n", "Feature selection done -- CPU time: 0.015102863311767578 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply SVC feature selection\n", "After feature selection:\n", "4 features remain\n", "['Fare', 'P<PERSON>lass', 'Age', 'PassengerId']\n", "Feature selection done -- CPU time: 0.011328935623168945 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Apply VAR feature selection with threshold= 0.3\n", "After feature selection:\n", "6 features remain\n", "['Parch', 'SibSp', 'Pc<PERSON>', 'Fare', 'Age', 'PassengerId']\n", "Feature selection done -- CPU time: 0.00565028190612793 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Apply VAR feature selection with threshold= 0.3\n", "and keep variable Cabin\n", "After feature selection:\n", "7 features remain\n", "['Pa<PERSON>', 'SibSp', '<PERSON><PERSON><PERSON>', '<PERSON>abin', 'Fare', 'Age', 'PassengerId']\n", "Feature selection done -- CPU time: 0.005725860595703125 seconds\n", "\n"]}, {"data": {"text/plain": ["{'train':       Age      Fare  Parch  PassengerId  Pclass  SibSp        Cabin\n", " 0    22.0    7.2500    0.0          1.0     3.0    1.0          NaN\n", " 1    38.0   71.2833    0.0          2.0     1.0    1.0          C85\n", " 2    26.0    7.9250    0.0          3.0     3.0    0.0          NaN\n", " 3    35.0   53.1000    0.0          4.0     1.0    1.0         C123\n", " 4    35.0    8.0500    0.0          5.0     3.0    0.0          NaN\n", " 5     NaN    8.4583    0.0          6.0     3.0    0.0          NaN\n", " 6    54.0   51.8625    0.0          7.0     1.0    0.0          E46\n", " 7     2.0   21.0750    1.0          8.0     3.0    3.0          NaN\n", " 8    27.0   11.1333    2.0          9.0     3.0    0.0          NaN\n", " 9    14.0   30.0708    0.0         10.0     2.0    1.0          NaN\n", " 10    4.0   16.7000    1.0         11.0     3.0    1.0           G6\n", " 11   58.0   26.5500    0.0         12.0     1.0    0.0         C103\n", " 12   20.0    8.0500    0.0         13.0     3.0    0.0          NaN\n", " 13   39.0   31.2750    5.0         14.0     3.0    1.0          NaN\n", " 14   14.0    7.8542    0.0         15.0     3.0    0.0          NaN\n", " 15   55.0   16.0000    0.0         16.0     2.0    0.0          NaN\n", " 16    2.0   29.1250    1.0         17.0     3.0    4.0          NaN\n", " 17    NaN   13.0000    0.0         18.0     2.0    0.0          NaN\n", " 18   31.0   18.0000    0.0         19.0     3.0    1.0          NaN\n", " 19    NaN    7.2250    0.0         20.0     3.0    0.0          NaN\n", " 20   35.0   26.0000    0.0         21.0     2.0    0.0          NaN\n", " 21   34.0   13.0000    0.0         22.0     2.0    0.0          D56\n", " 22   15.0    8.0292    0.0         23.0     3.0    0.0          NaN\n", " 23   28.0   35.5000    0.0         24.0     1.0    0.0           A6\n", " 24    8.0   21.0750    1.0         25.0     3.0    3.0          NaN\n", " 25   38.0   31.3875    5.0         26.0     3.0    1.0          NaN\n", " 26    NaN    7.2250    0.0         27.0     3.0    0.0          NaN\n", " 27   19.0  263.0000    2.0         28.0     1.0    3.0  C23 C25 C27\n", " 28    NaN    7.8792    0.0         29.0     3.0    0.0          NaN\n", " 29    NaN    7.8958    0.0         30.0     3.0    0.0          NaN\n", " ..    ...       ...    ...          ...     ...    ...          ...\n", " 861  21.0   11.5000    0.0        862.0     2.0    1.0          NaN\n", " 862  48.0   25.9292    0.0        863.0     1.0    0.0          D17\n", " 863   NaN   69.5500    2.0        864.0     3.0    8.0          NaN\n", " 864  24.0   13.0000    0.0        865.0     2.0    0.0          NaN\n", " 865  42.0   13.0000    0.0        866.0     2.0    0.0          NaN\n", " 866  27.0   13.8583    0.0        867.0     2.0    1.0          NaN\n", " 867  31.0   50.4958    0.0        868.0     1.0    0.0          A24\n", " 868   NaN    9.5000    0.0        869.0     3.0    0.0          NaN\n", " 869   4.0   11.1333    1.0        870.0     3.0    1.0          NaN\n", " 870  26.0    7.8958    0.0        871.0     3.0    0.0          NaN\n", " 871  47.0   52.5542    1.0        872.0     1.0    1.0          D35\n", " 872  33.0    5.0000    0.0        873.0     1.0    0.0  B51 B53 B55\n", " 873  47.0    9.0000    0.0        874.0     3.0    0.0          NaN\n", " 874  28.0   24.0000    0.0        875.0     2.0    1.0          NaN\n", " 875  15.0    7.2250    0.0        876.0     3.0    0.0          NaN\n", " 876  20.0    9.8458    0.0        877.0     3.0    0.0          NaN\n", " 877  19.0    7.8958    0.0        878.0     3.0    0.0          NaN\n", " 878   NaN    7.8958    0.0        879.0     3.0    0.0          NaN\n", " 879  56.0   83.1583    1.0        880.0     1.0    0.0          C50\n", " 880  25.0   26.0000    1.0        881.0     2.0    0.0          NaN\n", " 881  33.0    7.8958    0.0        882.0     3.0    0.0          NaN\n", " 882  22.0   10.5167    0.0        883.0     3.0    0.0          NaN\n", " 883  28.0   10.5000    0.0        884.0     2.0    0.0          NaN\n", " 884  25.0    7.0500    0.0        885.0     3.0    0.0          NaN\n", " 885  39.0   29.1250    5.0        886.0     3.0    0.0          NaN\n", " 886  27.0   13.0000    0.0        887.0     2.0    0.0          NaN\n", " 887  19.0   30.0000    0.0        888.0     1.0    0.0          B42\n", " 888   NaN   23.4500    2.0        889.0     3.0    1.0          NaN\n", " 889  26.0   30.0000    0.0        890.0     1.0    0.0         C148\n", " 890  32.0    7.7500    0.0        891.0     3.0    0.0          NaN\n", " \n", " [891 rows x 7 columns],\n", " 'test':      Parch  SibSp  Pclass            Cabin      Fare   Age  PassengerId\n", " 0      0.0    0.0     3.0              NaN    7.8292  34.5        892.0\n", " 1      0.0    1.0     3.0              NaN    7.0000  47.0        893.0\n", " 2      0.0    0.0     2.0              NaN    9.6875  62.0        894.0\n", " 3      0.0    0.0     3.0              NaN    8.6625  27.0        895.0\n", " 4      1.0    1.0     3.0              NaN   12.2875  22.0        896.0\n", " 5      0.0    0.0     3.0              NaN    9.2250  14.0        897.0\n", " 6      0.0    0.0     3.0              NaN    7.6292  30.0        898.0\n", " 7      1.0    1.0     2.0              NaN   29.0000  26.0        899.0\n", " 8      0.0    0.0     3.0              NaN    7.2292  18.0        900.0\n", " 9      0.0    2.0     3.0              NaN   24.1500  21.0        901.0\n", " 10     0.0    0.0     3.0              NaN    7.8958   NaN        902.0\n", " 11     0.0    0.0     1.0              NaN   26.0000  46.0        903.0\n", " 12     0.0    1.0     1.0              B45   82.2667  23.0        904.0\n", " 13     0.0    1.0     2.0              NaN   26.0000  63.0        905.0\n", " 14     0.0    1.0     1.0              E31   61.1750  47.0        906.0\n", " 15     0.0    1.0     2.0              NaN   27.7208  24.0        907.0\n", " 16     0.0    0.0     2.0              NaN   12.3500  35.0        908.0\n", " 17     0.0    0.0     3.0              NaN    7.2250  21.0        909.0\n", " 18     0.0    1.0     3.0              NaN    7.9250  27.0        910.0\n", " 19     0.0    0.0     3.0              NaN    7.2250  45.0        911.0\n", " 20     0.0    1.0     1.0              NaN   59.4000  55.0        912.0\n", " 21     1.0    0.0     3.0              NaN    3.1708   9.0        913.0\n", " 22     0.0    0.0     1.0              NaN   31.6833   NaN        914.0\n", " 23     1.0    0.0     1.0              NaN   61.3792  21.0        915.0\n", " 24     3.0    1.0     1.0  B57 B59 B63 B66  262.3750  48.0        916.0\n", " 25     0.0    1.0     3.0              NaN   14.5000  50.0        917.0\n", " 26     1.0    0.0     1.0              B36   61.9792  22.0        918.0\n", " 27     0.0    0.0     3.0              NaN    7.2250  22.5        919.0\n", " 28     0.0    0.0     1.0              A21   30.5000  41.0        920.0\n", " 29     0.0    2.0     3.0              NaN   21.6792   NaN        921.0\n", " ..     ...    ...     ...              ...       ...   ...          ...\n", " 388    0.0    0.0     3.0              NaN    7.7500  21.0       1280.0\n", " 389    1.0    3.0     3.0              NaN   21.0750   6.0       1281.0\n", " 390    0.0    0.0     1.0              B24   93.5000  23.0       1282.0\n", " 391    1.0    0.0     1.0              D28   39.4000  51.0       1283.0\n", " 392    2.0    0.0     3.0              NaN   20.2500  13.0       1284.0\n", " 393    0.0    0.0     2.0              NaN   10.5000  47.0       1285.0\n", " 394    1.0    3.0     3.0              NaN   22.0250  29.0       1286.0\n", " 395    0.0    1.0     1.0              C31   60.0000  18.0       1287.0\n", " 396    0.0    0.0     3.0              NaN    7.2500  24.0       1288.0\n", " 397    1.0    1.0     1.0              B41   79.2000  48.0       1289.0\n", " 398    0.0    0.0     3.0              NaN    7.7750  22.0       1290.0\n", " 399    0.0    0.0     3.0              NaN    7.7333  31.0       1291.0\n", " 400    0.0    0.0     1.0               C7  164.8667  30.0       1292.0\n", " 401    0.0    1.0     2.0              NaN   21.0000  38.0       1293.0\n", " 402    1.0    0.0     1.0              NaN   59.4000  22.0       1294.0\n", " 403    0.0    0.0     1.0              NaN   47.1000  17.0       1295.0\n", " 404    0.0    1.0     1.0              D40   27.7208  43.0       1296.0\n", " 405    0.0    0.0     2.0              D38   13.8625  20.0       1297.0\n", " 406    0.0    1.0     2.0              NaN   10.5000  23.0       1298.0\n", " 407    1.0    1.0     1.0              C80  211.5000  50.0       1299.0\n", " 408    0.0    0.0     3.0              NaN    7.7208   NaN       1300.0\n", " 409    1.0    1.0     3.0              NaN   13.7750   3.0       1301.0\n", " 410    0.0    0.0     3.0              NaN    7.7500   NaN       1302.0\n", " 411    0.0    1.0     1.0              C78   90.0000  37.0       1303.0\n", " 412    0.0    0.0     3.0              NaN    7.7750  28.0       1304.0\n", " 413    0.0    0.0     3.0              NaN    8.0500   NaN       1305.0\n", " 414    0.0    0.0     1.0             C105  108.9000  39.0       1306.0\n", " 415    0.0    0.0     3.0              NaN    7.2500  38.5       1307.0\n", " 416    0.0    0.0     3.0              NaN    8.0500   NaN       1308.0\n", " 417    1.0    1.0     3.0              NaN   22.3583   NaN       1309.0\n", " \n", " [418 rows x 7 columns],\n", " 'target': 0      0.0\n", " 1      1.0\n", " 2      1.0\n", " 3      1.0\n", " 4      0.0\n", " 5      0.0\n", " 6      0.0\n", " 7      0.0\n", " 8      1.0\n", " 9      1.0\n", " 10     1.0\n", " 11     1.0\n", " 12     0.0\n", " 13     0.0\n", " 14     0.0\n", " 15     1.0\n", " 16     0.0\n", " 17     1.0\n", " 18     0.0\n", " 19     1.0\n", " 20     0.0\n", " 21     1.0\n", " 22     1.0\n", " 23     1.0\n", " 24     0.0\n", " 25     1.0\n", " 26     0.0\n", " 27     0.0\n", " 28     1.0\n", " 29     0.0\n", "       ... \n", " 861    0.0\n", " 862    1.0\n", " 863    0.0\n", " 864    0.0\n", " 865    1.0\n", " 866    1.0\n", " 867    0.0\n", " 868    0.0\n", " 869    1.0\n", " 870    0.0\n", " 871    1.0\n", " 872    0.0\n", " 873    0.0\n", " 874    1.0\n", " 875    1.0\n", " 876    0.0\n", " 877    0.0\n", " 878    0.0\n", " 879    1.0\n", " 880    1.0\n", " 881    0.0\n", " 882    0.0\n", " 883    0.0\n", " 884    0.0\n", " 885    0.0\n", " 886    0.0\n", " 887    1.0\n", " 888    0.0\n", " 889    1.0\n", " 890    0.0\n", " Name: Survived, Length: 891, dtype: float64,\n", " 'target_test': Series([], Name: Survived, dtype: float64)}"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# >> Examples for Feature selection\n", "# Available strategies=\n", "#           'MR': using a default threshold on the missing ratio per variable,\n", "#            i.e., variables with 20% (by default) and more missing values\n", "#            are removed\n", "#            'LC': detects pairs of linearly correlated variables and remove one\n", "#            'VAR': uses threshold on the variance\n", "#            'Tree': uses decision tree classification as model for feature\n", "#                selection given the target set for classification task\n", "#                'SVC': uses linear SVC as model for feature selection given\n", "#                 the target set for classification task\n", "#            'WR': uses the selectKbest (k=10) and Chi2 for feature selection\n", "#                given the target set for classification task\n", "#            'L1': uses Lasso L1 for feature selection given the target set for\n", "#                regression task\n", "#            'IMP': uses Random Forest regression for feature selection given\n", "#                the target set for regression task\n", "\n", "                \n", "import learn2clean.loading.reader as rd \n", "import learn2clean.feature_selection.feature_selector as fs\n", "titanic  = [\"../datasets/titanic/titanic_train.csv\", \"../datasets/titanic/test.csv\"]\n", "d_not_enc = rd.Reader(sep=',',verbose=True, encoding=False) \n", "titanic_not_encoded = d_not_enc.train_test_split(titanic, 'Survived')\n", "#Available strategies for feature selection \n", "#        'MR': using a default threshold on the missing ratio per variable, i.e., variables\n", "#                with 20% (by default) and more missing values are removed\n", "#        'LC': detects pairs of linearly correlated variables and remove one\n", "#        'VAR': uses threshold on the variance\n", "#        'Tree': uses decision tree classification as model for feature selection given the target set for classification task\n", "#        'SVC': uses linear SVC as model for feature selection given the target set for classification task\n", "#        'WR': uses the selectKbest (k=10) and Chi2 for feature selection given the target set for classification task\n", "#        'L1': uses Lasso L1 for feature selection given the target set for regression task\n", "#        'IMP': uses Random Forest regression for feature selection given the target set for regression task\n", "\n", "fs.Feature_selector(dataset = titanic_not_encoded.copy(), strategy= 'MR', threshold=0.1, exclude=None, verbose=True).transform()\n", "\n", "fs.Feature_selector(dataset = titanic_not_encoded.copy(), strategy= 'LC', threshold=0.2,  exclude=None, verbose=True).transform()\n", "\n", "fs.Feature_selector(dataset = titanic_not_encoded.copy(), strategy= 'L1',  exclude= None, threshold=.3,verbose=True).transform()\n", "\n", "fs.Feature_selector(dataset = titanic_not_encoded.copy(), strategy= 'IMP', exclude = 'SibSp',verbose=True, threshold=.4).transform()\n", "\n", "fs.Feature_selector(dataset = titanic_not_encoded.copy(), strategy= 'Tree',  exclude='Pclass',verbose=True).transform()\n", "\n", "fs.Feature_selector(dataset = titanic_not_encoded.copy(), strategy= 'WR', exclude= None, verbose=True).transform()\n", "\n", "fs.Feature_selector(dataset = titanic_not_encoded.copy(), strategy= 'WR', exclude= 'Sex', verbose=True).transform()\n", "\n", "fs.Feature_selector(dataset = titanic_not_encoded.copy(), strategy= 'SVC',  exclude=None).transform()\n", "\n", "fs.Feature_selector(dataset = titanic_not_encoded.copy(), strategy= 'VAR',  exclude=None).transform()\n", "\n", "fs.Feature_selector(dataset = titanic_not_encoded.copy(), strategy= 'VAR',  exclude='Cabin').transform()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## >> Classification "]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.7002801120448179\n", "\n", "Classification done -- CPU time: 0.0186312198638916 seconds\n", "\n", ">>Classification task\n", "Accuracy of Multinomial Naive Bayes classification for 10 cross-validation : 0.653\n", "\n", "Classification done -- CPU time: 1.9718081951141357 seconds\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6680672268907563\n", "\n", "Classification done -- CPU time: 0.030199050903320312 seconds\n", "\n", ">>Classification task\n", "{'mean_fit_time': array([0.00119755, 0.00142217, 0.00156717, 0.00172107, 0.00180094]), 'std_fit_time': array([4.62359281e-05, 4.82917406e-05, 3.13430762e-05, 2.82700601e-05,\n", "       5.98741210e-05]), 'mean_score_time': array([0.00027101, 0.00025697, 0.00025885, 0.00026007, 0.00026159]), 'std_score_time': array([2.35376139e-05, 3.35958766e-06, 5.37584994e-06, 5.55244060e-06,\n", "       5.72581961e-06]), 'param_max_depth': masked_array(data=[3, 5, 7, 9, 10],\n", "             mask=[<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>],\n", "       fill_value='?',\n", "            dtype=object), 'params': [{'max_depth': 3}, {'max_depth': 5}, {'max_depth': 7}, {'max_depth': 9}, {'max_depth': 10}], 'split0_test_score': array([0.56944444, 0.58333333, 0.54166667, 0.48611111, 0.47222222]), 'split1_test_score': array([0.75      , 0.68055556, 0.59722222, 0.56944444, 0.58333333]), 'split2_test_score': array([0.73611111, 0.69444444, 0.69444444, 0.69444444, 0.65277778]), 'split3_test_score': array([0.70833333, 0.68055556, 0.63888889, 0.65277778, 0.625     ]), 'split4_test_score': array([0.70422535, 0.73239437, 0.67605634, 0.70422535, 0.66197183]), 'split5_test_score': array([0.67605634, 0.67605634, 0.70422535, 0.69014085, 0.67605634]), 'split6_test_score': array([0.66197183, 0.67605634, 0.69014085, 0.69014085, 0.66197183]), 'split7_test_score': array([0.71830986, 0.70422535, 0.71830986, 0.64788732, 0.73239437]), 'split8_test_score': array([0.70422535, 0.70422535, 0.67605634, 0.64788732, 0.67605634]), 'split9_test_score': array([0.78873239, 0.76056338, 0.71830986, 0.71830986, 0.71830986]), 'mean_test_score': array([0.70168067, 0.68907563, 0.66526611, 0.64985994, 0.64565826]), 'std_test_score': array([0.05592145, 0.04380901, 0.05439313, 0.06812188, 0.07058898]), 'rank_test_score': array([1, 2, 3, 4, 5], dtype=int32), 'split0_train_score': array([0.75077882, 0.79595016, 0.85669782, 0.92056075, 0.9470405 ]), 'split1_train_score': array([0.73831776, 0.7694704 , 0.83956386, 0.90031153, 0.92367601]), 'split2_train_score': array([0.7305296 , 0.78816199, 0.8317757 , 0.89096573, 0.90965732]), 'split3_train_score': array([0.7305296 , 0.79750779, 0.84890966, 0.89719626, 0.92056075]), 'split4_train_score': array([0.7340591 , 0.77760498, 0.83825816, 0.88335925, 0.90202177]), 'split5_train_score': array([0.75427683, 0.79315708, 0.83203733, 0.86936236, 0.88646967]), 'split6_train_score': array([0.73872473, 0.77916019, 0.81959565, 0.87713841, 0.90513219]), 'split7_train_score': array([0.74183515, 0.7807154 , 0.81804044, 0.87713841, 0.90046656]), 'split8_train_score': array([0.74650078, 0.78227061, 0.82426128, 0.87713841, 0.91135303]), 'split9_train_score': array([0.73716952, 0.79004666, 0.83048212, 0.88180404, 0.90357698]), 'mean_train_score': array([0.74027219, 0.78540452, 0.8339622 , 0.88749752, 0.91099548]), 'std_train_score': array([0.00769439, 0.00855853, 0.01168235, 0.01436603, 0.01560694])}\n", "Avg accuracy of CART classification for 10 cross-validation : 0.7016806722689075\n", "\n", "Classification done -- CPU time: 13.200136184692383 seconds\n"]}], "source": ["import learn2clean.classification.classifier as cl\n", "#output is accuracy of classification for k=10 cross-validation and execution time \n", "#plus a detailed classification report if verbose = True\n", "\n", "Cl1 = cl.Classifier(dataset = titanic_not_encoded,target = 'Survived',strategy = 'LDA', verbose = False).transform()\n", "Cl2 = cl.Classifier(dataset = titanic_not_encoded,target = 'Survived',strategy = 'MNB',verbose = False).transform()\n", "Cl3 = cl.Classifier(dataset = titanic_not_encoded,target = 'Survived',strategy = 'NB',verbose = False).transform()\n", "Cl4 = cl.Classifier(dataset = titanic_not_encoded,target = 'Survived',strategy = 'CART',verbose = True).transform()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## >> Regression"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", ">>Regression task\n", "MSE values of cross validation\n", "[[0.33428799 0.19545235 0.27222477 0.25590979 0.26467421 0.24275834\n", "  0.22934358 0.22435433 0.23116173 0.24046703]\n", " [0.33381358 0.19613092 0.2702665  0.25316562 0.26448743 0.24257295\n", "  0.22936945 0.22496779 0.23169864 0.23734665]\n", " [0.33336004 0.1972071  0.26855447 0.25059992 0.26438711 0.24251741\n", "  0.22949092 0.22566054 0.23237851 0.2344432 ]\n", " [0.32786341 0.19956412 0.26645376 0.24709295 0.26439872 0.24267824\n", "  0.22985272 0.2268461  0.23366493 0.23050614]\n", " [0.32029113 0.20221071 0.26513165 0.24448445 0.26455681 0.24303609\n", "  0.23031892 0.22796896 0.23498205 0.22760982]\n", " [0.31605777 0.20409598 0.26452545 0.24305575 0.26471675 0.24334843\n", "  0.23067047 0.22870123 0.23587898 0.22603889]\n", " [0.31469755 0.20477317 0.26435474 0.24260224 0.26507631 0.24357785\n", "  0.23369878 0.22991413 0.23603975 0.2257568 ]\n", " [0.31170511 0.20394425 0.26067606 0.24021353 0.26580729 0.24317463\n", "  0.2372156  0.23768063 0.23460895 0.2254377 ]\n", " [0.30984331 0.20400398 0.25861438 0.23977547 0.26609464 0.24278416\n", "  0.23800362 0.23958874 0.23297182 0.22521051]\n", " [0.30656947 0.20442601 0.2547075  0.23897988 0.26693846 0.24214824\n", "  0.24062896 0.24375994 0.22985248 0.2249785 ]\n", " [0.30184787 0.20476756 0.25158197 0.23790219 0.26833099 0.24134861\n", "  0.2464445  0.24875498 0.22789703 0.22463639]]\n", "alphas vs. MSE in cross-validation\n", "    alpha       MSE\n", "0   1.200  0.249063\n", "1   1.000  0.248382\n", "2   0.800  0.247860\n", "3   0.500  0.246892\n", "4   0.250  0.246059\n", "5   0.100  0.245709\n", "6   0.050  0.246049\n", "7   0.025  0.246046\n", "8   0.020  0.245689\n", "9   0.010  0.245299\n", "10  0.001  0.245351\n", "Best alpha =  0.01\n", "MSE of LASSO with 10  folds for cross-validation: 0.24529894457206738\n", "Regression done -- CPU time: 0.03478717803955078 seconds\n", "\n", ">>Regression task\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:               Survived   R-squared:                       0.195\n", "Model:                            OLS   Adj. R-squared:                  0.188\n", "Method:                 Least Squares   F-statistic:                     28.54\n", "Date:                Thu, 28 Mar 2019   Prob (F-statistic):           1.23e-30\n", "Time:                        09:15:01   Log-Likelihood:                -427.98\n", "No. Observations:                 714   AIC:                             870.0\n", "Df Residuals:                     707   BIC:                             902.0\n", "Df Model:                           6                                         \n", "Covariance Type:            nonrobust                                         \n", "===============================================================================\n", "                  coef    std err          t      P>|t|      [0.025      0.975]\n", "-------------------------------------------------------------------------------\n", "const           1.1796      0.094     12.519      0.000       0.995       1.365\n", "Age            -0.0086      0.001     -6.610      0.000      -0.011      -0.006\n", "Fare            0.0006      0.000      1.418      0.157      -0.000       0.001\n", "Parch           0.0490      0.022      2.273      0.023       0.007       0.091\n", "PassengerId  2.959e-05   6.43e-05      0.461      0.645   -9.66e-05       0.000\n", "Pclass         -0.2429      0.026     -9.397      0.000      -0.294      -0.192\n", "SibSp          -0.0567      0.020     -2.810      0.005      -0.096      -0.017\n", "==============================================================================\n", "Omnibus:                      209.321   <PERSON><PERSON><PERSON><PERSON><PERSON>:                   1.920\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):               47.347\n", "Skew:                           0.348   Prob(JB):                     5.23e-11\n", "Kurtosis:                       1.947   Cond. No.                     3.03e+03\n", "==============================================================================\n", "\n", "Warnings:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "[2] The condition number is large, 3.03e+03. This might indicate that there are\n", "strong multicollinearity or other numerical problems.\n", "Regression done -- CPU time: 0.026846885681152344 seconds\n"]}], "source": ["import learn2clean.regression.regressor as rg\n", "# output is MSE and computation time, with regression summary if verbose = True\n", "rg1 = rg.Regressor(dataset = titanic_not_encoded,target = 'Survived',strategy= 'LASSO', verbose = True).transform()\n", "\n", "rg2 = rg.Regressor(dataset = titanic_not_encoded,target = 'Survived',strategy= 'OLS',verbose = True).transform()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## >> Clustering"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", ">>Clustering task\n", "Note: The clustering is applied on the training dataset only.\n", "Best silhouette = 0.5976  for k= 2\n", "Quality of clustering 0.5976\n", "Labels distribution:\n", "0    363\n", "1    351\n", "Name: cluster_ID, dtype: int64\n", "Clustering done -- CPU time: 1.173151969909668 seconds\n", "\n", ">>Clustering task\n", "Note: The clustering is applied on the training dataset only.\n", "Best silhouette = 0.5544  for k= 3\n", "Quality of clustering 0.5544\n", "Labels distribution:\n", "1    455\n", "2    256\n", "0      3\n", "Name: cluster_ID, dtype: int64\n", "Clustering done -- CPU time: 0.09331917762756348 seconds\n", "\n", ">>Clustering task\n", "Note: The clustering is applied on the training dataset only.\n", "Best silhouette = 0.5544  for k= 3\n", "Quality of clustering 0.5544\n", "Labels distribution:\n", "1    455\n", "2    256\n", "0      3\n", "Name: cluster_ID, dtype: int64\n", "Clustering done -- CPU time: 0.0819709300994873 seconds\n", "\n", ">>Clustering task\n", "Note: The clustering is applied on the training dataset only.\n", "Best silhouette = 0.5544  for k= 3\n", "Quality of clustering 0.1868\n", "Labels distribution:\n", "1    678\n", "2     18\n", "0     18\n", "Name: cluster_ID, dtype: int64\n", "Clustering done -- CPU time: 0.09133601188659668 seconds\n", "\n", ">>Clustering task\n", "Note: The clustering is applied on the training dataset only.\n", "Best silhouette = 0.5544  for k= 3\n", "Quality of clustering 0.5935\n", "Labels distribution:\n", "2    358\n", "1    353\n", "0      3\n", "Name: cluster_ID, dtype: int64\n", "Clustering done -- CPU time: 0.0744009017944336 seconds\n"]}, {"data": {"text/plain": ["{'quality_metric': 0.5935,\n", " 'result': {'train':       Age      Fare  Parch  PassengerId  Pclass  SibSp  cluster_ID\n", "  0    22.0    7.2500    0.0          1.0     3.0    1.0           1\n", "  1    38.0   71.2833    0.0          2.0     1.0    1.0           1\n", "  2    26.0    7.9250    0.0          3.0     3.0    0.0           1\n", "  3    35.0   53.1000    0.0          4.0     1.0    1.0           1\n", "  4    35.0    8.0500    0.0          5.0     3.0    0.0           1\n", "  6    54.0   51.8625    0.0          7.0     1.0    0.0           1\n", "  7     2.0   21.0750    1.0          8.0     3.0    3.0           1\n", "  8    27.0   11.1333    2.0          9.0     3.0    0.0           1\n", "  9    14.0   30.0708    0.0         10.0     2.0    1.0           1\n", "  10    4.0   16.7000    1.0         11.0     3.0    1.0           1\n", "  11   58.0   26.5500    0.0         12.0     1.0    0.0           1\n", "  12   20.0    8.0500    0.0         13.0     3.0    0.0           1\n", "  13   39.0   31.2750    5.0         14.0     3.0    1.0           1\n", "  14   14.0    7.8542    0.0         15.0     3.0    0.0           1\n", "  15   55.0   16.0000    0.0         16.0     2.0    0.0           1\n", "  16    2.0   29.1250    1.0         17.0     3.0    4.0           1\n", "  18   31.0   18.0000    0.0         19.0     3.0    1.0           1\n", "  20   35.0   26.0000    0.0         21.0     2.0    0.0           1\n", "  21   34.0   13.0000    0.0         22.0     2.0    0.0           1\n", "  22   15.0    8.0292    0.0         23.0     3.0    0.0           1\n", "  23   28.0   35.5000    0.0         24.0     1.0    0.0           1\n", "  24    8.0   21.0750    1.0         25.0     3.0    3.0           1\n", "  25   38.0   31.3875    5.0         26.0     3.0    1.0           1\n", "  27   19.0  263.0000    2.0         28.0     1.0    3.0           1\n", "  30   40.0   27.7208    0.0         31.0     1.0    0.0           1\n", "  33   66.0   10.5000    0.0         34.0     2.0    0.0           1\n", "  34   28.0   82.1708    0.0         35.0     1.0    1.0           1\n", "  35   42.0   52.0000    0.0         36.0     1.0    1.0           1\n", "  37   21.0    8.0500    0.0         38.0     3.0    0.0           1\n", "  38   18.0   18.0000    0.0         39.0     3.0    2.0           1\n", "  ..    ...       ...    ...          ...     ...    ...         ...\n", "  856  45.0  164.8667    1.0        857.0     1.0    1.0           2\n", "  857  51.0   26.5500    0.0        858.0     1.0    0.0           2\n", "  858  24.0   19.2583    3.0        859.0     3.0    0.0           2\n", "  860  41.0   14.1083    0.0        861.0     3.0    2.0           2\n", "  861  21.0   11.5000    0.0        862.0     2.0    1.0           2\n", "  862  48.0   25.9292    0.0        863.0     1.0    0.0           2\n", "  864  24.0   13.0000    0.0        865.0     2.0    0.0           2\n", "  865  42.0   13.0000    0.0        866.0     2.0    0.0           2\n", "  866  27.0   13.8583    0.0        867.0     2.0    1.0           2\n", "  867  31.0   50.4958    0.0        868.0     1.0    0.0           2\n", "  869   4.0   11.1333    1.0        870.0     3.0    1.0           2\n", "  870  26.0    7.8958    0.0        871.0     3.0    0.0           2\n", "  871  47.0   52.5542    1.0        872.0     1.0    1.0           2\n", "  872  33.0    5.0000    0.0        873.0     1.0    0.0           2\n", "  873  47.0    9.0000    0.0        874.0     3.0    0.0           2\n", "  874  28.0   24.0000    0.0        875.0     2.0    1.0           2\n", "  875  15.0    7.2250    0.0        876.0     3.0    0.0           2\n", "  876  20.0    9.8458    0.0        877.0     3.0    0.0           2\n", "  877  19.0    7.8958    0.0        878.0     3.0    0.0           2\n", "  879  56.0   83.1583    1.0        880.0     1.0    0.0           2\n", "  880  25.0   26.0000    1.0        881.0     2.0    0.0           2\n", "  881  33.0    7.8958    0.0        882.0     3.0    0.0           2\n", "  882  22.0   10.5167    0.0        883.0     3.0    0.0           2\n", "  883  28.0   10.5000    0.0        884.0     2.0    0.0           2\n", "  884  25.0    7.0500    0.0        885.0     3.0    0.0           2\n", "  885  39.0   29.1250    5.0        886.0     3.0    0.0           2\n", "  886  27.0   13.0000    0.0        887.0     2.0    0.0           2\n", "  887  19.0   30.0000    0.0        888.0     1.0    0.0           2\n", "  889  26.0   30.0000    0.0        890.0     1.0    0.0           2\n", "  890  32.0    7.7500    0.0        891.0     3.0    0.0           2\n", "  \n", "  [714 rows x 7 columns],\n", "  'test':       <PERSON> Embarked      Far<PERSON>  \\\n", "  0    34.5              NaN        Q    7.8292   \n", "  1    47.0              NaN        S    7.0000   \n", "  2    62.0              NaN        Q    9.6875   \n", "  3    27.0              NaN        S    8.6625   \n", "  4    22.0              NaN        S   12.2875   \n", "  5    14.0              NaN        S    9.2250   \n", "  6    30.0              NaN        Q    7.6292   \n", "  7    26.0              NaN        S   29.0000   \n", "  8    18.0              NaN        C    7.2292   \n", "  9    21.0              NaN        S   24.1500   \n", "  10    NaN              NaN        S    7.8958   \n", "  11   46.0              NaN        S   26.0000   \n", "  12   23.0              B45        S   82.2667   \n", "  13   63.0              NaN        S   26.0000   \n", "  14   47.0              E31        S   61.1750   \n", "  15   24.0              NaN        C   27.7208   \n", "  16   35.0              NaN        Q   12.3500   \n", "  17   21.0              NaN        C    7.2250   \n", "  18   27.0              NaN        S    7.9250   \n", "  19   45.0              NaN        C    7.2250   \n", "  20   55.0              NaN        C   59.4000   \n", "  21    9.0              NaN        S    3.1708   \n", "  22    NaN              NaN        S   31.6833   \n", "  23   21.0              NaN        C   61.3792   \n", "  24   48.0  B57 B59 B63 B66        C  262.3750   \n", "  25   50.0              NaN        S   14.5000   \n", "  26   22.0              B36        C   61.9792   \n", "  27   22.5              NaN        C    7.2250   \n", "  28   41.0              A21        S   30.5000   \n", "  29    NaN              NaN        C   21.6792   \n", "  ..    ...              ...      ...       ...   \n", "  388  21.0              NaN        Q    7.7500   \n", "  389   6.0              NaN        S   21.0750   \n", "  390  23.0              B24        S   93.5000   \n", "  391  51.0              D28        S   39.4000   \n", "  392  13.0              NaN        S   20.2500   \n", "  393  47.0              NaN        S   10.5000   \n", "  394  29.0              NaN        S   22.0250   \n", "  395  18.0              C31        S   60.0000   \n", "  396  24.0              NaN        Q    7.2500   \n", "  397  48.0              B41        C   79.2000   \n", "  398  22.0              NaN        S    7.7750   \n", "  399  31.0              NaN        Q    7.7333   \n", "  400  30.0               C7        S  164.8667   \n", "  401  38.0              NaN        S   21.0000   \n", "  402  22.0              NaN        C   59.4000   \n", "  403  17.0              NaN        S   47.1000   \n", "  404  43.0              D40        C   27.7208   \n", "  405  20.0              D38        C   13.8625   \n", "  406  23.0              NaN        S   10.5000   \n", "  407  50.0              C80        C  211.5000   \n", "  408   NaN              NaN        Q    7.7208   \n", "  409   3.0              NaN        S   13.7750   \n", "  410   NaN              NaN        Q    7.7500   \n", "  411  37.0              C78        Q   90.0000   \n", "  412  28.0              NaN        S    7.7750   \n", "  413   NaN              NaN        S    8.0500   \n", "  414  39.0             C105        C  108.9000   \n", "  415  38.5              NaN        S    7.2500   \n", "  416   NaN              NaN        S    8.0500   \n", "  417   NaN              NaN        C   22.3583   \n", "  \n", "                                                                  Name  Parch  \\\n", "  0                                                   <PERSON>, <PERSON><PERSON>    0.0   \n", "  1                                   <PERSON>, Mrs. <PERSON> (<PERSON>)    0.0   \n", "  2                                          <PERSON><PERSON>, Mr. <PERSON>    0.0   \n", "  3                                                   <PERSON><PERSON><PERSON>, <PERSON><PERSON>    0.0   \n", "  4                       <PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON><PERSON>)    1.0   \n", "  5                                         <PERSON><PERSON>, <PERSON>. <PERSON>    0.0   \n", "  6                                               <PERSON>, <PERSON><PERSON>    0.0   \n", "  7                                       <PERSON>, Mr. <PERSON>    1.0   \n", "  8                          <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)    0.0   \n", "  9                                            <PERSON>, Mr. <PERSON>    0.0   \n", "  10                                                  <PERSON><PERSON><PERSON>, <PERSON><PERSON>    0.0   \n", "  11                                        <PERSON>, Mr. <PERSON>    0.0   \n", "  12                     <PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)    0.0   \n", "  13                                              <PERSON>, Mr. <PERSON>    0.0   \n", "  14           <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)    0.0   \n", "  <PERSON>                     <PERSON>, Mrs<PERSON> <PERSON> (<PERSON><PERSON><PERSON>)    0.0   \n", "  16                                                 <PERSON><PERSON>, <PERSON><PERSON> <PERSON>    0.0   \n", "  17                                                 <PERSON><PERSON><PERSON>, <PERSON><PERSON>    0.0   \n", "  18                                      <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>    0.0   \n", "  19                             <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>\")\"    0.0   \n", "  20                                            <PERSON>, <PERSON><PERSON>    0.0   \n", "  21                                         <PERSON>, <PERSON><PERSON> <PERSON><PERSON>    1.0   \n", "  22                              <PERSON><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON><PERSON>)    0.0   \n", "  23                                   <PERSON>, Mr. <PERSON> II    1.0   \n", "  24                   <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)    3.0   \n", "  25                                           <PERSON><PERSON>, <PERSON>. <PERSON> A    0.0   \n", "  26                                      <PERSON><PERSON><PERSON>, <PERSON><PERSON>    1.0   \n", "  27                                                 <PERSON><PERSON>, <PERSON><PERSON>    0.0   \n", "  28                                           <PERSON>, Mr. <PERSON>    0.0   \n", "  29                                                 <PERSON><PERSON>, <PERSON><PERSON>    0.0   \n", "  ..                                                               ...    ...   \n", "  388                                             <PERSON><PERSON>, <PERSON><PERSON>    0.0   \n", "  389                                      <PERSON><PERSON>, <PERSON><PERSON>    1.0   \n", "  390                                       <PERSON>, Mr. <PERSON>    0.0   \n", "  391                   Lines, Mrs. <PERSON> (<PERSON>)    1.0   \n", "  392                                    <PERSON>, <PERSON><PERSON>    2.0   \n", "  393                                             <PERSON>, Mr. <PERSON>    0.0   \n", "  394                                         <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>    1.0   \n", "  395                   <PERSON>, Mrs. <PERSON> (<PERSON>)    0.0   \n", "  396                                             <PERSON>, <PERSON><PERSON>    0.0   \n", "  397  Fr<PERSON><PERSON>-<PERSON><PERSON><PERSON>, Mrs. <PERSON><PERSON> (<PERSON><PERSON>)    1.0   \n", "  398                                   <PERSON><PERSON>-<PERSON><PERSON><PERSON>, Mr. <PERSON><PERSON>    0.0   \n", "  399                                         <PERSON><PERSON>, Mr. <PERSON>    0.0   \n", "  400                                          <PERSON><PERSON>, <PERSON><PERSON>    0.0   \n", "  401                                                  <PERSON>, Mr. <PERSON>    0.0   \n", "  402                                   <PERSON>, <PERSON><PERSON>    1.0   \n", "  403                                           <PERSON><PERSON>, Mr. <PERSON>    0.0   \n", "  404                                     <PERSON><PERSON><PERSON><PERSON>, Mr. <PERSON>    0.0   \n", "  405                     <PERSON><PERSON>, Mr. <PERSON> (Baron <PERSON>\")\"    0.0   \n", "  406                                        <PERSON>, Mr. <PERSON>    0.0   \n", "  407                                       <PERSON><PERSON>, Mr. <PERSON>    1.0   \n", "  408                                  <PERSON><PERSON><PERSON>, <PERSON><PERSON>\"\"    0.0   \n", "  409                                        <PERSON>, <PERSON><PERSON>    1.0   \n", "  410                                           <PERSON><PERSON><PERSON>, <PERSON><PERSON>    0.0   \n", "  411                  <PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)    0.0   \n", "  412                                   <PERSON><PERSON>, <PERSON><PERSON>    0.0   \n", "  413                                               <PERSON><PERSON><PERSON>, Mr<PERSON>    0.0   \n", "  414                                     <PERSON><PERSON>, Dona. Fermina    0.0   \n", "  415                                     <PERSON>, Mr. <PERSON>    0.0   \n", "  416                                              <PERSON>, Mr. <PERSON>    0.0   \n", "  417                                         <PERSON>, <PERSON><PERSON>    1.0   \n", "  \n", "       PassengerId  Pclass     Sex  SibSp              Ticket  \n", "  0          892.0     3.0    male    0.0              330911  \n", "  1          893.0     3.0  female    1.0              363272  \n", "  2          894.0     2.0    male    0.0              240276  \n", "  3          895.0     3.0    male    0.0              315154  \n", "  4          896.0     3.0  female    1.0             3101298  \n", "  5          897.0     3.0    male    0.0                7538  \n", "  6          898.0     3.0  female    0.0              330972  \n", "  7          899.0     2.0    male    1.0              248738  \n", "  8          900.0     3.0  female    0.0                2657  \n", "  9          901.0     3.0    male    2.0           A/4 48871  \n", "  10         902.0     3.0    male    0.0              349220  \n", "  11         903.0     1.0    male    0.0                 694  \n", "  12         904.0     1.0  female    1.0               21228  \n", "  13         905.0     2.0    male    1.0               24065  \n", "  14         906.0     1.0  female    1.0         W.E.P. 5734  \n", "  15         907.0     2.0  female    1.0       SC/PARIS 2167  \n", "  16         908.0     2.0    male    0.0              233734  \n", "  17         909.0     3.0    male    0.0                2692  \n", "  18         910.0     3.0  female    1.0    STON/O2. 3101270  \n", "  19         911.0     3.0  female    0.0                2696  \n", "  20         912.0     1.0    male    1.0            PC 17603  \n", "  21         913.0     3.0    male    0.0             C 17368  \n", "  22         914.0     1.0  female    0.0            PC 17598  \n", "  23         915.0     1.0    male    0.0            PC 17597  \n", "  24         916.0     1.0  female    1.0            PC 17608  \n", "  25         917.0     3.0    male    1.0           A/5. 3337  \n", "  26         918.0     1.0  female    0.0              113509  \n", "  27         919.0     3.0    male    0.0                2698  \n", "  28         920.0     1.0    male    0.0              113054  \n", "  29         921.0     3.0    male    2.0                2662  \n", "  ..           ...     ...     ...    ...                 ...  \n", "  388       1280.0     3.0    male    0.0              364858  \n", "  389       1281.0     3.0    male    3.0              349909  \n", "  390       1282.0     1.0    male    0.0               12749  \n", "  391       1283.0     1.0  female    0.0            PC 17592  \n", "  392       1284.0     3.0    male    0.0           C.A. 2673  \n", "  393       1285.0     2.0    male    0.0          C.A. 30769  \n", "  394       1286.0     3.0    male    3.0              315153  \n", "  395       1287.0     1.0  female    1.0               13695  \n", "  396       1288.0     3.0    male    0.0              371109  \n", "  397       1289.0     1.0  female    1.0               13567  \n", "  398       1290.0     3.0    male    0.0              347065  \n", "  399       1291.0     3.0    male    0.0               21332  \n", "  400       1292.0     1.0  female    0.0               36928  \n", "  401       1293.0     2.0    male    1.0               28664  \n", "  402       1294.0     1.0  female    0.0              112378  \n", "  403       1295.0     1.0    male    0.0              113059  \n", "  404       1296.0     1.0    male    1.0               17765  \n", "  405       1297.0     2.0    male    0.0       SC/PARIS 2166  \n", "  406       1298.0     2.0    male    1.0               28666  \n", "  407       1299.0     1.0    male    1.0              113503  \n", "  408       1300.0     3.0  female    0.0              334915  \n", "  409       1301.0     3.0  female    1.0  SOTON/O.Q. 3101315  \n", "  410       1302.0     3.0  female    0.0              365237  \n", "  411       1303.0     1.0  female    1.0               19928  \n", "  412       1304.0     3.0  female    0.0              347086  \n", "  413       1305.0     3.0    male    0.0           A.5. 3236  \n", "  414       1306.0     1.0  female    0.0            PC 17758  \n", "  415       1307.0     3.0    male    0.0  SOTON/O.Q. 3101262  \n", "  416       1308.0     3.0    male    0.0              359309  \n", "  417       1309.0     3.0    male    1.0                2668  \n", "  \n", "  [418 rows x 11 columns],\n", "  'target': 0      0.0\n", "  1      1.0\n", "  2      1.0\n", "  3      1.0\n", "  4      0.0\n", "  5      0.0\n", "  6      0.0\n", "  7      0.0\n", "  8      1.0\n", "  9      1.0\n", "  10     1.0\n", "  11     1.0\n", "  12     0.0\n", "  13     0.0\n", "  14     0.0\n", "  15     1.0\n", "  16     0.0\n", "  17     1.0\n", "  18     0.0\n", "  19     1.0\n", "  20     0.0\n", "  21     1.0\n", "  22     1.0\n", "  23     1.0\n", "  24     0.0\n", "  25     1.0\n", "  26     0.0\n", "  27     0.0\n", "  28     1.0\n", "  29     0.0\n", "        ... \n", "  861    0.0\n", "  862    1.0\n", "  863    0.0\n", "  864    0.0\n", "  865    1.0\n", "  866    1.0\n", "  867    0.0\n", "  868    0.0\n", "  869    1.0\n", "  870    0.0\n", "  871    1.0\n", "  872    0.0\n", "  873    0.0\n", "  874    1.0\n", "  875    1.0\n", "  876    0.0\n", "  877    0.0\n", "  878    0.0\n", "  879    1.0\n", "  880    1.0\n", "  881    0.0\n", "  882    0.0\n", "  883    0.0\n", "  884    0.0\n", "  885    0.0\n", "  886    0.0\n", "  887    1.0\n", "  888    0.0\n", "  889    1.0\n", "  890    0.0\n", "  Name: Survived, Length: 891, dtype: float64,\n", "  'target_test': Series([], Name: Survived, dtype: float64)}}"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["import learn2clean.clustering.clusterer as ct\n", "# clustering is applied to one dataset (i.e., the training set if two datasets are given in the path)\n", "# output is silhouette, best k, and computation time, plus the training dataset with cluster IDs\n", "\n", "ct.Clusterer(dataset = titanic_not_encoded,strategy= 'KMEANS', verbose=True).transform()\n", "ct.Clusterer(dataset = titanic_not_encoded,strategy='HCA', verbose = True).transform()\n", "ct.Clusterer(dataset = titanic_not_encoded,strategy='HCA', metric= 'euclidean', verbose = True).transform()\n", "ct.Clusterer(dataset = titanic_not_encoded,strategy='HCA', metric= 'cosine', verbose = True).transform()\n", "ct.Clusterer(dataset = titanic_not_encoded,strategy='HCA', metric= 'cityblock', verbose = True).transform()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Create your own pipeline"]}, {"cell_type": "code", "execution_count": 24, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Apply MR feature selection with missing threshold= 0.3\n", "1 features with greater than 0.30 missing values.\n", "\n", "List of variables to be removed : ['Cabin']\n", "After feature selection:\n", "10 features remain\n", "['Parch', 'Ticket', 'SibSp', 'Pc<PERSON>', 'Name', 'Embarked', 'Fare', 'Sex', 'Age', 'PassengerId']\n", "Feature selection done -- CPU time: 0.007447242736816406 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 179 missing values in ['Embarked', 'Age']\n", "- 177 numerical missing values in ['Age']\n", "- 2 non-numerical missing values in ['Embarked']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 87 missing values in ['Fare', 'Age']\n", "- 87 numerical missing values in ['Fare', 'Age']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "Imputation done -- CPU time: 0.08392596244812012 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.034832000732421875 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "60 outlying rows have been removed\n", "* For test dataset\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.020370960235595703 seconds\n", "\n", "\n", ">>Classification task\n", "{'mean_fit_time': array([0.00162196]), 'std_fit_time': array([7.15255737e-06]), 'mean_score_time': array([0.00030613]), 'std_score_time': array([1.90734863e-05]), 'params': [{}], 'split0_test_score': array([0.69230769]), 'split1_test_score': array([0.72048193]), 'mean_test_score': array([0.70637786]), 'std_test_score': array([0.01408711]), 'rank_test_score': array([1], dtype=int32), 'split0_train_score': array([0.71566265]), 'split1_train_score': array([0.71634615]), 'mean_train_score': array([0.7160044]), 'std_train_score': array([0.00034175])}\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.7063778580024067\n", "\n", "Classification done -- CPU time: 0.01895880699157715 seconds\n"]}, {"data": {"text/plain": ["{'quality_metric': 0.7063778580024067}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# create your preprocessing pipeline for classification\n", "\n", "import learn2clean.loading.reader as rd \n", "import learn2clean.normalization.normalizer as nl \n", "import learn2clean.feature_selection.feature_selector as fs\n", "import learn2clean.duplicate_detection.duplicate_detector as dd\n", "import learn2clean.outlier_detection.outlier_detector as od\n", "import learn2clean.imputation.imputer as imp\n", "import learn2clean.classification.classifier as cl\n", "\n", "d_not_enc = rd.Reader(sep=',',verbose=False, encoding=False) \n", "\n", "# when you have two datasets as inputs: train and test datasets\n", "titanic  = [\"../datasets/titanic/titanic_train.csv\", \"../datasets/titanic/test.csv\"]\n", "dataset = d_not_enc.train_test_split(titanic, 'Survived')\n", "\n", "dataset['train']['Pclass'] = dataset['train']['Pclass'].astype('object')\n", "dataset['train']['PassengerId'] = dataset['train']['PassengerId'].astype('object')\n", "dataset['train']['SibSp'] = dataset['train']['SibSp'].astype('object')\n", "dataset['train']['Parch'] = dataset['train']['Parch'].astype('object')\n", "\n", "dataset['test']['Pclass'] = dataset['test']['Pclass'].astype('object')\n", "dataset['test']['PassengerId'] = dataset['test']['PassengerId'].astype('object')\n", "dataset['test']['SibSp'] = dataset['test']['SibSp'].astype('object')\n", "dataset['test']['Parch'] = dataset['test']['Parch'].astype('object')\n", "\n", "d1 = fs.Feature_selector(dataset=dataset.copy(),strategy= 'MR', threshold=.3).transform()\n", "d2 = imp.Imputer(d1, strategy = 'MF',verbose=False).transform()\n", "d3 = nl.Normalizer(d2,strategy='DS', exclude='Survived',verbose=False).transform()\n", "d4 = od.Outlier_detector(d3, strategy='LOF', threshold= .6,verbose=False).transform()\n", "cl.Classifier(d4,strategy = 'LDA', target = 'Survived', verbose =True).transform()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Learn2clean data preprocessing pipeline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Classification with Learn2Clean"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Start Learn2Clean\n", "Learn2Clean - Pipeline construction -- CPU time: 0.13695597648620605 seconds\n", "=== Start Pipeline Execution ===\n", "\n", "\n", "Strategy# 0 : Greedy traversal for starting state MICE\n", "MICE -> CART\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.09270310401916504 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.7048260381593715\n", "\n", "Classification done -- CPU time: 13.596114873886108 seconds\n", "End Pipeline CPU time: 13.696760892868042 seconds\n", "\n", "\n", "Strategy# 1 : Greedy traversal for starting state EM\n", "EM -> MM -> IQR -> CART\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.1823441982269287 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.0203399658203125 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed\n", "* For test dataset\n", "0 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.023466110229492188 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.7014590347923682\n", "\n", "Classification done -- CPU time: 13.504812955856323 seconds\n", "End Pipeline CPU time: 13.749058961868286 seconds\n", "\n", "\n", "Strategy# 2 : Greedy traversal for starting state KNN\n", "KNN -> CART\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.694580078125 seconds\n", "\n", "\n", ">>Classification task\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Avg accuracy of CART classification for 10 cross-validation : 0.6363636363636364\n", "\n", "Classification done -- CPU time: 13.80032992362976 seconds\n", "End Pipeline CPU time: 14.50236988067627 seconds\n", "\n", "\n", "Strategy# 3 : Greedy traversal for starting state MF\n", "MF -> CART\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "Imputation done -- CPU time: 0.08531689643859863 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.7048260381593715\n", "\n", "Classification done -- CPU time: 14.928837299346924 seconds\n", "End Pipeline CPU time: 15.022620916366577 seconds\n", "\n", "\n", "Strategy# 4 : Greedy traversal for starting state DS\n", "DS -> IQR -> CART\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.03473210334777832 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "177 outlying rows have been removed\n", "* For test dataset\n", "87 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.023534774780273438 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.7016806722689075\n", "\n", "Classification done -- CPU time: 12.422258853912354 seconds\n", "End Pipeline CPU time: 12.492411136627197 seconds\n", "\n", "\n", "Strategy# 5 : Greedy traversal for starting state MM\n", "MM -> IQR -> CART\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.02655482292175293 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "177 outlying rows have been removed\n", "* For test dataset\n", "87 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.025084257125854492 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.7016806722689075\n", "\n", "Classification done -- CPU time: 12.464968919754028 seconds\n", "End Pipeline CPU time: 12.527073860168457 seconds\n", "\n", "\n", "Strategy# 6 : Greedy traversal for starting state ZS\n", "ZS -> LOF -> CART\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.02848076820373535 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.01862192153930664 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.6829268292682927\n", "\n", "Classification done -- CPU time: 9.254787683486938 seconds\n", "End Pipeline CPU time: 9.312004804611206 seconds\n", "\n", "\n", "Strategy# 7 : Greedy traversal for starting state MR\n", "MR -> DS -> IQR -> CART\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Apply MR feature selection with missing threshold= 0.3\n", "1 features with greater than 0.30 missing values.\n", "\n", "List of variables to be removed : ['Cabin']\n", "After feature selection:\n", "10 features remain\n", "['Name', 'Parch', 'Sex', 'Embarked', 'PassengerId', 'Age', 'Ticket', 'Pclass', 'Fare', 'SibSp']\n", "Feature selection done -- CPU time: 0.0058100223541259766 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.034548044204711914 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "177 outlying rows have been removed\n", "* For test dataset\n", "87 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.024102210998535156 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.7016806722689075\n", "\n", "Classification done -- CPU time: 12.770240068435669 seconds\n", "End Pipeline CPU time: 12.850909948348999 seconds\n", "\n", "\n", "Strategy# 8 : Greedy traversal for starting state WR\n", "WR -> MF -> CART\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply WR feature selection\n", "Input variables must be non-negative. WR feature selection is only applied to positive variables.\n", "After feature selection:\n", "6 features remain\n", "['<PERSON><PERSON>', 'PassengerId', 'Age', 'P<PERSON>lass', 'Fare', 'SibSp']\n", "Feature selection done -- CPU time: 0.014119863510131836 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "No missing values in the given data\n", "* For test dataset\n", "Before imputation:\n", "Total 87 missing values in ['Age', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0.0 non-numerical missing values\n", "Imputation done -- CPU time: 0.029539108276367188 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.7016806722689075\n", "\n", "Classification done -- CPU time: 14.173364877700806 seconds\n", "End Pipeline CPU time: 14.227406024932861 seconds\n", "\n", "\n", "Strategy# 9 : Greedy traversal for starting state LC\n", "LC -> MF -> CART\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Apply LC feature selection with threshold= 0.3\n", "2 features with linear correlation greater than 0.30.\n", "\n", "List of correlated variables to be removed : ['Pclass', 'SibSp']\n", "After feature selection:\n", "9 features remain\n", "['<PERSON>abin', 'Name', 'Pa<PERSON>', 'Sex', 'Embarked', 'PassengerId', 'Age', 'Ticket', 'Fare']\n", "Feature selection done -- CPU time: 0.01737189292907715 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Cabin', 'Age', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "Imputation done -- CPU time: 0.07361888885498047 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.6902356902356902\n", "\n", "Classification done -- CPU time: 14.284090280532837 seconds\n", "End Pipeline CPU time: 14.387985229492188 seconds\n", "\n", "\n", "Strategy# 10 : Greedy traversal for starting state Tree\n", "Tree -> IQR -> CART\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "169 outlying rows have been removed\n", "* For test dataset\n", "65 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.043347835540771484 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.6790780141843972\n", "\n", "Classification done -- CPU time: 13.33837103843689 seconds\n", "End Pipeline CPU time: 13.398026943206787 seconds\n", "\n", "\n", "Strategy# 11 : Greedy traversal for starting state ZSB\n", "ZSB -> CART\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "38 outlying rows have been removed:\n", "* For test dataset\n", "0 outlying rows have been removed:\n", "Outlier detection and removal done -- CPU time: 0.034095048904418945 seconds\n", "\n", "\n", ">>Classification task\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Avg accuracy of CART classification for 10 cross-validation : 0.7144970414201184\n", "\n", "Classification done -- CPU time: 14.045543193817139 seconds\n", "End Pipeline CPU time: 14.087561845779419 seconds\n", "\n", "\n", "Strategy# 12 : Greedy traversal for starting state LOF\n", "LOF -> CART\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.02259993553161621 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.7235772357723578\n", "\n", "Classification done -- CPU time: 10.82175612449646 seconds\n", "End Pipeline CPU time: 10.851893186569214 seconds\n", "\n", "\n", "Strategy# 13 : Greedy traversal for starting state IQR\n", "IQR -> CART\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "169 outlying rows have been removed\n", "* For test dataset\n", "65 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.029310941696166992 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.6790780141843972\n", "\n", "Classification done -- CPU time: 13.11614203453064 seconds\n", "End Pipeline CPU time: 13.152339935302734 seconds\n", "\n", "\n", "Strategy# 14 : Greedy traversal for starting state CC\n", "CC -> LOF -> CART\n", "\n", "Start pipeline\n", "-------------\n", ">>Consistency checking\n", "* For train dataset\n", "Constraints from the file: titanic_example_constraints.tdda\n", "Constraints passing: 51\n", "\n", "Constraints failing: 5\n", "\n", "* For test dataset\n", "Constraints from the file: titanic_example_constraints.tdda\n", "Constraints passing: 47\n", "\n", "Constraints failing: 9\n", "\n", "Consistency checking done -- CPU time: 0.08783197402954102 seconds\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.020134925842285156 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.7235772357723578\n", "\n", "Classification done -- CPU time: 10.497478008270264 seconds\n", "End Pipeline CPU time: 10.616434812545776 seconds\n", "\n", "\n", "Strategy# 15 : Greedy traversal for starting state PC\n", "PC -> ZSB -> CART\n", "\n", "Start pipeline\n", "-------------\n", ">>Consistency checking\n", "* For train dataset\n", "Number of pattern violations on variable ' Sex 'for pattern# 0 as '^[a-m]{2,3}$' : 891\n", "No violation on variable ' Sex ' for pattern# 1 as '^[a-z]{4,6}$'\n", "Number of pattern violations on variable ' Embarked 'for pattern# 0 as '^[Q|S]$' : 170\n", "Number of pattern violations on variable ' Embarked 'for pattern# 1 as '^[Q|S]$' : 170\n", "Number of pattern violations on variable ' Embarked 'for pattern# 2 as '^[Z|S]$' : 247\n", "245\n", "* For test dataset\n", "Number of pattern violations on variable ' Sex 'for pattern# 0 as '^[a-m]{2,3}$' : 418\n", "No violation on variable ' Sex ' for pattern# 1 as '^[a-z]{4,6}$'\n", "Number of pattern violations on variable ' Embarked 'for pattern# 0 as '^[Q|S]$' : 102\n", "Number of pattern violations on variable ' Embarked 'for pattern# 1 as '^[Q|S]$' : 102\n", "Number of pattern violations on variable ' Embarked 'for pattern# 2 as '^[Z|S]$' : 148\n", "148\n", "Consistency checking done -- CPU time: 0.03182411193847656 seconds\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed:\n", "* For test dataset\n", "0 outlying rows have been removed:\n", "Outlier detection and removal done -- CPU time: 0.018471956253051758 seconds\n", "\n", "\n", ">>Classification task\n", "Error: Need at least one continous variable and  10  observations for regression\n", "Classification done -- CPU time: 0.002089262008666992 seconds\n", "End Pipeline CPU time: 0.06238889694213867 seconds\n", "\n", "\n", "Strategy# 16 : Greedy traversal for starting state ED\n", "ED -> KNN -> CART\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "To detect duplicates, rows with missing values are removed using DROP\n", "Total number of rows: 891\n", "Number of duplicated rows: 0\n", "After deduplication: Number of rows: 891\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "To detect duplicates, rows with missing values are removed using DROP\n", "Total number of rows: 418\n", "Number of duplicated rows: 0\n", "After deduplication: Number of rows: 418\n", "Deduplication done -- CPU time: 0.027279138565063477 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "No missing values in the given data\n", "* For test dataset\n", "Before imputation:\n", "No missing values in the given data\n", "Imputation done -- CPU time: 0.013499259948730469 seconds\n", "\n", "\n", ">>Classification task\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.local/lib/python3.6/site-packages/numpy/core/fromnumeric.py:3118: RuntimeWarning: Mean of empty slice.\n", "  out=out, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/core/fromnumeric.py:3118: RuntimeWarning: Mean of empty slice.\n", "  out=out, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/core/fromnumeric.py:3118: RuntimeWarning: Mean of empty slice.\n", "  out=out, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/core/fromnumeric.py:3118: RuntimeWarning: Mean of empty slice.\n", "  out=out, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Avg accuracy of CART classification for 10 cross-validation : 0.5993265993265994\n", "\n", "Classification done -- CPU time: 14.089879989624023 seconds\n", "End Pipeline CPU time: 14.14141297340393 seconds\n", "\n", "\n", "Strategy# 17 : Greedy traversal for starting state AD\n", "AD -> ZS -> LOF -> CART\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "Number of duplicate rows removed: 0\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "Number of duplicate rows removed: 0\n", "Deduplication done -- CPU time: 1.0860631465911865 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.02784109115600586 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.018588781356811523 seconds\n", "\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.6829268292682927\n", "\n", "Classification done -- CPU time: 9.118140935897827 seconds\n", "End Pipeline CPU time: 10.26478624343872 seconds\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.7016806722689075\n", "\n", "Classification done -- CPU time: 14.995396852493286 seconds\n", "End Pipeline CPU time: 14.999600887298584 seconds\n", "\n", "==== Recap ====\n", "\n", "List of strategies tried by Learn2Clean:\n", "['MICE -> CART', 'EM -> MM -> IQR -> CART', 'KNN -> CART', 'MF -> CART', 'DS -> IQR -> CART', 'MM -> IQR -> CART', 'ZS -> LOF -> CART', 'MR -> DS -> IQR -> CART', 'WR -> MF -> CART', 'LC -> MF -> CART', 'Tree -> IQR -> CART', 'ZSB -> CART', 'LOF -> CART', 'IQR -> CART', 'CC -> LOF -> CART', 'PC -> ZSB -> CART', 'ED -> KNN -> CART', 'AD -> ZS -> LOF -> CART']\n", "\n", "List of corresponding quality metrics ****\n", " [{'quality_metric': 0.7048260381593715}, {'quality_metric': 0.7014590347923682}, {'quality_metric': 0.6363636363636364}, {'quality_metric': 0.7048260381593715}, {'quality_metric': 0.7016806722689075}, {'quality_metric': 0.7016806722689075}, {'quality_metric': 0.6829268292682927}, {'quality_metric': 0.7016806722689075}, {'quality_metric': 0.7016806722689075}, {'quality_metric': 0.6902356902356902}, {'quality_metric': 0.6790780141843972}, {'quality_metric': 0.7144970414201184}, {'quality_metric': 0.7235772357723578}, {'quality_metric': 0.6790780141843972}, {'quality_metric': 0.7235772357723578}, {'quality_metric': None}, {'quality_metric': 0.5993265993265994}, {'quality_metric': 0.6829268292682927}, {'quality_metric': 0.7016806722689075}]\n", "\n", "Strategy LOF -> CART for maximal accuracy : 0.7235772357723578 for CART\n", "\n", "=== End of Learn2Clean - Pipeline execution -- CPU time: 234.34766507148743 seconds\n", "\n", "**** Best strategy ****\n", "('titanic_example', 'learn2clean', 'CART', 'Survived', None, 'LOF -> CART', 'accuracy', 0.7235772357723578, 234.34766507148743)\n"]}], "source": ["import learn2clean.loading.reader as rd \n", "import learn2clean.qlearning.qlearner as ql\n", "# the results of learn2clean cleaning are stored in 'titanic_example'_results.txt in 'save' directory\n", "\n", "titanic = [\"../datasets/titanic/titanic_train.csv\",\"../datasets/titanic/test.csv\"]\n", "hr=rd.Reader(sep=',',verbose=False, encoding=False) \n", "dataset=hr.train_test_split(titanic, 'Survived')\n", "\n", "\n", "# Learn2clean finds the best strategy LOF -> CART for maximal accuracy : 0.7235772357723578 for CART\n", "# in  234.35 seconds\n", "# The best strategy is stored in EOF of 'titanic_example_results.txt' in 'save' directory as\n", "# ('titanic_example', 'learn2clean', 'CART', 'Survived', None, 'LOF -> CART', 'accuracy', 0.7235772357723578, 234.34766507148743)\n", "\n", "l2c_c1assification1=ql.Qlearner(dataset = dataset,goal='CART', target_goal='Survived',threshold = 0.6, target_prepare=None, file_name = 'titanic_example', verbose = False)\n", "l2c_c1assification1.learn2clean()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Start Learn2Clean\n", "Learn2Clean - Pipeline construction -- CPU time: 0.1473400592803955 seconds\n", "=== Start Pipeline Execution ===\n", "\n", "\n", "Strategy# 0 : Greedy traversal for starting state MICE\n", "MICE -> LDA\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.09962105751037598 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.7014590347923682\n", "\n", "Classification done -- CPU time: 0.018969058990478516 seconds\n", "End Pipeline CPU time: 0.12717914581298828 seconds\n", "\n", "\n", "Strategy# 1 : Greedy traversal for starting state EM\n", "EM -> MM -> IQR -> LDA\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.19570398330688477 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.027826786041259766 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed\n", "* For test dataset\n", "0 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.025235891342163086 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.6980920314253648\n", "\n", "Classification done -- CPU time: 0.016846179962158203 seconds\n", "End Pipeline CPU time: 0.2810671329498291 seconds\n", "\n", "\n", "Strategy# 2 : Greedy traversal for starting state KNN\n", "KNN -> LDA\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.8263652324676514 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.6161616161616161\n", "\n", "Classification done -- CPU time: 0.019936084747314453 seconds\n", "End Pipeline CPU time: 0.8538460731506348 seconds\n", "\n", "\n", "Strategy# 3 : Greedy traversal for starting state MF\n", "MF -> LDA\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "Imputation done -- CPU time: 0.09158706665039062 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.7037037037037037\n", "\n", "Classification done -- CPU time: 0.01627182960510254 seconds\n", "End Pipeline CPU time: 0.11719393730163574 seconds\n", "\n", "\n", "Strategy# 4 : Greedy traversal for starting state DS\n", "DS -> IQR -> LDA\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.03666090965270996 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["177 outlying rows have been removed\n", "* For test dataset\n", "87 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.02717113494873047 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.6918767507002801\n", "\n", "Classification done -- CPU time: 0.016734838485717773 seconds\n", "End Pipeline CPU time: 0.0915231704711914 seconds\n", "\n", "\n", "Strategy# 5 : Greedy traversal for starting state MM\n", "MM -> IQR -> LDA\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.02985095977783203 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "177 outlying rows have been removed\n", "* For test dataset\n", "87 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.02574896812438965 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.7002801120448179\n", "\n", "Classification done -- CPU time: 0.015340089797973633 seconds\n", "End Pipeline CPU time: 0.08214306831359863 seconds\n", "\n", "\n", "Strategy# 6 : Greedy traversal for starting state ZS\n", "ZS -> LOF -> LDA\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.033987998962402344 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.02151322364807129 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.7398373983739838\n", "\n", "Classification done -- CPU time: 0.01700305938720703 seconds\n", "End Pipeline CPU time: 0.08415389060974121 seconds\n", "\n", "\n", "Strategy# 7 : Greedy traversal for starting state MR\n", "MR -> DS -> IQR -> LDA\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "13 features \n", "Apply MR feature selection with missing threshold= 0.3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["1 features with greater than 0.30 missing values.\n", "\n", "List of variables to be removed : ['Cabin']\n", "After feature selection:\n", "12 features remain\n", "['New_<PERSON>', 'Name', 'Pa<PERSON>', 'Sex', 'Pclass', 'Embarked', 'PassengerId', 'Age', 'Ticket', 'row', 'Fare', 'SibSp']\n", "Feature selection done -- CPU time: 0.006521940231323242 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.041127920150756836 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "177 outlying rows have been removed\n", "* For test dataset\n", "87 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.026643991470336914 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.6918767507002801\n", "\n", "Classification done -- CPU time: 0.01655411720275879 seconds\n", "End Pipeline CPU time: 0.10740804672241211 seconds\n", "\n", "\n", "Strategy# 8 : Greedy traversal for starting state WR\n", "WR -> MF -> LDA\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "13 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply WR feature selection\n", "Input variables must be non-negative. WR feature selection is only applied to positive variables.\n", "After feature selection:\n", "7 features remain\n", "['New_ID', 'Parch', 'PassengerId', 'Age', 'Pclass', 'Fare', 'SibSp']\n", "Feature selection done -- CPU time: 0.014829874038696289 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "No missing values in the given data\n", "* For test dataset\n", "Before imputation:\n", "Total 87 missing values in ['Age', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0.0 non-numerical missing values\n", "Imputation done -- CPU time: 0.03357982635498047 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.7002801120448179\n", "\n", "Classification done -- CPU time: 0.01703786849975586 seconds\n", "End Pipeline CPU time: 0.0772252082824707 seconds\n", "\n", "\n", "Strategy# 9 : Greedy traversal for starting state LC\n", "LC -> MF -> LDA\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "13 features \n", "Apply LC feature selection with threshold= 0.3\n", "3 features with linear correlation greater than 0.30.\n", "\n", "List of correlated variables to be removed : ['Pclass', 'SibSp', 'New_ID']\n", "After feature selection:\n", "10 features remain\n", "['<PERSON>abin', 'Name', 'Pa<PERSON>', 'Sex', 'Embarked', 'PassengerId', 'Age', 'Ticket', 'row', 'Fare']\n", "Feature selection done -- CPU time: 0.02201533317565918 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Cabin', 'Age', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "Imputation done -- CPU time: 0.08667111396789551 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.6487093153759821\n", "\n", "Classification done -- CPU time: 0.017573118209838867 seconds\n", "End Pipeline CPU time: 0.13915705680847168 seconds\n", "\n", "\n", "Strategy# 10 : Greedy traversal for starting state Tree\n", "Tree -> IQR -> LDA\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "159 outlying rows have been removed\n", "* For test dataset\n", "62 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.031812191009521484 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.6912280701754386\n", "\n", "Classification done -- CPU time: 0.017689943313598633 seconds\n", "End Pipeline CPU time: 0.06208395957946777 seconds\n", "\n", "\n", "Strategy# 11 : Greedy traversal for starting state ZSB\n", "ZSB -> LDA\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed:\n", "* For test dataset\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["0 outlying rows have been removed:\n", "Outlier detection and removal done -- CPU time: 0.03742480278015137 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.7002801120448179\n", "\n", "Classification done -- CPU time: 0.018596887588500977 seconds\n", "End Pipeline CPU time: 0.06345200538635254 seconds\n", "\n", "\n", "Strategy# 12 : Greedy traversal for starting state LOF\n", "LOF -> LDA\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.022199153900146484 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.7317073170731707\n", "\n", "Classification done -- CPU time: 0.019717931747436523 seconds\n", "End Pipeline CPU time: 0.04882979393005371 seconds\n", "\n", "\n", "Strategy# 13 : Greedy traversal for starting state IQR\n", "IQR -> LDA\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "159 outlying rows have been removed\n", "* For test dataset\n", "62 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.03426384925842285 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.6912280701754386\n", "\n", "Classification done -- CPU time: 0.01969623565673828 seconds\n", "End Pipeline CPU time: 0.06175684928894043 seconds\n", "\n", "\n", "Strategy# 14 : Greedy traversal for starting state CC\n", "CC -> LOF -> LDA\n", "\n", "Start pipeline\n", "-------------\n", ">>Consistency checking\n", "* For train dataset\n", "Constraints from the file: titanic_example_constraints.tdda\n", "Constraints passing: 51\n", "\n", "Constraints failing: 5\n", "\n", "* For test dataset\n", "Constraints from the file: titanic_example_constraints.tdda\n", "Constraints passing: 47\n", "\n", "Constraints failing: 9\n", "\n", "Consistency checking done -- CPU time: 0.05825304985046387 seconds\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.022368907928466797 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.7317073170731707\n", "\n", "Classification done -- CPU time: 0.01831197738647461 seconds\n", "End Pipeline CPU time: 0.11045598983764648 seconds\n", "\n", "\n", "Strategy# 15 : Greedy traversal for starting state PC\n", "PC -> ZSB -> LDA\n", "\n", "Start pipeline\n", "-------------\n", ">>Consistency checking\n", "* For train dataset\n", "Number of pattern violations on variable ' Sex 'for pattern# 0 as '^[a-m]{2,3}$' : 891\n", "No violation on variable ' Sex ' for pattern# 1 as '^[a-z]{4,6}$'\n", "Number of pattern violations on variable ' Embarked 'for pattern# 0 as '^[Q|S]$' : 170\n", "Number of pattern violations on variable ' Embarked 'for pattern# 1 as '^[Q|S]$' : 170\n", "Number of pattern violations on variable ' Embarked 'for pattern# 2 as '^[Z|S]$' : 247\n", "245\n", "* For test dataset\n", "Number of pattern violations on variable ' Sex 'for pattern# 0 as '^[a-m]{2,3}$' : 418\n", "No violation on variable ' Sex ' for pattern# 1 as '^[a-z]{4,6}$'\n", "Number of pattern violations on variable ' Embarked 'for pattern# 0 as '^[Q|S]$' : 102\n", "Number of pattern violations on variable ' Embarked 'for pattern# 1 as '^[Q|S]$' : 102\n", "Number of pattern violations on variable ' Embarked 'for pattern# 2 as '^[Z|S]$' : 148\n", "148\n", "Consistency checking done -- CPU time: 0.033538818359375 seconds\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed:\n", "* For test dataset\n", "0 outlying rows have been removed:\n", "Outlier detection and removal done -- CPU time: 0.014999151229858398 seconds\n", "\n", "\n", ">>Classification task\n", "Error: Need at least one continous variable and  10  observations for regression\n", "Classification done -- CPU time: 0.0023338794708251953 seconds\n", "End Pipeline CPU time: 0.06121706962585449 seconds\n", "\n", "\n", "Strategy# 16 : Greedy traversal for starting state ED\n", "ED -> KNN -> LDA\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "To detect duplicates, rows with missing values are removed using DROP\n", "Total number of rows: 891\n", "Number of duplicated rows: 0\n", "After deduplication: Number of rows: 891\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "To detect duplicates, rows with missing values are removed using DROP\n", "Total number of rows: 418\n", "Number of duplicated rows: 0\n", "After deduplication: Number of rows: 418\n", "Deduplication done -- CPU time: 0.03443002700805664 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "No missing values in the given data\n", "* For test dataset\n", "Before imputation:\n", "No missing values in the given data\n", "Imputation done -- CPU time: 0.014500141143798828 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.6127946127946128\n", "\n", "Classification done -- CPU time: 0.022833824157714844 seconds\n", "End Pipeline CPU time: 0.08355307579040527 seconds\n", "\n", "\n", "Strategy# 17 : Greedy traversal for starting state AD\n", "AD -> ZS -> LOF -> LDA\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/core/fromnumeric.py:3118: RuntimeWarning: Mean of empty slice.\n", "  out=out, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/core/fromnumeric.py:3118: RuntimeWarning: Mean of empty slice.\n", "  out=out, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/core/fromnumeric.py:3118: RuntimeWarning: Mean of empty slice.\n", "  out=out, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/core/fromnumeric.py:3118: RuntimeWarning: Mean of empty slice.\n", "  out=out, **kwargs)\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of duplicate rows removed: 0\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "Number of duplicate rows removed: 0\n", "Deduplication done -- CPU time: 0.12124300003051758 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.030086040496826172 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.018805980682373047 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.6585365853658537\n", "\n", "Classification done -- CPU time: 0.0157010555267334 seconds\n", "End Pipeline CPU time: 0.20049405097961426 seconds\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.7002801120448179\n", "\n", "Classification done -- CPU time: 0.01841902732849121 seconds\n", "End Pipeline CPU time: 0.02251887321472168 seconds\n", "\n", "==== Recap ====\n", "\n", "List of strategies tried by Learn2Clean:\n", "['MICE -> LDA', 'EM -> MM -> IQR -> LDA', 'KNN -> LDA', 'MF -> LDA', 'DS -> IQR -> LDA', 'MM -> IQR -> LDA', 'ZS -> LOF -> LDA', 'MR -> DS -> IQR -> LDA', 'WR -> MF -> LDA', 'LC -> MF -> LDA', 'Tree -> IQR -> LDA', 'ZSB -> LDA', 'LOF -> LDA', 'IQR -> LDA', 'CC -> LOF -> LDA', 'PC -> ZSB -> LDA', 'ED -> KNN -> LDA', 'AD -> ZS -> LOF -> LDA']\n", "\n", "List of corresponding quality metrics ****\n", " [{'quality_metric': 0.7014590347923682}, {'quality_metric': 0.6980920314253648}, {'quality_metric': 0.6161616161616161}, {'quality_metric': 0.7037037037037037}, {'quality_metric': 0.6918767507002801}, {'quality_metric': 0.7002801120448179}, {'quality_metric': 0.7398373983739838}, {'quality_metric': 0.6918767507002801}, {'quality_metric': 0.7002801120448179}, {'quality_metric': 0.6487093153759821}, {'quality_metric': 0.6912280701754386}, {'quality_metric': 0.7002801120448179}, {'quality_metric': 0.7317073170731707}, {'quality_metric': 0.6912280701754386}, {'quality_metric': 0.7317073170731707}, {'quality_metric': None}, {'quality_metric': 0.6127946127946128}, {'quality_metric': 0.6585365853658537}, {'quality_metric': 0.7002801120448179}]\n", "\n", "Strategy ZS -> LOF -> LDA for maximal accuracy : 0.7398373983739838 for LDA\n", "\n", "=== End of Learn2Clean - Pipeline execution -- CPU time: 2.67972993850708 seconds\n", "\n", "**** Best strategy ****\n", "('titanic_example', 'learn2clean', 'LDA', 'Survived', None, 'ZS -> LOF -> LDA', 'accuracy', 0.7398373983739838, 2.67972993850708)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/sklearn/discriminant_analysis.py:388: UserWarning: Variables are collinear.\n", "  warnings.warn(\"Variables are collinear.\")\n"]}], "source": ["# Learn2clean finds the best strategy WR -> IQR -> LDA for maximal accuracy : 0.7398373983739838 for LDA \n", "# despite collinearity in  2.68 seconds\n", "# The best strategy is stored in EOF of 'titanic_example_results.txt' in 'save' directory as\n", "# ('titanic_example', 'learn2clean', 'LDA', 'Survived', None, 'ZS -> LOF -> LDA', 'accuracy', 0.7398373983739838, 2.67972993850708)\n", "\n", "l2c_c1assification2=ql.Qlearner(dataset,goal='LDA',target_goal='Survived',target_prepare=None, threshold = 0.6, file_name = 'titanic_example', verbose = False)\n", "l2c_c1assification2.learn2clean()\n", "\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Start Learn2Clean\n", "Learn2Clean - Pipeline construction -- CPU time: 0.13705706596374512 seconds\n", "=== Start Pipeline Execution ===\n", "\n", "\n", "Strategy# 0 : Greedy traversal for starting state MICE\n", "MICE -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.09693217277526855 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive <PERSON> classification for 10 cross-validation : 0.6767676767676768\n", "\n", "Classification done -- CPU time: 0.0321040153503418 seconds\n", "End Pipeline CPU time: 0.1375579833984375 seconds\n", "\n", "\n", "Strategy# 1 : Greedy traversal for starting state EM\n", "EM -> MM -> IQR -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.18787574768066406 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.029927968978881836 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed\n", "* For test dataset\n", "0 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.022772789001464844 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6734006734006734\n", "\n", "Classification done -- CPU time: 0.029360055923461914 seconds\n", "End Pipeline CPU time: 0.2852761745452881 seconds\n", "\n", "\n", "Strategy# 2 : Greedy traversal for starting state KNN\n", "KNN -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.796605110168457 seconds\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n", "/Users/<USER>/anaconda3/anaconda3/lib/python3.6/site-packages/fancyimpute/solver.py:58: UserWarning: Input matrix is not missing any values\n", "  warnings.warn(\"Input matrix is not missing any values\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.5813692480359147\n", "\n", "Classification done -- CPU time: 0.03131294250488281 seconds\n", "End Pipeline CPU time: 0.8369858264923096 seconds\n", "\n", "\n", "Strategy# 3 : Greedy traversal for starting state MF\n", "MF -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "Imputation done -- CPU time: 0.09486579895019531 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6722783389450057\n", "\n", "Classification done -- CPU time: 0.03402113914489746 seconds\n", "End Pipeline CPU time: 0.13645005226135254 seconds\n", "\n", "\n", "Strategy# 4 : Greedy traversal for starting state DS\n", "DS -> IQR -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.04644918441772461 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "177 outlying rows have been removed\n", "* For test dataset\n", "87 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.02735114097595215 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6680672268907563\n", "\n", "Classification done -- CPU time: 0.029088973999023438 seconds\n", "End Pipeline CPU time: 0.11577510833740234 seconds\n", "\n", "\n", "Strategy# 5 : Greedy traversal for starting state MM\n", "MM -> IQR -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.02892017364501953 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "177 outlying rows have been removed\n", "* For test dataset\n", "87 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.027365922927856445 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6708683473389355\n", "\n", "Classification done -- CPU time: 0.028953075408935547 seconds\n", "End Pipeline CPU time: 0.0966348648071289 seconds\n", "\n", "\n", "Strategy# 6 : Greedy traversal for starting state ZS\n", "ZS -> LOF -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.03268599510192871 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.020756959915161133 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive <PERSON> classification for 10 cross-validation : 0.6585365853658537\n", "\n", "Classification done -- CPU time: 0.030221939086914062 seconds\n", "End Pipeline CPU time: 0.09348607063293457 seconds\n", "\n", "\n", "Strategy# 7 : Greedy traversal for starting state MR\n", "MR -> DS -> IQR -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "13 features \n", "Apply MR feature selection with missing threshold= 0.3\n", "1 features with greater than 0.30 missing values.\n", "\n", "List of variables to be removed : ['Cabin']\n", "After feature selection:\n", "12 features remain\n", "['New_<PERSON>', 'Name', 'Pa<PERSON>', 'Sex', 'Pclass', 'Embarked', 'PassengerId', 'Age', 'Ticket', 'row', 'Fare', 'SibSp']\n", "Feature selection done -- CPU time: 0.006105899810791016 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.03782987594604492 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "177 outlying rows have been removed\n", "* For test dataset\n", "87 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.023138046264648438 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6680672268907563\n", "\n", "Classification done -- CPU time: 0.027782201766967773 seconds\n", "End Pipeline CPU time: 0.11172008514404297 seconds\n", "\n", "\n", "Strategy# 8 : Greedy traversal for starting state WR\n", "WR -> MF -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "13 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply WR feature selection\n", "Input variables must be non-negative. WR feature selection is only applied to positive variables.\n", "After feature selection:\n", "7 features remain\n", "['New_ID', 'Parch', 'PassengerId', 'Age', 'Pclass', 'Fare', 'SibSp']\n", "Feature selection done -- CPU time: 0.015136003494262695 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "No missing values in the given data\n", "* For test dataset\n", "Before imputation:\n", "Total 87 missing values in ['Age', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0.0 non-numerical missing values\n", "Imputation done -- CPU time: 0.03452801704406738 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6708683473389355\n", "\n", "Classification done -- CPU time: 0.03441500663757324 seconds\n", "End Pipeline CPU time: 0.09575510025024414 seconds\n", "\n", "\n", "Strategy# 9 : Greedy traversal for starting state LC\n", "LC -> MF -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "13 features \n", "Apply LC feature selection with threshold= 0.3\n", "3 features with linear correlation greater than 0.30.\n", "\n", "List of correlated variables to be removed : ['Pclass', 'SibSp', 'New_ID']\n", "After feature selection:\n", "10 features remain\n", "['<PERSON>abin', 'Name', 'Pa<PERSON>', 'Sex', 'Embarked', 'PassengerId', 'Age', 'Ticket', 'row', 'Fare']\n", "Feature selection done -- CPU time: 0.01921820640563965 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Cabin', 'Age', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "Imputation done -- CPU time: 0.08190703392028809 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6677890011223344\n", "\n", "Classification done -- CPU time: 0.03490018844604492 seconds\n", "End Pipeline CPU time: 0.14860820770263672 seconds\n", "\n", "\n", "Strategy# 10 : Greedy traversal for starting state Tree\n", "Tree -> IQR -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "159 outlying rows have been removed\n", "* For test dataset\n", "62 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.03393721580505371 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.7052631578947368\n", "\n", "Classification done -- CPU time: 0.0352320671081543 seconds\n", "End Pipeline CPU time: 0.08119082450866699 seconds\n", "\n", "\n", "Strategy# 11 : Greedy traversal for starting state ZSB\n", "ZSB -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed:\n", "* For test dataset\n", "0 outlying rows have been removed:\n", "Outlier detection and removal done -- CPU time: 0.03577613830566406 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6708683473389355\n", "\n", "Classification done -- CPU time: 0.03692173957824707 seconds\n", "End Pipeline CPU time: 0.08146810531616211 seconds\n", "\n", "\n", "Strategy# 12 : Greedy traversal for starting state LOF\n", "LOF -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.02280402183532715 seconds\n", "\n", "\n", ">>Classification task\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Accuracy of Naive <PERSON> classification for 10 cross-validation : 0.6747967479674797\n", "\n", "Classification done -- CPU time: 0.04029703140258789 seconds\n", "End Pipeline CPU time: 0.07034015655517578 seconds\n", "\n", "\n", "Strategy# 13 : Greedy traversal for starting state IQR\n", "IQR -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "159 outlying rows have been removed\n", "* For test dataset\n", "62 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.034117937088012695 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.7052631578947368\n", "\n", "Classification done -- CPU time: 0.036270856857299805 seconds\n", "End Pipeline CPU time: 0.07789993286132812 seconds\n", "\n", "\n", "Strategy# 14 : Greedy traversal for starting state CC\n", "CC -> LOF -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Consistency checking\n", "* For train dataset\n", "Constraints from the file: titanic_example_constraints.tdda\n", "Constraints passing: 51\n", "\n", "Constraints failing: 5\n", "\n", "* For test dataset\n", "Constraints from the file: titanic_example_constraints.tdda\n", "Constraints passing: 47\n", "\n", "Constraints failing: 9\n", "\n", "Consistency checking done -- CPU time: 0.057296037673950195 seconds\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.021456003189086914 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive <PERSON> classification for 10 cross-validation : 0.6747967479674797\n", "\n", "Classification done -- CPU time: 0.03267312049865723 seconds\n", "End Pipeline CPU time: 0.12237095832824707 seconds\n", "\n", "\n", "Strategy# 15 : Greedy traversal for starting state PC\n", "PC -> ZSB -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Consistency checking\n", "* For train dataset\n", "Number of pattern violations on variable ' Sex 'for pattern# 0 as '^[a-m]{2,3}$' : 891\n", "No violation on variable ' Sex ' for pattern# 1 as '^[a-z]{4,6}$'\n", "Number of pattern violations on variable ' Embarked 'for pattern# 0 as '^[Q|S]$' : 170\n", "Number of pattern violations on variable ' Embarked 'for pattern# 1 as '^[Q|S]$' : 170\n", "Number of pattern violations on variable ' Embarked 'for pattern# 2 as '^[Z|S]$' : 247\n", "245\n", "* For test dataset\n", "Number of pattern violations on variable ' Sex 'for pattern# 0 as '^[a-m]{2,3}$' : 418\n", "No violation on variable ' Sex ' for pattern# 1 as '^[a-z]{4,6}$'\n", "Number of pattern violations on variable ' Embarked 'for pattern# 0 as '^[Q|S]$' : 102\n", "Number of pattern violations on variable ' Embarked 'for pattern# 1 as '^[Q|S]$' : 102\n", "Number of pattern violations on variable ' Embarked 'for pattern# 2 as '^[Z|S]$' : 148\n", "148\n", "Consistency checking done -- CPU time: 0.03300213813781738 seconds\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed:\n", "* For test dataset\n", "0 outlying rows have been removed:\n", "Outlier detection and removal done -- CPU time: 0.01564335823059082 seconds\n", "\n", "\n", ">>Classification task\n", "Error: Need at least one continous variable and  10  observations for regression\n", "Classification done -- CPU time: 0.002316713333129883 seconds\n", "End Pipeline CPU time: 0.06266403198242188 seconds\n", "\n", "\n", "Strategy# 16 : Greedy traversal for starting state ED\n", "ED -> KNN -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "To detect duplicates, rows with missing values are removed using DROP\n", "Total number of rows: 891\n", "Number of duplicated rows: 0\n", "After deduplication: Number of rows: 891\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "To detect duplicates, rows with missing values are removed using DROP\n", "Total number of rows: 418\n", "Number of duplicated rows: 0\n", "After deduplication: Number of rows: 418\n", "Deduplication done -- CPU time: 0.03474593162536621 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "No missing values in the given data\n", "* For test dataset\n", "Before imputation:\n", "No missing values in the given data\n", "Imputation done -- CPU time: 0.015358924865722656 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive <PERSON> classification for 10 cross-validation : 0.6127946127946128\n", "\n", "Classification done -- CPU time: 0.04173612594604492 seconds\n", "End Pipeline CPU time: 0.1039571762084961 seconds\n", "\n", "\n", "Strategy# 17 : Greedy traversal for starting state AD\n", "AD -> ZS -> LOF -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.local/lib/python3.6/site-packages/numpy/core/fromnumeric.py:3118: RuntimeWarning: Mean of empty slice.\n", "  out=out, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/core/fromnumeric.py:3118: RuntimeWarning: Mean of empty slice.\n", "  out=out, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/core/fromnumeric.py:3118: RuntimeWarning: Mean of empty slice.\n", "  out=out, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/core/fromnumeric.py:3118: RuntimeWarning: Mean of empty slice.\n", "  out=out, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of duplicate rows removed: 0\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "Number of duplicate rows removed: 0\n", "Deduplication done -- CPU time: 0.12542319297790527 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.028620004653930664 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "60 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.020748138427734375 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6260162601626016\n", "\n", "Classification done -- CPU time: 0.02990579605102539 seconds\n", "End Pipeline CPU time: 0.21939778327941895 seconds\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6708683473389355\n", "\n", "Classification done -- CPU time: 0.03898501396179199 seconds\n", "End Pipeline CPU time: 0.04347991943359375 seconds\n", "\n", "==== Recap ====\n", "\n", "List of strategies tried by Learn2Clean:\n", "['MICE -> NB', 'EM -> MM -> IQR -> NB', 'KNN -> NB', 'MF -> NB', 'DS -> IQR -> NB', 'MM -> IQR -> NB', 'ZS -> LOF -> NB', 'MR -> DS -> IQR -> NB', 'WR -> MF -> NB', 'LC -> MF -> NB', 'Tree -> IQR -> NB', 'ZSB -> NB', 'LOF -> NB', 'IQR -> NB', 'CC -> LOF -> NB', 'PC -> ZSB -> NB', 'ED -> KNN -> NB', 'AD -> ZS -> LOF -> NB']\n", "\n", "List of corresponding quality metrics ****\n", " [{'quality_metric': 0.6767676767676768}, {'quality_metric': 0.6734006734006734}, {'quality_metric': 0.5813692480359147}, {'quality_metric': 0.6722783389450057}, {'quality_metric': 0.6680672268907563}, {'quality_metric': 0.6708683473389355}, {'quality_metric': 0.6585365853658537}, {'quality_metric': 0.6680672268907563}, {'quality_metric': 0.6708683473389355}, {'quality_metric': 0.6677890011223344}, {'quality_metric': 0.7052631578947368}, {'quality_metric': 0.6708683473389355}, {'quality_metric': 0.6747967479674797}, {'quality_metric': 0.7052631578947368}, {'quality_metric': 0.6747967479674797}, {'quality_metric': None}, {'quality_metric': 0.6127946127946128}, {'quality_metric': 0.6260162601626016}, {'quality_metric': 0.6708683473389355}]\n", "\n", "Strategy Tree -> IQR -> NB for maximal accuracy : 0.7052631578947368 for NB\n", "\n", "=== End of Learn2Clean - Pipeline execution -- CPU time: 2.9261510372161865 seconds\n", "\n", "**** Best strategy ****\n", "('titanic_example', 'learn2clean', 'NB', 'Survived', None, 'Tree -> IQR -> NB', 'accuracy', 0.7052631578947368, 2.9261510372161865)\n"]}], "source": ["# Learn2clean finds the best strategy WR -> IQR -> NB for maximal accuracy : 0.7052631578947368 for NB\n", "# in  2.96 seconds\n", "# The best strategy is stored in EOF of 'titanic_example_results.txt' in 'save' directory as\n", "# ('titanic_example', 'learn2clean', 'NB', 'Survived', None, 'Tree -> IQR -> NB', 'accuracy', 0.7052631578947368, 2.9261510372161865)\n", "\n", "l2c_c1assification1=ql.Qlearner(dataset = dataset,goal='NB',target_goal='Survived',threshold = 0.6,target_prepare=None, file_name = 'titanic_example', verbose = False)\n", "l2c_c1assification1.learn2clean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Random data preprocessing pipelines"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "--------------------------\n", "Random cleaning strategy:\n", " MM -> LC -> ZSB -> CART\n", "--------------------------\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.02590799331665039 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Apply LC feature selection with threshold= 0.3\n", "3 features with linear correlation greater than 0.30.\n", "\n", "List of correlated variables to be removed : ['PassengerId', 'Pclass', 'SibSp']\n", "After feature selection:\n", "8 features remain\n", "['<PERSON>abin', 'Name', 'Pa<PERSON>', 'Sex', 'Embarked', 'Age', 'Ticket', 'Fare']\n", "Feature selection done -- CPU time: 0.016934871673583984 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed:\n", "* For test dataset\n", "0 outlying rows have been removed:\n", "Outlier detection and removal done -- CPU time: 0.022260189056396484 seconds\n", "\n", "\n", ">>Classification task\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n", "/Users/<USER>/.local/lib/python3.6/site-packages/numpy/lib/function_base.py:3405: RuntimeWarning: Invalid value encountered in median\n", "  r = func(a, **kwargs)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Avg accuracy of CART classification for 10 cross-validation : 0.6876750700280112\n", "\n", "Classification done -- CPU time: 11.214827060699463 seconds\n", "End Pipeline CPU time: 11.296580791473389 seconds\n", "('titanic_example', 'random', 'CART', 'Survived', None, 'MM -> LC -> ZSB -> CART', 'accuracy', ({'quality_metric': 0.6876750700280112}, 11.296579837799072))\n", "\n", "\n", "--------------------------\n", "Random cleaning strategy:\n", " MICE -> WR -> ZSB -> ED -> LDA\n", "--------------------------\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.09569311141967773 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "11 features \n", "Apply WR feature selection\n", "Input variables must be non-negative. WR feature selection is only applied to positive variables.\n", "Best features to keep ['Age', 'Fare', 'Parch', 'PassengerId', 'Pclass', 'SibSp']\n", "After feature selection:\n", "6 features remain\n", "['<PERSON><PERSON>', 'PassengerId', 'Age', 'P<PERSON>lass', 'Fare', 'SibSp']\n", "Feature selection done -- CPU time: 0.012602090835571289 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed:\n", "* For test dataset\n", "0 outlying rows have been removed:\n", "Outlier detection and removal done -- CPU time: 0.023391008377075195 seconds\n", "\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "Duplicates: Empty DataFrame\n", "Columns: [<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, SibSp, count]\n", "Index: []\n", "Total number of rows: 891\n", "Number of duplicated rows: 0\n", "After deduplication: Number of rows: 891\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "Duplicates: Empty DataFrame\n", "Columns: [<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>b<PERSON><PERSON>, count]\n", "Index: []\n", "Total number of rows: 418\n", "Number of duplicated rows: 0\n", "After deduplication: Number of rows: 418\n", "Deduplication done -- CPU time: 0.019173145294189453 seconds\n", "\n", "\n", ">>Classification task\n", "{'mean_fit_time': array([0.00180399]), 'std_fit_time': array([0.00024092]), 'mean_score_time': array([0.00035369]), 'std_score_time': array([7.1644783e-05]), 'params': [{}], 'split0_test_score': array([0.61659193]), 'split1_test_score': array([0.61348315]), 'mean_test_score': array([0.61503928]), 'std_test_score': array([0.00155439]), 'rank_test_score': array([1], dtype=int32), 'split0_train_score': array([0.61573034]), 'split1_train_score': array([0.62331839]), 'mean_train_score': array([0.61952436]), 'std_train_score': array([0.00379402])}\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.6150392817059483\n", "\n", "Classification done -- CPU time: 0.018697023391723633 seconds\n", "End Pipeline CPU time: 0.18587899208068848 seconds\n", "('titanic_example', 'random', 'LDA', 'Survived', None, 'MICE -> WR -> ZSB -> ED -> LDA', 'accuracy', ({'quality_metric': 0.6150392817059483}, 0.18587803840637207))\n", "\n", "\n", "--------------------------\n", "Random cleaning strategy:\n", " MICE -> ZS -> Tree -> NB\n", "--------------------------\n", "\n", "Start pipeline\n", "-------------\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 866 missing values in ['Age', 'Cabin', 'Embarked']\n", "- 177 numerical missing values in ['Age']\n", "- 689 non-numerical missing values in ['Cabin', 'Embarked']\n", "After imputation:\n", "Total 689 missing values\n", "- 0 numerical missing values\n", "- 689 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 414 missing values in ['Age', 'Cabin', 'Fare']\n", "- 87 numerical missing values in ['Age', 'Fare']\n", "- 327 non-numerical missing values in ['Cabin']\n", "After imputation:\n", "Total 327 missing values\n", "- 0 numerical missing values\n", "- 327 non-numerical missing values\n", "Imputation done -- CPU time: 0.09999203681945801 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.028235197067260742 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6801346801346801\n", "\n", "Classification done -- CPU time: 0.032572031021118164 seconds\n", "End Pipeline CPU time: 0.17720389366149902 seconds\n", "('titanic_example', 'random', 'NB', 'Survived', None, 'MICE -> ZS -> Tree -> NB', 'accuracy', ({'quality_metric': 0.6801346801346801}, 0.17720293998718262))\n"]}, {"data": {"text/plain": ["{'quality_metric': 0.6801346801346801}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["import learn2clean.loading.reader as rd \n", "import learn2clean.qlearning.qlearner as ql\n", "\n", "titanic = [\"../datasets/titanic/titanic_train.csv\",\"../datasets/titanic/test.csv\"]\n", "hr=rd.Reader(sep=',',verbose=False, encoding=False) \n", "dataset=hr.train_test_split(titanic, 'Survived')\n", "\n", "\n", "# random preprocessing pipeline for classification ; \n", "# the results of random cleaning are stored in 'titanic_example'_results_file.txt in 'save' directory\n", "# appended to the EOF but random cleaning may raise errors and have no result\n", "\n", "#random preprocessing pipeline for CART classification\n", "random1=ql.<PERSON>lea<PERSON>(dataset = dataset,goal='CART',target_goal='Survived',target_prepare=None, verbose = False)\n", "random1.random_cleaning('titanic_example')\n", "\n", "#random preprocessing pipeline for LDA classification\n", "random2=ql.<PERSON><PERSON><PERSON>(dataset,goal='LDA',target_goal='Survived',target_prepare=None, verbose = True)\n", "random2.random_cleaning('titanic_example')\n", "\n", "#random preprocessing pipeline for NB classification\n", "random3=ql.<PERSON><PERSON><PERSON>(dataset,goal='NB',target_goal='Survived', threshold=0.6, target_prepare=None, verbose = False)\n", "random3.random_cleaning('titanic_example')\n", "\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Start pipeline\n", "-------------\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.7016806722689075\n", "\n", "Classification done -- CPU time: 14.213786125183105 seconds\n", "End Pipeline CPU time: 14.218555212020874 seconds\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.7002801120448179\n", "\n", "Classification done -- CPU time: 0.01483607292175293 seconds\n", "End Pipeline CPU time: 0.018539905548095703 seconds\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6680672268907563\n", "\n", "Classification done -- CPU time: 0.030712127685546875 seconds\n", "End Pipeline CPU time: 0.03400993347167969 seconds\n"]}], "source": ["# no preprocessing: results appended to the EOF 'titanic_example'_results.txt \n", "\n", "\n", "no_prep1=ql.Qlearner(dataset = dataset,goal='CART',target_goal='Survived',target_prepare=None, verbose = False)\n", "no_prep1.no_prep('titanic_example')\n", "\n", "no_prep2=ql.Qlearner(dataset = dataset,goal='LDA',target_goal='Survived',target_prepare=None, verbose = False)\n", "no_prep2.no_prep('titanic_example')\n", "\n", "no_prep3=ql.Qlearner(dataset = dataset,goal='NB',target_goal='Survived',target_prepare=None, verbose = False)\n", "no_prep3.no_prep('titanic_example')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:anaconda3]", "language": "python", "name": "conda-env-anaconda3-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.5"}}, "nbformat": 4, "nbformat_minor": 2}