{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 1) To set up your own data cleaning pipeline"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["#Load the datasets\n", "def read_dataset(name): #when only one dataset is provided as input\n", "    import pandas as pd\n", "    if name == \"gpsa\":\n", "        df = pd.read_csv('../datasets/googleplaystore.csv', sep=',', encoding ='ISO-8859-1')\n", "    elif name == \"gpsu\":\n", "        df = pd.read_csv('../datasets/googleplaystore_reviews.csv', sep=',',encoding = 'ISO-8859-1')  \n", "    elif name == \"titanic\":\n", "        df = pd.read_csv('../datasets/titanic/titanic_train.csv', sep=',', encoding ='ISO-8859-1')\n", "    elif name == \"house\":\n", "        df = pd.read_csv('../datasets/house/house_train.csv', sep=',', encoding ='ISO-8859-1')\n", "    else: \n", "        raise ValueError('Invalid dataset name')               \n", "    return df\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>App</th>\n", "      <th>Translated_Review</th>\n", "      <th>Sentiment</th>\n", "      <th>Sentiment_Polarity</th>\n", "      <th>Sentiment_Subjectivity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10 Best Foods for You</td>\n", "      <td>I like eat delicious food. That's I'm cooking ...</td>\n", "      <td>Positive</td>\n", "      <td>1.00</td>\n", "      <td>0.533333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10 Best Foods for You</td>\n", "      <td>This help eating healthy exercise regular basis</td>\n", "      <td>Positive</td>\n", "      <td>0.25</td>\n", "      <td>0.288462</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10 Best Foods for You</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10 Best Foods for You</td>\n", "      <td>Works great especially going grocery store</td>\n", "      <td>Positive</td>\n", "      <td>0.40</td>\n", "      <td>0.875000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10 Best Foods for You</td>\n", "      <td>Best idea us</td>\n", "      <td>Positive</td>\n", "      <td>1.00</td>\n", "      <td>0.300000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                     App                                  Translated_Review  \\\n", "0  10 Best Foods for You  I like eat delicious food. That's I'm cooking ...   \n", "1  10 Best Foods for You    This help eating healthy exercise regular basis   \n", "2  10 Best Foods for You                                                NaN   \n", "3  10 Best Foods for You         Works great especially going grocery store   \n", "4  10 Best Foods for You                                       Best idea us   \n", "\n", "  Sentiment  Sentiment_Polarity  Sentiment_Subjectivity  \n", "0  Positive                1.00                0.533333  \n", "1  Positive                0.25                0.288462  \n", "2       NaN                 NaN                     NaN  \n", "3  Positive                0.40                0.875000  \n", "4  Positive                1.00                0.300000  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["read_dataset(\"gpsu\").head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Loading your data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Profiling datasets\n", "                Attribute     Type  Num. Missing Values  Num. Unique Values             Sknewness  Kurtosis\n", "0      Sentiment_Polarity  float64              26863.0              6196.0  -0.10457655084633158  0.646756\n", "1  Sentiment_Subjectivity  float64              26863.0              4531.0   -0.3063336025424886 -0.282853\n", "2                     App   object                  0.0              1074.0                   N/A       N/A\n", "3       Translated_Review   object              26868.0             27995.0                   N/A       N/A\n", "4               Sentiment   object              26863.0                 4.0                   N/A       N/A\n"]}], "source": ["import learn2clean.loading.reader as rd \n", "import learn2clean.normalization.normalizer as nl \n", "import pandas as pd\n", "\n", "# executing profiling function for one dataset as input\n", "rd.profile_summary(read_dataset('gpsu'), plot=False)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    Positive\n", "1    Positive\n", "2         NaN\n", "3    Positive\n", "4    Positive\n", "Name: Sentiment, dtype: object"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "read_dataset('gpsu')['Sentiment'].head() # the target variable is numerical \n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Reading csv : googleplaystore_reviews.csv ...\n", "Reading data ...\n", "CPU time: 3.3271310329437256 seconds\n", "Profiling datasets\n", "                Attribute     Type  Num. Missing Values  Num. Unique Values             Sknewness  Kurtosis\n", "0      Sentiment_Polarity  float64              26863.0              6196.0  -0.10457655084633158  0.646756\n", "1  Sentiment_Subjectivity  float64              26863.0              4531.0   -0.3063336025424886 -0.282853\n", "2                     App   object                  0.0              1074.0                   N/A       N/A\n", "3       Translated_Review   object              26868.0             27995.0                   N/A       N/A\n", "4               Sentiment   object              26863.0                 4.0                   N/A       N/A\n", "\n", "> Number of categorical features in the training set: 3\n", "> Number of numerical features in the training set: 2\n", "> Number of data samples : 64295\n", "\n", "> Top sparse features (% missing values on dataset set):\n", "Translated_Review         41.6\n", "Sentiment_Subjectivity    41.6\n", "Sentiment_Polarity        41.6\n", "Sentiment                 41.6\n", "dtype: float64\n", "\n", "> Task : classification\n", "Positive    16144\n", "Negative     5519\n", "Neutral      3486\n", "Name: Sentiment, dtype: int64\n", "\n", "Encoding target...\n", "Encoding target done...\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>App</th>\n", "      <th>Translated_Review</th>\n", "      <th>Sentiment</th>\n", "      <th>Sentiment_Polarity</th>\n", "      <th>Sentiment_Subjectivity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>964</th>\n", "      <td>4 in a Row</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38801</th>\n", "      <td>Easy Installer - Apps On SD</td>\n", "      <td>What guys thinking cool</td>\n", "      <td>2</td>\n", "      <td>0.35000</td>\n", "      <td>0.650000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45485</th>\n", "      <td>File Manager</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6230</th>\n", "      <td>Alto's Adventure</td>\n", "      <td>I would enjoy game lot option watch video keep playing actually worked. Instead either freezes s...</td>\n", "      <td>2</td>\n", "      <td>0.07619</td>\n", "      <td>0.504762</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44484</th>\n", "      <td>Farm Heroes Saga</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                               App  \\\n", "964                     4 in a Row   \n", "38801  Easy Installer - Apps On SD   \n", "45485                 File Manager   \n", "6230              Alto's Adventure   \n", "44484             Farm Heroes Saga   \n", "\n", "                                                                                         Translated_Review  \\\n", "964                                                                                                    NaN   \n", "38801                                                                              What guys thinking cool   \n", "45485                                                                                                  NaN   \n", "6230   I would enjoy game lot option watch video keep playing actually worked. Instead either freezes s...   \n", "44484                                                                                                  NaN   \n", "\n", "       Sentiment  Sentiment_Polarity  Sentiment_Subjectivity  \n", "964            3                 NaN                     NaN  \n", "38801          2             0.35000                0.650000  \n", "45485          3                 NaN                     NaN  \n", "6230           2             0.07619                0.504762  \n", "44484          3                 NaN                     NaN  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# encoding of the target variable\n", "import learn2clean.loading.reader as rd \n", "d_enc = rd.Reader(sep=',',verbose=True, encoding=True) \n", "\n", "gpsu  = [\"../datasets/googleplaystore_reviews.csv\"]\n", "gpsu_encoded = d_enc.train_test_split(gpsu, 'Sentiment')\n", "gpsu_encoded['train'].head()\n", "gpsu_encoded['test'].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Normalize your data"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.06097912788391113 seconds\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sentiment</th>\n", "      <th>Sentiment_Polarity</th>\n", "      <th>Sentiment_Subjectivity</th>\n", "      <th>App</th>\n", "      <th>Translated_Review</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>1.000</td>\n", "      <td>0.533333</td>\n", "      <td>10 Best Foods for You</td>\n", "      <td>I like eat delicious food. That's I'm cooking food myself, case \"10 Best Foods\" helps lot, also ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>0.625</td>\n", "      <td>0.288462</td>\n", "      <td>10 Best Foods for You</td>\n", "      <td>This help eating healthy exercise regular basis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>10 Best Foods for You</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>0.700</td>\n", "      <td>0.875000</td>\n", "      <td>10 Best Foods for You</td>\n", "      <td>Works great especially going grocery store</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>1.000</td>\n", "      <td>0.300000</td>\n", "      <td>10 Best Foods for You</td>\n", "      <td>Best idea us</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Sentiment  Sentiment_Polarity  Sentiment_Subjectivity  \\\n", "0          2               1.000                0.533333   \n", "1          2               0.625                0.288462   \n", "2          3                 NaN                     NaN   \n", "3          2               0.700                0.875000   \n", "4          2               1.000                0.300000   \n", "\n", "                     App  \\\n", "0  10 Best Foods for You   \n", "1  10 Best Foods for You   \n", "2  10 Best Foods for You   \n", "3  10 Best Foods for You   \n", "4  10 Best Foods for You   \n", "\n", "                                                                                     Translated_Review  \n", "0  I like eat delicious food. That's I'm cooking food myself, case \"10 Best Foods\" helps lot, also ...  \n", "1                                                      This help eating healthy exercise regular basis  \n", "2                                                                                                  NaN  \n", "3                                                           Works great especially going grocery store  \n", "4                                                                                         Best idea us  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# >> Examples of normalization\n", "# The choice for the normalizer : 'ZS', 'MM','DS' or 'Log10'\n", "#    Available strategies=\n", "#       - 'ZS' z-score normalization\n", "#       - 'M<PERSON>' MinMax scaling\n", "#       - 'DS' decimal scaling\n", "#       - 'Log10 log10 scaling\n", "\n", "import learn2clean.normalization.normalizer as nl \n", "\n", "# MM normalization with exclude = None, all numeric variables will be normalized\n", "n1= nl.Normalizer(gpsu_encoded.copy(),strategy='MM',exclude='Sentiment')\n", "\n", "n1.transform()['train'].head()\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.06198620796203613 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.07776117324829102 seconds\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sentiment</th>\n", "      <th>Sentiment_Polarity</th>\n", "      <th>Sentiment_Subjectivity</th>\n", "      <th>App</th>\n", "      <th>Translated_Review</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2</td>\n", "      <td>1.000000</td>\n", "      <td>0.518519</td>\n", "      <td>10 Best Foods for You</td>\n", "      <td>I like eat delicious food. That's I'm cooking food myself, case \"10 Best Foods\" helps lot, also ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>0.605105</td>\n", "      <td>0.209730</td>\n", "      <td>10 Best Foods for You</td>\n", "      <td>This help eating healthy exercise regular basis</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>10 Best Foods for You</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>0.737485</td>\n", "      <td>0.934083</td>\n", "      <td>10 Best Foods for You</td>\n", "      <td>Works great especially going grocery store</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>1.000000</td>\n", "      <td>0.213675</td>\n", "      <td>10 Best Foods for You</td>\n", "      <td>Best idea us</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Sentiment  Sentiment_Polarity  Sentiment_Subjectivity  \\\n", "0          2            1.000000                0.518519   \n", "1          2            0.605105                0.209730   \n", "2          3                 NaN                     NaN   \n", "3          2            0.737485                0.934083   \n", "4          2            1.000000                0.213675   \n", "\n", "                     App  \\\n", "0  10 Best Foods for You   \n", "1  10 Best Foods for You   \n", "2  10 Best Foods for You   \n", "3  10 Best Foods for You   \n", "4  10 Best Foods for You   \n", "\n", "                                                                                     Translated_Review  \n", "0  I like eat delicious food. That's I'm cooking food myself, case \"10 Best Foods\" helps lot, also ...  \n", "1                                                      This help eating healthy exercise regular basis  \n", "2                                                                                                  NaN  \n", "3                                                           Works great especially going grocery store  \n", "4                                                                                         Best idea us  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "#ZS normalization\n", "n1= nl.Normalizer(gpsu_encoded.copy(),strategy='ZS',exclude='Sentiment', verbose = False)\n", "n1.transform()['train'].head()\n", "\n", "#DS scaling\n", "n2= nl.Normalizer(gpsu_encoded.copy(),strategy='DS',exclude='Sentiment', verbose = False)\n", "n2.transform()['train'].head()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Replace missing values"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 54256 missing values in ['Translated_Review', 'Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "- 36168 numerical missing values in ['Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "- 18088 non-numerical missing values in ['Translated_Review']\n", "Most frequent value for  App is: CBS Sports App - Scores, News, Stats & Watch Live\n", "Most frequent value for  Translated_Review is: Good\n", "Most frequent value for  Sentiment is: 3\n", "Most frequent value for  Sentiment_Polarity is: 0.0\n", "Most frequent value for  Sentiment_Subjectivity is: 0.0\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 26338 missing values in ['Translated_Review', 'Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "- 17558 numerical missing values in ['Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "- 8780 non-numerical missing values in ['Translated_Review']\n", "Most frequent value for  App is: Bowmasters\n", "Most frequent value for  Translated_Review is: Good\n", "Most frequent value for  Sentiment is: 3\n", "Most frequent value for  Sentiment_Polarity is: 0.0\n", "Most frequent value for  Sentiment_Subjectivity is: 0.0\n", "After imputation:\n", "Total 0 missing values\n", "- 0 numerical missing values\n", "- 0 non-numerical missing values\n", "Imputation done -- CPU time: 0.1868119239807129 seconds\n", "\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 54256 missing values in ['Translated_Review', 'Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "- 36168 numerical missing values in ['Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "- 18088 non-numerical missing values in ['Translated_Review']\n", "After imputation:\n", "Total 40632 missing values\n", "- 0 numerical missing values\n", "- 40632 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 26338 missing values in ['Translated_Review', 'Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "- 17558 numerical missing values in ['Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "- 8780 non-numerical missing values in ['Translated_Review']\n", "After imputation:\n", "Total 31099 missing values\n", "- 0 numerical missing values\n", "- 31099 non-numerical missing values\n", "Imputation done -- CPU time: 0.16642022132873535 seconds\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>App</th>\n", "      <th>Translated_Review</th>\n", "      <th>Sentiment</th>\n", "      <th>Sentiment_Polarity</th>\n", "      <th>Sentiment_Subjectivity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2854</th>\n", "      <td>ANA</td>\n", "      <td>problem remember login ID password .</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14556</th>\n", "      <td>BeyondMenu Food Delivery</td>\n", "      <td>Love ease simplicity!</td>\n", "      <td>2</td>\n", "      <td>0.625000</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19582</th>\n", "      <td>CBS Sports App - Scores, News, Stats &amp; Watch Live</td>\n", "      <td>It's good, works. 50% time bugs show everything going (mostly MLB play play). Other that, gets j...</td>\n", "      <td>2</td>\n", "      <td>0.358333</td>\n", "      <td>0.491667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20330</th>\n", "      <td>CNN Breaking US &amp; World News</td>\n", "      <td>The works well. Too bad President would call CNN fake media enemy people. That authoritarian cou...</td>\n", "      <td>0</td>\n", "      <td>-0.066667</td>\n", "      <td>0.805556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15369</th>\n", "      <td>Bleacher Report: sports news, scores, &amp; highlights</td>\n", "      <td>Good</td>\n", "      <td>3</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                      App  \\\n", "2854                                                  ANA   \n", "14556                            BeyondMenu Food Delivery   \n", "19582   CBS Sports App - Scores, News, Stats & Watch Live   \n", "20330                        CNN Breaking US & World News   \n", "15369  Bleacher Report: sports news, scores, & highlights   \n", "\n", "                                                                                         Translated_Review  \\\n", "2854                                                                  problem remember login ID password .   \n", "14556                                                                                Love ease simplicity!   \n", "19582  It's good, works. 50% time bugs show everything going (mostly MLB play play). Other that, gets j...   \n", "20330  The works well. Too bad President would call CNN fake media enemy people. That authoritarian cou...   \n", "15369                                                                                                 Good   \n", "\n", "       Sentiment  Sentiment_Polarity  Sentiment_Subjectivity  \n", "2854           1            0.000000                0.000000  \n", "14556          2            0.625000                0.600000  \n", "19582          2            0.358333                0.491667  \n", "20330          0           -0.066667                0.805556  \n", "15369          3            0.000000                0.000000  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["#>> Examples for missing value imputation\n", "# Available strategies:\n", "#            - 'EM': only for numerical variables; imputation based on\n", "#                expectation maximization\n", "#            - 'MICE': only for numerical variables  missing at random (MAR);\n", "#                Multivariate Imputation by Chained Equations\n", "#            - 'KNN', only for numerical variables; k-nearest neighbor\n", "#                imputation (k=4) which weights samples using the mean squared\n", "#                difference on features for which two rows both have observed\n", "#                data\n", "#            - 'RAND', 'MF': both for numerical and categorical variables;\n", "#                replace missing values by randomly selected value in the \n", "#                variable domain or by the most frequent value in the variable\n", "#                domain respectively\n", "#            - 'MEAN', 'MEDIAN': only for numerical variables; replace missing\n", "#                values by mean or median of the numerical variable respectvely\n", "#            - or 'DROP' remove the row with at least one missing value\n", "\n", "import learn2clean.imputation.imputer as imp\n", "\n", "# replace missing values by the most frequent ones in the training and testing datasets\n", "\n", "imp1 = imp.Imputer(gpsu_encoded.copy(),strategy='MF', verbose=True).transform()\n", "\n", "imp2 = imp.Imputer(gpsu_encoded.copy(),strategy='MEDIAN', verbose=True).transform()\n", "imp1['train'].head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Detect outliers and remove them"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed:\n", "* For test dataset\n", "0 outlying rows have been removed:\n", "Outlier detection and removal done -- CPU time: 0.09380102157592773 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "43077 outlying rows have been removed\n", "* For test dataset\n", "21218 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.06182408332824707 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "40 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "40 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.4606969356536865 seconds\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sentiment</th>\n", "      <th>Sentiment_Polarity</th>\n", "      <th>Sentiment_Subjectivity</th>\n", "      <th>App</th>\n", "      <th>Translated_Review</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>6216</th>\n", "      <td>2</td>\n", "      <td>0.155556</td>\n", "      <td>0.688889</td>\n", "      <td>Alto's Adventure</td>\n", "      <td>The game awesome, filled \"must have\" criterias make good game. Graphics simply breathtaking, bac...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29674</th>\n", "      <td>2</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>Co<PERSON><PERSON> Life : Date Older Women Sugar Mummy</td>\n", "      <td>Its awesome!!!</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29363</th>\n", "      <td>2</td>\n", "      <td>0.150000</td>\n", "      <td>0.375000</td>\n", "      <td>Cooking Madness - A Chef's Restaurant Games</td>\n", "      <td>like every game way many commercials way many ads trying get buy extra stuff need</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40362</th>\n", "      <td>2</td>\n", "      <td>0.380556</td>\n", "      <td>0.611111</td>\n", "      <td>Enterprise Rent-A-Car</td>\n", "      <td>Easy, quick user friendly. Does needed.</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49676</th>\n", "      <td>2</td>\n", "      <td>0.408929</td>\n", "      <td>0.883929</td>\n", "      <td>Free TV Shows App:News, TV Series, Episode, Movies</td>\n", "      <td>I started using right now. I'm pretty impressed I've seen far.</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Sentiment  Sentiment_Polarity  Sentiment_Subjectivity  \\\n", "6216           2            0.155556                0.688889   \n", "29674          2            1.000000                1.000000   \n", "29363          2            0.150000                0.375000   \n", "40362          2            0.380556                0.611111   \n", "49676          2            0.408929                0.883929   \n", "\n", "                                                      App  \\\n", "6216                                     Alto's Adventure   \n", "29674   Cougar Dating Life : Date Older Women Sugar Mummy   \n", "29363         Cooking Madness - A Chef's Restaurant Games   \n", "40362                               Enterprise Rent-A-Car   \n", "49676  Free TV Shows App:News, TV Series, Episode, Movies   \n", "\n", "                                                                                         Translated_Review  \n", "6216   The game awesome, filled \"must have\" criterias make good game. Graphics simply breathtaking, bac...  \n", "29674                                                                                       Its awesome!!!  \n", "29363                    like every game way many commercials way many ads trying get buy extra stuff need  \n", "40362                                                              Easy, quick user friendly. Does needed.  \n", "49676                                       I started using right now. I'm pretty impressed I've seen far.  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# >> Examples for outlier detection and removal\n", "# Available strategies =\n", "#            'ZS': detects outliers using the robust Zscore as a function\n", "#            of median and median absolute deviation (MAD)\n", "#            'IQR': detects outliers using Q1 and Q3 +/- 1.5*InterQuartile Range\n", "#            'LOF': detects outliers using Local Outlier Factor\n", "\n", "                \n", "import learn2clean.outlier_detection.outlier_detector as out\n", "\n", "#to remove rows having 30% and more ZSB-based outling values among the numerical variables\n", "out1=out.Outlier_detector(gpsu_encoded.copy(), strategy='ZSB', threshold = 0.3, verbose=True)\n", "out1.transform()\n", "\n", "#to remove rows having at least one IQR-based outlying value using threshold '-1'\n", "out2=out.Outlier_detector(gpsu_encoded.copy(), strategy='IQR', threshold = -1, verbose=False)\n", "out2.transform()\n", "\n", "#to remove rows having 40% and more ZSB-based outling values among the numerical variables; \n", "# since LOF requires non missing values, rows with NaN are also removed\n", "out3=out.Outlier_detector(gpsu_encoded.copy(), strategy='LOF', threshold = .4, verbose=False)\n", "out3.transform()['train'].head()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Detect duplicates and remove them"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "Initial number of rows: 43077\n", "After deduplication: Number of rows: 22229\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "Initial number of rows: 21218\n", "After deduplication: Number of rows: 12088\n", "Deduplication done -- CPU time: 0.027705907821655273 seconds\n", "\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'AD'.\n", "Number of duplicate rows removed: 10\n", "* For test dataset\n", "Metric is not considered for 'AD'.\n", "Number of duplicate rows removed: 7\n", "Deduplication done -- CPU time: 1.6808409690856934 seconds\n", "\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>App</th>\n", "      <th>Translated_Review</th>\n", "      <th>Sentiment</th>\n", "      <th>Sentiment_Polarity</th>\n", "      <th>Sentiment_Subjectivity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>47636</th>\n", "      <td>Flow Free</td>\n", "      <td>Well, amazing simple game. Definitely addicting like puzzles. IN every pack beaten including one...</td>\n", "      <td>2</td>\n", "      <td>0.240000</td>\n", "      <td>0.491429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24091</th>\n", "      <td>Capital OneÂ® Mobile</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42174</th>\n", "      <td>FINAL FANTASY BRAVE EXVIUS</td>\n", "      <td>I've played since started. It impossible beat everything unless TMRs never get unless fork thous...</td>\n", "      <td>0</td>\n", "      <td>-0.186667</td>\n", "      <td>0.473333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51567</th>\n", "      <td>GPS Speedometer and Odometer</td>\n", "      <td>Works great</td>\n", "      <td>2</td>\n", "      <td>0.800000</td>\n", "      <td>0.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23313</th>\n", "      <td><PERSON> - selfie, beauty camera, photo editor</td>\n", "      <td>Amazing app..thanks a lot</td>\n", "      <td>2</td>\n", "      <td>0.600000</td>\n", "      <td>0.900000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16631</th>\n", "      <td>BlueJeans for Android</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44369</th>\n", "      <td>Farm Heroes Saga</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16802</th>\n", "      <td>Booking.com Travel Deals</td>\n", "      <td>They canceled booking without informing</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41299</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td>I'm blown away! After lag battery life issues Nova, I turned <PERSON><PERSON> fill home screen need...</td>\n", "      <td>2</td>\n", "      <td>0.100000</td>\n", "      <td>0.514286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41048</th>\n", "      <td>Events High - Meet Your City!</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28425</th>\n", "      <td>Colorfy: Coloring Book for Adults - Free</td>\n", "      <td>I hate pay draw u try make pitcher work looks weird least good drawings I give 3</td>\n", "      <td>0</td>\n", "      <td>-0.225000</td>\n", "      <td>0.725000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41805</th>\n", "      <td>Extreme Match</td>\n", "      <td>Colorful graphics fun play!</td>\n", "      <td>2</td>\n", "      <td>0.337500</td>\n", "      <td>0.300000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35104</th>\n", "      <td>Docs To Goâ¢ Free Office Suite</td>\n", "      <td>I able find way print! I snap screen shot print picture document. User friendly ways maybe but, ...</td>\n", "      <td>2</td>\n", "      <td>0.546875</td>\n", "      <td>0.562500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62487</th>\n", "      <td><PERSON>ya - Caller ID &amp; Block</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40820</th>\n", "      <td>Etsy: Handmade &amp; Vintage Goods</td>\n", "      <td>favorites lists work anymore - thought saw reviews said thing</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10101</th>\n", "      <td>BBC News</td>\n", "      <td>Keeps closing updating. Fed app.</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31343</th>\n", "      <td>Cute Pet Puppies</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63914</th>\n", "      <td>HotelTonight: Book amazing deals at great hotels</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58681</th>\n", "      <td>HD Widgets</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10209</th>\n", "      <td>BBC Sport</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45781</th>\n", "      <td>Final Fantasy XV: A New Empire</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23747</th>\n", "      <td>Candy Crush Soda Saga</td>\n", "      <td>You made hard pass game. I hate always says \"you're close\" sip it!!!!! Suggestion!!!!make easier...</td>\n", "      <td>0</td>\n", "      <td>-0.184524</td>\n", "      <td>0.420238</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16408</th>\n", "      <td>Bloomberg Professional</td>\n", "      <td>Very poor performance Android devices!!!</td>\n", "      <td>0</td>\n", "      <td>-1.000000</td>\n", "      <td>0.780000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37124</th>\n", "      <td>Dresses Ideas &amp; Fashions +3000</td>\n", "      <td>The full show phone pls I need help open</td>\n", "      <td>2</td>\n", "      <td>0.175000</td>\n", "      <td>0.525000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8397</th>\n", "      <td>Apartment List: Housing, Apt, and Property Rentals</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31802</th>\n", "      <td>DEAD TARGET: FPS Zombie Apocalypse Survival Games</td>\n", "      <td>Cool game love shooting zombies.</td>\n", "      <td>2</td>\n", "      <td>0.150000</td>\n", "      <td>0.550000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63611</th>\n", "      <td>Hostelworld: Hostels &amp; Cheap Hotels Travel App</td>\n", "      <td>Best book backpacker</td>\n", "      <td>2</td>\n", "      <td>1.000000</td>\n", "      <td>0.300000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56341</th>\n", "      <td>Google Pay</td>\n", "      <td>If got fingerprint security fantastic. No need open app, unlock phone tap. Gift / loyalty barcod...</td>\n", "      <td>2</td>\n", "      <td>0.111224</td>\n", "      <td>0.412245</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47572</th>\n", "      <td>Flow Free</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4447</th>\n", "      <td>Adult Glitter Color by Number Book - Sandbox Pages</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47716</th>\n", "      <td>Fly Delta</td>\n", "      <td>This would update, reporting server error past three days. Also, would lose trip \"Today\" tab, th...</td>\n", "      <td>0</td>\n", "      <td>-0.250000</td>\n", "      <td>0.250000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44384</th>\n", "      <td>Farm Heroes Saga</td>\n", "      <td>It creatively made keeps occupied every night I go sleep. Its AWESOME!!</td>\n", "      <td>2</td>\n", "      <td>0.750000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58083</th>\n", "      <td>H TV</td>\n", "      <td>Good</td>\n", "      <td>2</td>\n", "      <td>0.700000</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3710</th>\n", "      <td>Accounting App - Zoho Books</td>\n", "      <td>Good value similar advantages</td>\n", "      <td>2</td>\n", "      <td>0.350000</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35573</th>\n", "      <td>Domino's Pizza USA</td>\n", "      <td>The makes easy order Dom<PERSON>'s. Coupons always available simple apply. The tracker notoriously in...</td>\n", "      <td>2</td>\n", "      <td>0.277778</td>\n", "      <td>0.530159</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36180</th>\n", "      <td>DraftKings - Daily Fantasy Sports</td>\n", "      <td>Easy money</td>\n", "      <td>2</td>\n", "      <td>0.433333</td>\n", "      <td>0.833333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>57588</th>\n", "      <td>Groovebook Photo Books &amp; Gifts</td>\n", "      <td>It works perfectly certain time couple months stop working well. I selected photos never finish ...</td>\n", "      <td>2</td>\n", "      <td>0.054762</td>\n", "      <td>0.640476</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2515</th>\n", "      <td>ABC Preschool Free</td>\n", "      <td>Good Help grow easily</td>\n", "      <td>2</td>\n", "      <td>0.566667</td>\n", "      <td>0.716667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51240</th>\n", "      <td>GO Keyboard - Cute Emojis, Themes and GIFs</td>\n", "      <td>in ã Ã´VÃ« THÃ« kÃ«Ã½ÃÃ´Ã¢rd</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13482</th>\n", "      <td>Beauty Camera - <PERSON><PERSON></td>\n", "      <td>goes ads cannot stop them. used love it. uninstalling it.</td>\n", "      <td>2</td>\n", "      <td>0.500000</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26345</th>\n", "      <td><PERSON>ting For Free App</td>\n", "      <td>It's got quite lot people here. It lags little looks bit dated messaging seems fine. It tell two...</td>\n", "      <td>2</td>\n", "      <td>0.109722</td>\n", "      <td>0.466667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51982</th>\n", "      <td>Galaxy Attack: Alien Shooter</td>\n", "      <td>Thanks ruining game play</td>\n", "      <td>0</td>\n", "      <td>-0.100000</td>\n", "      <td>0.300000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23845</th>\n", "      <td>Candy Day</td>\n", "      <td>This game hilarious! Love it!</td>\n", "      <td>2</td>\n", "      <td>0.283333</td>\n", "      <td>0.666667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10259</th>\n", "      <td>BBC Sport</td>\n", "      <td>Good I cannot get videos play</td>\n", "      <td>2</td>\n", "      <td>0.700000</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9146</th>\n", "      <td>Arrow.io</td>\n", "      <td>Killer</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45630</th>\n", "      <td>Filters for B Live</td>\n", "      <td>As for the rest of the software, I thought it was auto-ridiculous</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30844</th>\n", "      <td>Crossy Road</td>\n", "      <td>Teat</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>48066</th>\n", "      <td>Football Live Scores</td>\n", "      <td>Would 5but needs 2 b kept updated oztumer jackson walsall mayb teams like needs updating</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51933</th>\n", "      <td>Galactic Core Free Wallpaper</td>\n", "      <td>It's okay... Every Time I restart phone, wallpaper removed!! It fine first, driving crazy! May u...</td>\n", "      <td>2</td>\n", "      <td>0.179583</td>\n", "      <td>0.486667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42953</th>\n", "      <td>Facebook</td>\n", "      <td>Decided simplify review issue. You disconnecting friends. A post three minutes ago might turn 'm...</td>\n", "      <td>2</td>\n", "      <td>0.177778</td>\n", "      <td>0.386111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7493</th>\n", "      <td>Angry Birds Classic</td>\n", "      <td>I hate This Game used Fun well I hate LIKE SPENDING STARS TO PLAY happened please remove having ...</td>\n", "      <td>0</td>\n", "      <td>-0.320000</td>\n", "      <td>0.520000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8070</th>\n", "      <td>Anthem Anywhere</td>\n", "      <td>This doesnt givr useful information. Summary gives plan details deductible. It show information ...</td>\n", "      <td>2</td>\n", "      <td>0.257143</td>\n", "      <td>0.285714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26525</th>\n", "      <td>Cinemark Theatres</td>\n", "      <td>It would great actually worked consistently. The too. Ugh</td>\n", "      <td>2</td>\n", "      <td>0.350000</td>\n", "      <td>0.366667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27949</th>\n", "      <td>Color by Number - Draw Sandbox Pixel Art</td>\n", "      <td>I enjoy coloring pic numbers scattered wereever. Like &amp; makes scenes&amp; takes 40 min. find ( frust...</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.700000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26721</th>\n", "      <td>Citizens Bank Mobile Banking</td>\n", "      <td>I'm beginning happy app, every time I try I get screen says \"mobile banking unavailable\", really...</td>\n", "      <td>2</td>\n", "      <td>0.375000</td>\n", "      <td>0.456250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43562</th>\n", "      <td>Family Dollar</td>\n", "      <td>Love</td>\n", "      <td>2</td>\n", "      <td>0.500000</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8058</th>\n", "      <td>Anthem Anywhere</td>\n", "      <td>The completely unresponsive. I tried uninstalling reinstalling nothing. Not sure happened used w...</td>\n", "      <td>0</td>\n", "      <td>-0.316667</td>\n", "      <td>0.762963</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52764</th>\n", "      <td>Garena Free Fire</td>\n", "      <td>New updates I bad see waiting player game open</td>\n", "      <td>0</td>\n", "      <td>-0.240909</td>\n", "      <td>0.505303</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1742</th>\n", "      <td>8fit Workouts &amp; Meal Planner</td>\n", "      <td>I can't sign up!!! Keeps saying \"error connecting server.try again\" Yet connection fine, working...</td>\n", "      <td>2</td>\n", "      <td>0.416667</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35836</th>\n", "      <td>DoorDash - Food Delivery</td>\n", "      <td>While I like restaurants delivery apps, awful. Whenever I place order, delivery/pickup time cons...</td>\n", "      <td>0</td>\n", "      <td>-0.315741</td>\n", "      <td>0.491667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>22219 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                                                      App  \\\n", "47636                                           Flow Free   \n", "24091                                Capital OneÂ® Mobile   \n", "42174                          FINAL FANTASY BRAVE EXVIUS   \n", "51567                        GPS Speedometer and Odometer   \n", "23313  Candy Camera - selfie, beauty camera, photo editor   \n", "16631                               BlueJeans for Android   \n", "44369                                    Farm Heroes Saga   \n", "16802                            Booking.com Travel Deals   \n", "41299                                       <PERSON><PERSON>   \n", "41048                       Events High - Meet Your City!   \n", "28425            Colorfy: Coloring Book for Adults - Free   \n", "41805                                       Extreme Match   \n", "35104                     Docs To Goâ¢ Free Office Suite   \n", "62487                            Hiya - Caller ID & Block   \n", "40820                      Etsy: Handmade & Vintage Goods   \n", "10101                                            BBC News   \n", "31343                                    Cute Pet Puppies   \n", "63914    HotelTonight: Book amazing deals at great hotels   \n", "58681                                          HD Widgets   \n", "10209                                           BBC Sport   \n", "45781                      Final Fantasy XV: A New Empire   \n", "23747                               Candy Crush Soda Saga   \n", "16408                              Bloomberg Professional   \n", "37124                      Dresses Ideas & Fashions +3000   \n", "8397   Apartment List: Housing, Apt, and Property Rentals   \n", "31802   DEAD TARGET: FPS Zombie Apocalypse Survival Games   \n", "63611      Hostelworld: Hostels & Cheap Hotels Travel App   \n", "56341                                          Google Pay   \n", "47572                                           Flow Free   \n", "4447   Adult Glitter Color by Number Book - Sandbox Pages   \n", "...                                                   ...   \n", "47716                                           Fly Delta   \n", "44384                                    Farm Heroes Saga   \n", "58083                                                H TV   \n", "3710                          Accounting App - Zoho Books   \n", "35573                                  Domino's Pizza USA   \n", "36180                   DraftKings - Daily Fantasy Sports   \n", "57588                      Groovebook Photo Books & Gifts   \n", "2515                                   ABC Preschool Free   \n", "51240          GO Keyboard - Cute Emojis, Themes and GIFs   \n", "13482                       Beauty Camera - <PERSON>ie Camera   \n", "26345                       <PERSON> Dating For Free App   \n", "51982                        Galaxy Attack: Alien Shooter   \n", "23845                                           Candy Day   \n", "10259                                           BBC Sport   \n", "9146                                             Arrow.io   \n", "45630                                  Filters for B Live   \n", "30844                                         Crossy Road   \n", "48066                                Football Live Scores   \n", "51933                        Galactic Core Free Wallpaper   \n", "42953                                            Facebook   \n", "7493                                  Angry Birds Classic   \n", "8070                                      Anthem Anywhere   \n", "26525                                   Cinemark Theatres   \n", "27949            Color by Number - Draw Sandbox Pixel Art   \n", "26721                        Citizens Bank Mobile Banking   \n", "43562                                       Family Dollar   \n", "8058                                      Anthem Anywhere   \n", "52764                                    Garena Free Fire   \n", "1742                         8fit Workouts & Meal Planner   \n", "35836                            DoorDash - Food Delivery   \n", "\n", "                                                                                         Translated_Review  \\\n", "47636  Well, amazing simple game. Definitely addicting like puzzles. IN every pack beaten including one...   \n", "24091                                                                                                  NaN   \n", "42174  I've played since started. It impossible beat everything unless TMRs never get unless fork thous...   \n", "51567                                                                                          Works great   \n", "23313                                                                            Amazing app..thanks a lot   \n", "16631                                                                                                  NaN   \n", "44369                                                                                                  NaN   \n", "16802                                                              They canceled booking without informing   \n", "41299  I'm blown away! After lag battery life issues Nova, I turned <PERSON><PERSON> fill home screen need...   \n", "41048                                                                                                  NaN   \n", "28425                     I hate pay draw u try make pitcher work looks weird least good drawings I give 3   \n", "41805                                                                          Colorful graphics fun play!   \n", "35104  I able find way print! I snap screen shot print picture document. User friendly ways maybe but, ...   \n", "62487                                                                                                  NaN   \n", "40820                                        favorites lists work anymore - thought saw reviews said thing   \n", "10101                                                                     Keeps closing updating. Fed app.   \n", "31343                                                                                                  NaN   \n", "63914                                                                                                  NaN   \n", "58681                                                                                                  NaN   \n", "10209                                                                                                  NaN   \n", "45781                                                                                                  NaN   \n", "23747  You made hard pass game. I hate always says \"you're close\" sip it!!!!! Suggestion!!!!make easier...   \n", "16408                                                             Very poor performance Android devices!!!   \n", "37124                                                             The full show phone pls I need help open   \n", "8397                                                                                                   NaN   \n", "31802                                                                     Cool game love shooting zombies.   \n", "63611                                                                                 Best book backpacker   \n", "56341  If got fingerprint security fantastic. No need open app, unlock phone tap. Gift / loyalty barcod...   \n", "47572                                                                                                  NaN   \n", "4447                                                                                                   NaN   \n", "...                                                                                                    ...   \n", "47716  This would update, reporting server error past three days. Also, would lose trip \"Today\" tab, th...   \n", "44384                              It creatively made keeps occupied every night I go sleep. Its AWESOME!!   \n", "58083                                                                                                 Good   \n", "3710                                                                         Good value similar advantages   \n", "35573  The makes easy order Dom<PERSON>'s. Coupons always available simple apply. The tracker notoriously in...   \n", "36180                                                                                           Easy money   \n", "57588  It works perfectly certain time couple months stop working well. I selected photos never finish ...   \n", "2515                                                                                 Good Help grow easily   \n", "51240                                                                      in ã Ã´VÃ« THÃ« kÃ«Ã½ÃÃ´Ã¢rd   \n", "13482                                            goes ads cannot stop them. used love it. uninstalling it.   \n", "26345  It's got quite lot people here. It lags little looks bit dated messaging seems fine. It tell two...   \n", "51982                                                                             Thanks ruining game play   \n", "23845                                                                        This game hilarious! Love it!   \n", "10259                                                                        Good I cannot get videos play   \n", "9146                                                                                                Killer   \n", "45630                                    As for the rest of the software, I thought it was auto-ridiculous   \n", "30844                                                                                                 Teat   \n", "48066             Would 5but needs 2 b kept updated oztumer jackson walsall mayb teams like needs updating   \n", "51933  It's okay... Every Time I restart phone, wallpaper removed!! It fine first, driving crazy! May u...   \n", "42953  Decided simplify review issue. You disconnecting friends. A post three minutes ago might turn 'm...   \n", "7493   I hate This Game used Fun well I hate LIKE SPENDING STARS TO PLAY happened please remove having ...   \n", "8070   This doesnt givr useful information. Summary gives plan details deductible. It show information ...   \n", "26525                                            It would great actually worked consistently. The too. Ugh   \n", "27949  I enjoy coloring pic numbers scattered wereever. Like & makes scenes& takes 40 min. find ( frust...   \n", "26721  I'm beginning happy app, every time I try I get screen says \"mobile banking unavailable\", really...   \n", "43562                                                                                                 Love   \n", "8058   The completely unresponsive. I tried uninstalling reinstalling nothing. Not sure happened used w...   \n", "52764                                                       New updates I bad see waiting player game open   \n", "1742   I can't sign up!!! Keeps saying \"error connecting server.try again\" Yet connection fine, working...   \n", "35836  While I like restaurants delivery apps, awful. Whenever I place order, delivery/pickup time cons...   \n", "\n", "       Sentiment  Sentiment_Polarity  Sentiment_Subjectivity  \n", "47636          2            0.240000                0.491429  \n", "24091          3                 NaN                     NaN  \n", "42174          0           -0.186667                0.473333  \n", "51567          2            0.800000                0.750000  \n", "23313          2            0.600000                0.900000  \n", "16631          3                 NaN                     NaN  \n", "44369          3                 NaN                     NaN  \n", "16802          1            0.000000                0.000000  \n", "41299          2            0.100000                0.514286  \n", "41048          3                 NaN                     NaN  \n", "28425          0           -0.225000                0.725000  \n", "41805          2            0.337500                0.300000  \n", "35104          2            0.546875                0.562500  \n", "62487          3                 NaN                     NaN  \n", "40820          1            0.000000                0.000000  \n", "10101          1            0.000000                0.000000  \n", "31343          3                 NaN                     NaN  \n", "63914          3                 NaN                     NaN  \n", "58681          3                 NaN                     NaN  \n", "10209          3                 NaN                     NaN  \n", "45781          3                 NaN                     NaN  \n", "23747          0           -0.184524                0.420238  \n", "16408          0           -1.000000                0.780000  \n", "37124          2            0.175000                0.525000  \n", "8397           3                 NaN                     NaN  \n", "31802          2            0.150000                0.550000  \n", "63611          2            1.000000                0.300000  \n", "56341          2            0.111224                0.412245  \n", "47572          3                 NaN                     NaN  \n", "4447           3                 NaN                     NaN  \n", "...          ...                 ...                     ...  \n", "47716          0           -0.250000                0.250000  \n", "44384          2            0.750000                1.000000  \n", "58083          2            0.700000                0.600000  \n", "3710           2            0.350000                0.500000  \n", "35573          2            0.277778                0.530159  \n", "36180          2            0.433333                0.833333  \n", "57588          2            0.054762                0.640476  \n", "2515           2            0.566667                0.716667  \n", "51240          1            0.000000                0.000000  \n", "13482          2            0.500000                0.600000  \n", "26345          2            0.109722                0.466667  \n", "51982          0           -0.100000                0.300000  \n", "23845          2            0.283333                0.666667  \n", "10259          2            0.700000                0.600000  \n", "9146           1            0.000000                0.000000  \n", "45630          1            0.000000                0.000000  \n", "30844          1            0.000000                0.000000  \n", "48066          1            0.000000                0.000000  \n", "51933          2            0.179583                0.486667  \n", "42953          2            0.177778                0.386111  \n", "7493           0           -0.320000                0.520000  \n", "8070           2            0.257143                0.285714  \n", "26525          2            0.350000                0.366667  \n", "27949          1            0.000000                0.700000  \n", "26721          2            0.375000                0.456250  \n", "43562          2            0.500000                0.600000  \n", "8058           0           -0.316667                0.762963  \n", "52764          0           -0.240909                0.505303  \n", "1742           2            0.416667                0.500000  \n", "35836          0           -0.315741                0.491667  \n", "\n", "[22219 rows x 5 columns]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# >> Examples for duplicate detection and removal\n", "# House dataset has no duplicate anyway\n", "# Available strategies =\n", "#        'ED':  exact duplicate detection/removal or\n", "#        'AD':  for aproximate duplicate records detection and removal\n", "#        based on <PERSON><PERSON><PERSON> similarity \n", "\n", "\n", "# import the Duplicate_detector class\n", "import learn2clean.duplicate_detection.duplicate_detector as dup\n", "\n", "#Remove exact duplicates with 'ED' strategy of the Duplicate_detector class\n", "\n", "dup1 = dup.Duplicate_detector(gpsu_encoded, strategy='ED', verbose=True).transform()\n", "\n", "dup1['train'].head()\n", "\n", "#Remove approximate duplicates with thresholding <PERSON><PERSON><PERSON> similarity \n", "# using 'AD'strategy of the Duplicate_detector class\n", "dup2 = dup.Duplicate_detector(gpsu_encoded, strategy='AD', threshold = .6, verbose=True).transform()\n", "\n", "dup2['train']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Detect inconsistencies"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Original size of traning set 43077\n", ">>Consistency checking\n", "* For train dataset\n", "Patterns:\n", "         col  num        pattern\n", "0        App    0  '^[A-Za-z]+$'\n", "1  Sentiment    0    '^[1-2]+$^'\n", "\n", "Number of pattern violations on variable ' App 'for pattern# 0 : 39810\n", "Indexes of rows to be removed: []\n", "****** 3267 39810\n", "* For test dataset\n", "Patterns:\n", "         col  num        pattern\n", "0        App    0  '^[A-Za-z]+$'\n", "1  Sentiment    0    '^[1-2]+$^'\n", "\n", "Number of pattern violations on variable ' App 'for pattern# 0 : 19605\n", "Indexes of rows to be removed: []\n", "****** 1613 19605\n", "Consistency checking done -- CPU time: 0.17340803146362305 seconds\n", "After pattern checksing 3267\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>App</th>\n", "      <th>Translated_Review</th>\n", "      <th>Sentiment</th>\n", "      <th>Sentiment_Polarity</th>\n", "      <th>Sentiment_Subjectivity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2854</th>\n", "      <td>ANA</td>\n", "      <td>problem remember login ID password .</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14556</th>\n", "      <td>BeyondMenu Food Delivery</td>\n", "      <td>Love ease simplicity!</td>\n", "      <td>2</td>\n", "      <td>0.625000</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19582</th>\n", "      <td>CBS Sports App - Scores, News, Stats &amp; Watch Live</td>\n", "      <td>It's good, works. 50% time bugs show everything going (mostly MLB play play). Other that, gets j...</td>\n", "      <td>2</td>\n", "      <td>0.358333</td>\n", "      <td>0.491667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20330</th>\n", "      <td>CNN Breaking US &amp; World News</td>\n", "      <td>The works well. Too bad President would call CNN fake media enemy people. That authoritarian cou...</td>\n", "      <td>0</td>\n", "      <td>-0.066667</td>\n", "      <td>0.805556</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15369</th>\n", "      <td>Bleacher Report: sports news, scores, &amp; highlights</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>42823</th>\n", "      <td><PERSON>, <PERSON>ie Editor - Sweet Camera</td>\n", "      <td>Do make mistake downloading it. It takes forever load watch 30 secs videos filters. Dear program...</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26023</th>\n", "      <td>Chictopia</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12855</th>\n", "      <td>Baseball Boy!</td>\n", "      <td>Its actually embarrassing hard game shoves ads throat. It's even good game throwing ads left rig...</td>\n", "      <td>0</td>\n", "      <td>-0.000541</td>\n", "      <td>0.288853</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19421</th>\n", "      <td>CBS - Full Episodes &amp; Live TV</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35257</th>\n", "      <td>Dog Run - Pet Dog Simulator</td>\n", "      <td>SO THIS GAME IS TOTALLY AMAZING AND I REALLY LOVE DOGS. ESPECIALLY WHEN THEY ARE FURY AND I WISH...</td>\n", "      <td>0</td>\n", "      <td>-0.030000</td>\n", "      <td>0.745000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49817</th>\n", "      <td>FreePrints â Free Photos Delivered</td>\n", "      <td>I able complete process, saying apt., num/zip code incorrect &amp; I know apt., num &amp; zip code corre...</td>\n", "      <td>2</td>\n", "      <td>0.275000</td>\n", "      <td>0.489286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54331</th>\n", "      <td>Golden Dictionary (EN-AR)</td>\n", "      <td>Nice</td>\n", "      <td>2</td>\n", "      <td>0.600000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28022</th>\n", "      <td>Color by Number â New Coloring Book</td>\n", "      <td>I'm addicted site. I love watching pattern come life I finish. I wish would add patterns often I...</td>\n", "      <td>2</td>\n", "      <td>0.266667</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4463</th>\n", "      <td>Adult Glitter Color by Number Book - Sandbox Pages</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11179</th>\n", "      <td>Babbel â Learn Languages</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25933</th>\n", "      <td>Chick-fil-A</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14887</th>\n", "      <td>Binaural Beats Meditation</td>\n", "      <td>Can't figure works</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36807</th>\n", "      <td>Drawing for Kids Learning Games for Toddlers age 3</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37401</th>\n", "      <td>Dude Perfect 2</td>\n", "      <td>I really enjoy game however feels like pay fully enjoy lots paid content , adds exclusive offers...</td>\n", "      <td>2</td>\n", "      <td>0.166667</td>\n", "      <td>0.380000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61842</th>\n", "      <td>Hill Climb Racing</td>\n", "      <td>Time killing game. Even better new levels vehicles</td>\n", "      <td>2</td>\n", "      <td>0.078788</td>\n", "      <td>0.451515</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40319</th>\n", "      <td>Entel</td>\n", "      <td>Very heavy, I can activate always connected by the application.</td>\n", "      <td>0</td>\n", "      <td>-0.260000</td>\n", "      <td>0.650000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33567</th>\n", "      <td>Delivery ClubâÐÐ¾ÑÑÐ°Ð²ÐºÐ° ÐµÐ´Ñ:Ð¿Ð¸ÑÑÐ°,ÑÑÑÐ¸,Ð±ÑÑÐ³ÐµÑ,ÑÐ°Ð»Ð°Ñ</td>\n", "      <td>It is very convenient to order food from different places from one place!</td>\n", "      <td>2</td>\n", "      <td>0.100000</td>\n", "      <td>0.450000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18398</th>\n", "      <td>Bubble Shooter Space</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16892</th>\n", "      <td>Bowmasters</td>\n", "      <td>The main problem ads. Their many ads I like niether care about. And I going pay ad removal cuz w...</td>\n", "      <td>2</td>\n", "      <td>0.333333</td>\n", "      <td>0.416667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47005</th>\n", "      <td>Flip the Gun - Simulator Game</td>\n", "      <td>Great game way many ads. The game great time killer original addicting game. But 15 second ad ev...</td>\n", "      <td>2</td>\n", "      <td>0.126786</td>\n", "      <td>0.425000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60661</th>\n", "      <td>HealtheLife</td>\n", "      <td>This worked perfectly fine galaxy s7 I upgraded s9+ work all! I log says \"sorry error occurred, ...</td>\n", "      <td>2</td>\n", "      <td>0.240278</td>\n", "      <td>0.766667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21296</th>\n", "      <td>Caller ID +</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>752</th>\n", "      <td>365Scores - Live Scores</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33259</th>\n", "      <td>Dating App, Flirt &amp; Chat : W-Match</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59621</th>\n", "      <td>Hairstyles step by step</td>\n", "      <td>I loved much</td>\n", "      <td>2</td>\n", "      <td>0.450000</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24950</th>\n", "      <td>Chakra Cleansing</td>\n", "      <td>Fantastic</td>\n", "      <td>2</td>\n", "      <td>0.400000</td>\n", "      <td>0.900000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63634</th>\n", "      <td>Hot Wheels: Race Off</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26047</th>\n", "      <td>Chictopia</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1960</th>\n", "      <td>A Manual of Acupuncture</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52159</th>\n", "      <td>Galaxy Gift</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63308</th>\n", "      <td>Honkai Impact 3rd</td>\n", "      <td>It's good game, controls fantastic gameplay smooth. But, bunch little kinks made experience lot ...</td>\n", "      <td>0</td>\n", "      <td>-0.031944</td>\n", "      <td>0.477778</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26312</th>\n", "      <td>Choices: Stories You Play</td>\n", "      <td>There room improvement. While super fun game, genuinely interesting stories, whole concept limit...</td>\n", "      <td>2</td>\n", "      <td>0.123333</td>\n", "      <td>0.434167</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43312</th>\n", "      <td>Fair: A New Way To Own A Car</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54682</th>\n", "      <td>Golfshot: Golf GPS + Tee Times</td>\n", "      <td>Like looks good. Went pro version non stop crashing</td>\n", "      <td>2</td>\n", "      <td>0.700000</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40816</th>\n", "      <td>Etsy: Handmade &amp; Vintage Goods</td>\n", "      <td>It would wonderful favorites lists would actually show I try view them. Constant problem last se...</td>\n", "      <td>2</td>\n", "      <td>0.200000</td>\n", "      <td>0.300000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51382</th>\n", "      <td>GO SMS Pro - Messenger, Free Themes, Emoji</td>\n", "      <td>This cool messaging lots</td>\n", "      <td>2</td>\n", "      <td>0.350000</td>\n", "      <td>0.650000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30344</th>\n", "      <td>Create A Superhero HD</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10297</th>\n", "      <td>BBM - Free Calls &amp; Messages</td>\n", "      <td>Fails send half time. Support useless. Going give days buhbye. There shortcuts insane. Every mes...</td>\n", "      <td>0</td>\n", "      <td>-0.250595</td>\n", "      <td>0.470238</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30570</th>\n", "      <td>Credit Sesame</td>\n", "      <td>let sign-up says cannot verify address, credit carma works fine going try out, guess not. ... .....</td>\n", "      <td>2</td>\n", "      <td>0.304167</td>\n", "      <td>0.581250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30583</th>\n", "      <td>Credit Sesame</td>\n", "      <td>Tells score tells ways improve it. Breaks categories. From I hear, also monitors credit</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16168</th>\n", "      <td>Blogger</td>\n", "      <td>I feel like updated need like mode view. It nice creatives options. It nice view similar WordPre...</td>\n", "      <td>2</td>\n", "      <td>0.335714</td>\n", "      <td>0.625000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55315</th>\n", "      <td>Google Chrome: Fast &amp; Secure</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24520</th>\n", "      <td>Cartoon Network App</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55581</th>\n", "      <td>Google Drive</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55046</th>\n", "      <td>Google Analytics</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54589</th>\n", "      <td>GolfNow: Te<PERSON> Time Deals at Golf Courses, Golf GPS</td>\n", "      <td>Booked reservation using duplicated reservation billed twice!!! I able cancel second reservation...</td>\n", "      <td>2</td>\n", "      <td>0.333333</td>\n", "      <td>0.504630</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43254</th>\n", "      <td>Facetune - Ad Free</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61434</th>\n", "      <td>Hero Hunters</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1811</th>\n", "      <td>95Live -SG#1 Live Streaming App</td>\n", "      <td>Only written Chinese</td>\n", "      <td>1</td>\n", "      <td>0.000000</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51341</th>\n", "      <td>GO Notifier</td>\n", "      <td>Bugs I ticked Go Notifier accessibility, still same. I couldnt set notification facebook, whatsa...</td>\n", "      <td>0</td>\n", "      <td>-0.350000</td>\n", "      <td>0.395833</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53604</th>\n", "      <td>Girls Online Talk - Free Text and Video Chat</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17357</th>\n", "      <td>Boxed Wholesale</td>\n", "      <td>The prices still higher compared WM, Cost/of product choices limited. Waste time memory space ph...</td>\n", "      <td>0</td>\n", "      <td>-0.007143</td>\n", "      <td>0.214286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>37191</th>\n", "      <td>Droid Zap by Motorola</td>\n", "      <td>Love Came preinstalled Motorola, I friends install Samsung phones worked great.</td>\n", "      <td>2</td>\n", "      <td>0.650000</td>\n", "      <td>0.675000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22657</th>\n", "      <td>Camera MX - Free Photo &amp; Video Camera</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5102</th>\n", "      <td>Airport + Flight Tracker Radar</td>\n", "      <td>NaN</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>43077 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                                                                                       App  \\\n", "2854                                                                                   ANA   \n", "14556                                                             BeyondMenu Food Delivery   \n", "19582                                    CBS Sports App - Scores, News, Stats & Watch Live   \n", "20330                                                         CNN Breaking US & World News   \n", "15369                                   Bleacher Report: sports news, scores, & highlights   \n", "42823                                            <PERSON>, Selfie Editor - Sweet Camera   \n", "26023                                                                            Chictopia   \n", "12855                                                                        Baseball Boy!   \n", "19421                                                        CBS - Full Episodes & Live TV   \n", "35257                                                          Dog Run - Pet Dog Simulator   \n", "49817                                                 FreePrints â Free Photos Delivered   \n", "54331                                                            Golden Dictionary (EN-AR)   \n", "28022                                                Color by Number â New Coloring Book   \n", "4463                                    Adult Glitter Color by Number Book - Sandbox Pages   \n", "11179                                                           Babbel â Learn Languages   \n", "25933                                                                          Chick-fil-A   \n", "14887                                                            Binaural Beats Meditation   \n", "36807                                   Drawing for Kids Learning Games for Toddlers age 3   \n", "37401                                                                       Dude Perfect 2   \n", "61842                                                                    Hill Climb Racing   \n", "40319                                                                                Entel   \n", "33567  Delivery ClubâÐÐ¾ÑÑÐ°Ð²ÐºÐ° ÐµÐ´Ñ:Ð¿Ð¸ÑÑÐ°,ÑÑÑÐ¸,Ð±ÑÑÐ³ÐµÑ,ÑÐ°Ð»Ð°Ñ   \n", "18398                                                                 Bubble Shooter Space   \n", "16892                                                                           Bowmasters   \n", "47005                                                        Flip the Gun - Simulator Game   \n", "60661                                                                          HealtheLife   \n", "21296                                                                          Caller ID +   \n", "752                                                                365Scores - Live Scores   \n", "33259                                                   Da<PERSON>, Flirt & Chat : W-Match   \n", "59621                                                              Hairstyles step by step   \n", "...                                                                                    ...   \n", "24950                                                                     Chakra Cleansing   \n", "63634                                                                 Hot Wheels: Race Off   \n", "26047                                                                            Chictopia   \n", "1960                                                               A Manual of Acupuncture   \n", "52159                                                                          Galaxy Gift   \n", "63308                                                                    Honkai Impact 3rd   \n", "26312                                                            Choices: Stories You Play   \n", "43312                                                         Fair: A New Way To Own A Car   \n", "54682                                                       Golfshot: Golf GPS + Tee Times   \n", "40816                                                       Etsy: Handmade & Vintage Goods   \n", "51382                                           GO SMS Pro - Messenger, Free Themes, Emoji   \n", "30344                                                                Create A Superhero HD   \n", "10297                                                          BBM - Free Calls & Messages   \n", "30570                                                                        Credit Sesame   \n", "30583                                                                        Credit Sesame   \n", "16168                                                                              Blogger   \n", "55315                                                         Google Chrome: Fast & Secure   \n", "24520                                                                  Cartoon Network App   \n", "55581                                                                         Google Drive   \n", "55046                                                                     Google Analytics   \n", "54589                                    GolfNow: Tee Time Deals at Golf Courses, Golf GPS   \n", "43254                                                                   Facetune - Ad Free   \n", "61434                                                                         Hero Hunters   \n", "1811                                                       95Live -SG#1 Live Streaming App   \n", "51341                                                                          GO Notifier   \n", "53604                                         Girls Online Talk - Free Text and Video Chat   \n", "17357                                                                      Boxed Wholesale   \n", "37191                                                                Droid Zap by Motorola   \n", "22657                                                Camera MX - Free Photo & Video Camera   \n", "5102                                                        Airport + Flight Tracker Radar   \n", "\n", "                                                                                         Translated_Review  \\\n", "2854                                                                  problem remember login ID password .   \n", "14556                                                                                Love ease simplicity!   \n", "19582  It's good, works. 50% time bugs show everything going (mostly MLB play play). Other that, gets j...   \n", "20330  The works well. Too bad President would call CNN fake media enemy people. That authoritarian cou...   \n", "15369                                                                                                  NaN   \n", "42823  Do make mistake downloading it. It takes forever load watch 30 secs videos filters. Dear program...   \n", "26023                                                                                                  NaN   \n", "12855  Its actually embarrassing hard game shoves ads throat. It's even good game throwing ads left rig...   \n", "19421                                                                                                  NaN   \n", "35257  SO <PERSON>HIS GAME IS TOTALLY AMAZING AND I REALLY LOVE DOGS. ESPECIALLY WHEN THEY ARE FURY AND I WISH...   \n", "49817  I able complete process, saying apt., num/zip code incorrect & I know apt., num & zip code corre...   \n", "54331                                                                                                 Nice   \n", "28022  I'm addicted site. I love watching pattern come life I finish. I wish would add patterns often I...   \n", "4463                                                                                                   NaN   \n", "11179                                                                                                  NaN   \n", "25933                                                                                                  NaN   \n", "14887                                                                                   Can't figure works   \n", "36807                                                                                                  NaN   \n", "37401  I really enjoy game however feels like pay fully enjoy lots paid content , adds exclusive offers...   \n", "61842                                                   Time killing game. Even better new levels vehicles   \n", "40319                                      Very heavy, I can activate always connected by the application.   \n", "33567                            It is very convenient to order food from different places from one place!   \n", "18398                                                                                                  NaN   \n", "16892  The main problem ads. Their many ads I like niether care about. And I going pay ad removal cuz w...   \n", "47005  Great game way many ads. The game great time killer original addicting game. But 15 second ad ev...   \n", "60661  This worked perfectly fine galaxy s7 I upgraded s9+ work all! I log says \"sorry error occurred, ...   \n", "21296                                                                                                  NaN   \n", "752                                                                                                    NaN   \n", "33259                                                                                                  NaN   \n", "59621                                                                                         I loved much   \n", "...                                                                                                    ...   \n", "24950                                                                                            Fantastic   \n", "63634                                                                                                  NaN   \n", "26047                                                                                                  NaN   \n", "1960                                                                                                   NaN   \n", "52159                                                                                                  NaN   \n", "63308  It's good game, controls fantastic gameplay smooth. But, bunch little kinks made experience lot ...   \n", "26312  There room improvement. While super fun game, genuinely interesting stories, whole concept limit...   \n", "43312                                                                                                  NaN   \n", "54682                                                  Like looks good. Went pro version non stop crashing   \n", "40816  It would wonderful favorites lists would actually show I try view them. Constant problem last se...   \n", "51382                                                                             This cool messaging lots   \n", "30344                                                                                                  NaN   \n", "10297  Fails send half time. Support useless. Going give days buhbye. There shortcuts insane. Every mes...   \n", "30570  let sign-up says cannot verify address, credit carma works fine going try out, guess not. ... .....   \n", "30583              Tells score tells ways improve it. Breaks categories. From I hear, also monitors credit   \n", "16168  I feel like updated need like mode view. It nice creatives options. It nice view similar WordPre...   \n", "55315                                                                                                  NaN   \n", "24520                                                                                                  NaN   \n", "55581                                                                                                  NaN   \n", "55046                                                                                                  NaN   \n", "54589  Booked reservation using duplicated reservation billed twice!!! I able cancel second reservation...   \n", "43254                                                                                                  NaN   \n", "61434                                                                                                  NaN   \n", "1811                                                                                  Only written Chinese   \n", "51341  Bugs I ticked Go Notifier accessibility, still same. I couldnt set notification facebook, whatsa...   \n", "53604                                                                                                  NaN   \n", "17357  The prices still higher compared WM, Cost/of product choices limited. Waste time memory space ph...   \n", "37191                      <PERSON> Came preinstalled Motorola, I friends install Samsung phones worked great.   \n", "22657                                                                                                  NaN   \n", "5102                                                                                                   NaN   \n", "\n", "       Sentiment  Sentiment_Polarity  Sentiment_Subjectivity  \n", "2854           1            0.000000                0.000000  \n", "14556          2            0.625000                0.600000  \n", "19582          2            0.358333                0.491667  \n", "20330          0           -0.066667                0.805556  \n", "15369          3                 NaN                     NaN  \n", "42823          1            0.000000                0.000000  \n", "26023          3                 NaN                     NaN  \n", "12855          0           -0.000541                0.288853  \n", "19421          3                 NaN                     NaN  \n", "35257          0           -0.030000                0.745000  \n", "49817          2            0.275000                0.489286  \n", "54331          2            0.600000                1.000000  \n", "28022          2            0.266667                0.600000  \n", "4463           3                 NaN                     NaN  \n", "11179          3                 NaN                     NaN  \n", "25933          3                 NaN                     NaN  \n", "14887          1            0.000000                0.000000  \n", "36807          3                 NaN                     NaN  \n", "37401          2            0.166667                0.380000  \n", "61842          2            0.078788                0.451515  \n", "40319          0           -0.260000                0.650000  \n", "33567          2            0.100000                0.450000  \n", "18398          3                 NaN                     NaN  \n", "16892          2            0.333333                0.416667  \n", "47005          2            0.126786                0.425000  \n", "60661          2            0.240278                0.766667  \n", "21296          3                 NaN                     NaN  \n", "752            3                 NaN                     NaN  \n", "33259          3                 NaN                     NaN  \n", "59621          2            0.450000                0.500000  \n", "...          ...                 ...                     ...  \n", "24950          2            0.400000                0.900000  \n", "63634          3                 NaN                     NaN  \n", "26047          3                 NaN                     NaN  \n", "1960           3                 NaN                     NaN  \n", "52159          3                 NaN                     NaN  \n", "63308          0           -0.031944                0.477778  \n", "26312          2            0.123333                0.434167  \n", "43312          3                 NaN                     NaN  \n", "54682          2            0.700000                0.600000  \n", "40816          2            0.200000                0.300000  \n", "51382          2            0.350000                0.650000  \n", "30344          3                 NaN                     NaN  \n", "10297          0           -0.250595                0.470238  \n", "30570          2            0.304167                0.581250  \n", "30583          1            0.000000                0.000000  \n", "16168          2            0.335714                0.625000  \n", "55315          3                 NaN                     NaN  \n", "24520          3                 NaN                     NaN  \n", "55581          3                 NaN                     NaN  \n", "55046          3                 NaN                     NaN  \n", "54589          2            0.333333                0.504630  \n", "43254          3                 NaN                     NaN  \n", "61434          3                 NaN                     NaN  \n", "1811           1            0.000000                0.500000  \n", "51341          0           -0.350000                0.395833  \n", "53604          3                 NaN                     NaN  \n", "17357          0           -0.007143                0.214286  \n", "37191          2            0.650000                0.675000  \n", "22657          3                 NaN                     NaN  \n", "5102           3                 NaN                     NaN  \n", "\n", "[43077 rows x 5 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# >> Examples for inconsistency detection \n", "# Available consistency checking strategies :\n", "#            - 'CC': checks whether the data satisfy the constraints\n", "#                specified in a 'file_name'_constraint.tdda stored in 'save' directory\n", "#            - 'PC': checks whether the data satisfy the patterns\n", "#                specified in 'file_name'_patterns.txt stored in 'save' directory\n", "\n", "# import the Consistency_checker class                \n", "import learn2clean.consistency_checking.consistency_checker as cc\n", "          \n", "# discover the constraints from the input (train) dataset and store them in a file entitled 'gpsu'_constraint.tdda in the 'save' directory\n", "#cc.constraint_discovery(read_dataset('gpsu'), file_name='gpsu')\n", "\n", "# discover the patterns from the input (train) dataset and store them in a file entitled 'gpsu'_patterns.txt in the 'save' directory\n", "#cc.pattern_discovery(read_dataset('gpsu'), file_name='gpsu')\n", "\n", "# detect pattern violations with respect to a given file of patterns entitled 'gpsu'_constraint.tdda\" stored in the 'save' directory\n", "#cc.Consistency_checker(gpsu_encoded.copy(), strategy='CC', file_name='gpsu_example',verbose=False).transform()\n", "\n", "# detect pattern violations with respect to a given file of patterns entitled 'gpsu'_patterns.txt\" stored in the 'save' directory\n", "# with too strong patterns resulting in an empty dataframe fro the training set\n", "print(\"Original size of traning set\", len(gpsu_encoded['train']))\n", "p1= cc.Consistency_checker(gpsu_encoded.copy(), strategy='PC', file_name='gpsu_example', verbose=True).transform()\n", "print(\"After pattern checksing\",len(p1['train']))\n", "gpsu_encoded['train']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Select features"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Apply MR feature selection with missing threshold= 0.1\n", "                        missing_fraction\n", "Translated_Review               0.043103\n", "Sentiment_Polarity              0.042877\n", "Sentiment_Subjectivity          0.042877\n", "App                             0.000000\n", "Sentiment                       0.000000\n", "0 features with greater than 0.10 missing values.\n", "\n", "List of variables to be removed : []\n", "List of variables to be keep\n", "['Translated_Review', 'App', 'Sentiment_Polarity', 'Sentiment', 'Sentiment_Subjectivity']\n", "After feature selection:\n", "5 features remain\n", "['App', 'Sentiment_Polarity', 'Translated_Review', 'Sentiment', 'Sentiment_Subjectivity']\n", "Feature selection done -- CPU time: 0.01365208625793457 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Apply LC feature selection with threshold= 0.2\n", "Correlation matrix\n", "                        Sentiment  Sentiment_Polarity  Sentiment_Subjectivity\n", "Sentiment                1.000000            0.756047                0.183176\n", "Sentiment_Polarity       0.756047            1.000000                0.267144\n", "Sentiment_Subjectivity   0.183176            0.267144                1.000000\n", "2 features with linear correlation greater than 0.20.\n", "\n", "List of correlated variables to be removed : ['Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "List of numerical variables to be keep\n", "['Sentiment']\n", "After feature selection:\n", "3 features remain\n", "['Translated_Review', 'Sentiment', 'App']\n", "Feature selection done -- CPU time: 0.023252010345458984 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply L1 feature selection with threshold= 0.7\n", "After feature selection:\n", "3 features remain\n", "['Sentiment', 'Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "Feature selection done -- CPU time: 0.01258087158203125 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply IMP feature selection with threshold= 0.4\n", "After feature selection:\n", "3 features remain\n", "['Sentiment', 'Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "Feature selection done -- CPU time: 0.25987887382507324 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply Tree-based feature selection \n", "Best features to keep ['Sentiment']\n", "After feature selection:\n", "1 features remain\n", "['Sentiment']\n", "Feature selection done -- CPU time: 0.1333627700805664 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply WR feature selection\n", "Input variables must be non-negative. WR feature selection is only applied to positive variables.\n", "Best features to keep ['Sentiment', 'Sentiment_Subjectivity']\n", "After feature selection:\n", "2 features remain\n", "['Sentiment', 'Sentiment_Subjectivity']\n", "Feature selection done -- CPU time: 0.0216672420501709 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply WR feature selection\n", "Input variables must be non-negative. WR feature selection is only applied to positive variables.\n", "Best features to keep ['Sentiment', 'Sentiment_Subjectivity']\n", "After feature selection:\n", "2 features remain\n", "['Sentiment', 'Sentiment_Subjectivity']\n", "Feature selection done -- CPU time: 0.022714853286743164 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Warning: This strategy requires no missing values, so missing values have been removed applying DROP on the dataset.\n", "Apply SVC feature selection\n", "After feature selection:\n", "3 features remain\n", "['Sentiment', 'Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "Feature selection done -- CPU time: 0.159074068069458 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Apply VAR feature selection with threshold= 0.3\n", "After feature selection:\n", "3 features remain\n", "['Sentiment', 'Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "Feature selection done -- CPU time: 0.00569915771484375 seconds\n", "\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Apply VAR feature selection with threshold= 0.3\n", "After feature selection:\n", "3 features remain\n", "['Sentiment', 'Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "Feature selection done -- CPU time: 0.005470991134643555 seconds\n", "\n"]}, {"data": {"text/plain": ["{'train':        Sentiment  Sentiment_Polarity  Sentiment_Subjectivity\n", " 12307          0           -0.320000                0.420000\n", " 41643          3                 NaN                     NaN\n", " 43080          0           -0.180556                0.722222\n", " 34341          0           -0.100000                0.562500\n", " 30008          3                 NaN                     NaN\n", " 3118           2            0.577778                0.727778\n", " 57633          2            0.093750                0.472917\n", " 9637           3                 NaN                     NaN\n", " 59036          1            0.000000                0.000000\n", " 27988          2            0.100000                0.562500\n", " 8511           1            0.000000                0.000000\n", " 4707           3                 NaN                     NaN\n", " 20465          2            0.066667                0.550000\n", " 54407          2            0.433333                0.833333\n", " 21186          3                 NaN                     NaN\n", " 42227          1            0.000000                0.000000\n", " 51662          2            0.200000                0.200000\n", " 36746          3                 NaN                     NaN\n", " 22874          3                 NaN                     NaN\n", " 35367          2            0.275000                0.650000\n", " 10431          2            0.004583                0.444762\n", " 40681          0           -0.125000                0.375000\n", " 31621          2            0.285714                0.535714\n", " 26624          2            0.066667                0.541667\n", " 524            2            0.500000                0.500000\n", " 49497          3                 NaN                     NaN\n", " 13180          2            0.623437                0.575000\n", " 57583          1            0.000000                0.066667\n", " 1322           3                 NaN                     NaN\n", " 48943          3                 NaN                     NaN\n", " ...          ...                 ...                     ...\n", " 59466          2            0.012500                0.683333\n", " 55793          0           -0.044841                0.466667\n", " 27031          2            0.152381                0.476190\n", " 48832          2            0.431818                0.668182\n", " 1512           2            0.409091                0.505303\n", " 36180          2            0.433333                0.833333\n", " 35022          2            0.450000                0.400000\n", " 23989          2            0.383333                0.672619\n", " 47993          1            0.000000                0.000000\n", " 16234          2            0.500000                0.600000\n", " 13501          1            0.000000                0.000000\n", " 42747          2            0.526623                0.700000\n", " 26434          1            0.000000                0.000000\n", " 3771           2            0.187273                0.454242\n", " 40555          2            0.511111                0.627778\n", " 62919          1            0.000000                0.000000\n", " 26336          0           -0.028750                0.440000\n", " 52517          2            0.137500                0.531481\n", " 5547           2            1.000000                0.300000\n", " 9070           2            0.075000                0.400000\n", " 63281          2            0.520000                0.810000\n", " 50508          2            0.100000                0.700000\n", " 43585          2            0.780000                1.000000\n", " 48488          2            0.185714                0.459524\n", " 2455           1            0.000000                0.333333\n", " 40606          0           -0.242155                0.482292\n", " 21649          2            0.081389                0.641667\n", " 29357          2            0.112245                0.310459\n", " 57508          0           -0.200000                0.500000\n", " 40956          2            0.562500                0.775000\n", " \n", " [22110 rows x 3 columns],\n", " 'test':        Sentiment  Sentiment_Polarity  Sentiment_Subjectivity\n", " 32767          3                 NaN                     NaN\n", " 18754          3                 NaN                     NaN\n", " 26691          1            0.000000                0.000000\n", " 26734          1            0.000000                0.000000\n", " 36895          3                 NaN                     NaN\n", " 9687           2            0.362500                0.537500\n", " 27863          0           -0.095833                0.520833\n", " 6288           2            0.312500                0.612500\n", " 1107           3                 NaN                     NaN\n", " 49456          3                 NaN                     NaN\n", " 11701          0           -0.200000                0.466667\n", " 7202           3                 NaN                     NaN\n", " 57314          3                 NaN                     NaN\n", " 64042          0           -0.043750                0.806250\n", " 8244           3                 NaN                     NaN\n", " 15622          3                 NaN                     NaN\n", " 62913          2            0.300000                0.550000\n", " 14624          2            0.500000                0.500000\n", " 807            3                 NaN                     NaN\n", " 57121          3                 NaN                     NaN\n", " 38100          3                 NaN                     NaN\n", " 48669          2            0.106061                0.429924\n", " 59333          1            0.000000                0.900000\n", " 47236          2            0.700000                0.600000\n", " 1407           0           -0.092308                0.323077\n", " 59865          3                 NaN                     NaN\n", " 5290           0           -0.066667                0.439815\n", " 6679           0           -0.500000                0.200000\n", " 54223          2            0.550000                0.700000\n", " 15162          3                 NaN                     NaN\n", " ...          ...                 ...                     ...\n", " 7881           3                 NaN                     NaN\n", " 23431          0           -0.071429                0.285714\n", " 52031          2            0.076389                0.431944\n", " 58360          2            0.172222                0.588889\n", " 38996          2            0.300000                0.000000\n", " 52277          0           -0.166667                0.300000\n", " 32517          2            0.091964                0.489286\n", " 52478          1            0.000000                0.000000\n", " 25426          1            0.000000                0.000000\n", " 54397          2            0.616667                0.611111\n", " 26485          2            0.600000                1.000000\n", " 29099          2            0.100833                0.233333\n", " 57407          2            0.240365                0.581250\n", " 31077          1            0.000000                0.000000\n", " 54813          2            0.128125                0.492708\n", " 56229          2            0.177778                0.540741\n", " 16327          2            0.377083                0.639583\n", " 19857          2            0.433333                0.833333\n", " 19903          2            0.250000                0.250000\n", " 8365           2            0.650000                0.800000\n", " 63589          2            0.633333                0.580000\n", " 4961           2            0.568182                0.400000\n", " 62237          2            0.196875                0.546875\n", " 19055          0           -0.175000                0.550000\n", " 41801          2            0.500000                0.500000\n", " 8862           2            0.405000                0.585000\n", " 18449          2            0.200000                0.633333\n", " 51567          2            0.800000                0.750000\n", " 17923          0           -0.040000                0.400000\n", " 8986           2            0.540000                0.650000\n", " \n", " [12131 rows x 3 columns],\n", " 'target': 12307    0\n", " 41643    3\n", " 43080    0\n", " 34341    0\n", " 30008    3\n", " 3118     2\n", " 57633    2\n", " 9637     3\n", " 59036    1\n", " 27988    2\n", " 8511     1\n", " 4707     3\n", " 20465    2\n", " 54407    2\n", " 21186    3\n", " 42227    1\n", " 51662    2\n", " 36746    3\n", " 22874    3\n", " 35367    2\n", " 10431    2\n", " 40681    0\n", " 31621    2\n", " 26624    2\n", " 524      2\n", " 49497    3\n", " 13180    2\n", " 57583    1\n", " 1322     3\n", " 48943    3\n", "         ..\n", " 63093    2\n", " 23514    0\n", " 53850    3\n", " 50508    2\n", " 47925    2\n", " 19772    2\n", " 43585    2\n", " 3214     3\n", " 54876    0\n", " 20866    3\n", " 40801    2\n", " 10972    3\n", " 48488    2\n", " 2455     1\n", " 38211    3\n", " 52231    3\n", " 40606    0\n", " 25211    3\n", " 24748    3\n", " 21649    2\n", " 5935     3\n", " 24426    3\n", " 44359    0\n", " 871      3\n", " 29357    2\n", " 57508    0\n", " 27288    3\n", " 4188     2\n", " 59030    3\n", " 40956    2\n", " Name: Sentiment, Length: 43077, dtype: int64,\n", " 'target_test': 32767    3\n", " 18754    3\n", " 26691    1\n", " 26734    1\n", " 36895    3\n", " 9687     2\n", " 27863    0\n", " 6288     2\n", " 1107     3\n", " 49456    3\n", " 11701    0\n", " 7202     3\n", " 57314    3\n", " 64042    0\n", " 8244     3\n", " 15622    3\n", " 62913    2\n", " 14624    2\n", " 807      3\n", " 57121    3\n", " 38100    3\n", " 48669    2\n", " 59333    1\n", " 47236    2\n", " 1407     0\n", " 59865    3\n", " 5290     0\n", " 6679     0\n", " 54223    2\n", " 15162    3\n", "         ..\n", " 8365     2\n", " 25781    3\n", " 63589    2\n", " 4961     2\n", " 41030    3\n", " 25775    3\n", " 62237    2\n", " 5117     3\n", " 19055    0\n", " 3940     3\n", " 42389    3\n", " 43935    3\n", " 41801    2\n", " 27706    3\n", " 8862     2\n", " 290      1\n", " 3915     3\n", " 39642    3\n", " 14261    3\n", " 56681    3\n", " 45873    3\n", " 14268    3\n", " 18449    2\n", " 15261    3\n", " 51567    2\n", " 21527    2\n", " 17923    0\n", " 12613    3\n", " 8986     2\n", " 55325    3\n", " Name: Sentiment, Length: 21218, dtype: int64}"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# >> Examples for Feature selection\n", "# Available strategies=\n", "#           'MR': using a default threshold on the missing ratio per variable,\n", "#            i.e., variables with 20% (by default) and more missing values\n", "#            are removed\n", "#            'LC': detects pairs of linearly correlated variables and remove one\n", "#            'VAR': uses threshold on the variance\n", "#            'Tree': uses decision tree classification as model for feature\n", "#                selection given the target set for classification task\n", "#                'SVC': uses linear SVC as model for feature selection given\n", "#                 the target set for classification task\n", "#            'WR': uses the selectKbest (k=10) and Chi2 for feature selection\n", "#                given the target set for classification task\n", "#            'L1': uses Lasso L1 for feature selection given the target set for\n", "#                regression task\n", "#            'IMP': uses Random Forest regression for feature selection given\n", "#                the target set for regression task\n", "\n", "                \n", "import learn2clean.feature_selection.feature_selector as fs\n", "\n", "#Available strategies for feature selection \n", "#        'MR': using a default threshold on the missing ratio per variable, i.e., variables\n", "#                with 20% (by default) and more missing values are removed\n", "#        'LC': detects pairs of linearly correlated variables and remove one\n", "#        'VAR': uses threshold on the variance\n", "#        'Tree': uses decision tree classification as model for feature selection given the target set for classification task\n", "#        'SVC': uses linear SVC as model for feature selection given the target set for classification task\n", "#        'WR': uses the selectKbest (k=10) and Chi2 for feature selection given the target set for classification task\n", "#        'L1': uses Lasso L1 for feature selection given the target set for regression task\n", "#        'IMP': uses Random Forest regression for feature selection given the target set for regression task\n", "\n", "fs.Feature_selector(dataset = gpsu_encoded.copy(), strategy= 'MR', threshold=0.1, exclude=None, verbose=True).transform()\n", "\n", "fs.Feature_selector(dataset = gpsu_encoded.copy(), strategy= 'LC', threshold=0.2,  exclude=None, verbose=True).transform()\n", "\n", "fs.Feature_selector(dataset = gpsu_encoded.copy(), strategy= 'L1',  exclude= None, threshold=.7,verbose=True).transform()\n", "\n", "fs.Feature_selector(dataset = gpsu_encoded.copy(), strategy= 'IMP', exclude = 'Sentiment',verbose=True, threshold=.4).transform()\n", "\n", "fs.Feature_selector(dataset = gpsu_encoded.copy(), strategy= 'Tree',  exclude='Sentiment',verbose=True).transform()\n", "\n", "fs.Feature_selector(dataset = gpsu_encoded.copy(), strategy= 'WR', exclude= 'Sentiment', verbose=True).transform()\n", "\n", "fs.Feature_selector(dataset = gpsu_encoded.copy(), strategy= 'WR', exclude= 'Sentiment', verbose=True).transform()\n", "\n", "fs.Feature_selector(dataset = gpsu_encoded.copy(), strategy= 'SVC',  exclude='Sentiment').transform()\n", "\n", "fs.Feature_selector(dataset = gpsu_encoded.copy(), strategy= 'VAR',  exclude=None).transform()\n", "\n", "fs.Feature_selector(dataset = gpsu_encoded.copy(), strategy= 'VAR',  exclude='Sentiment').transform()\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## >> Classification "]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", ">>Classification task\n", "{'mean_fit_time': array([0.00579399]), 'std_fit_time': array([0.00082295]), 'mean_score_time': array([0.00062704]), 'std_score_time': array([0.00012987]), 'params': [{}], 'split0_test_score': array([0.93063693]), 'split1_test_score': array([0.93252693]), 'split2_test_score': array([0.9221172]), 'split3_test_score': array([0.92117202]), 'mean_test_score': array([0.92661374]), 'std_test_score': array([0.00502452]), 'rank_test_score': array([1], dtype=int32), 'split0_train_score': array([0.92628064]), 'split1_train_score': array([0.92546153]), 'split2_train_score': array([0.92710433]), 'split3_train_score': array([0.92886845]), 'mean_train_score': array([0.92692874]), 'std_train_score': array([0.00126155])}\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.926613741612324\n", "\n", "Classification done -- CPU time: 0.05927395820617676 seconds\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.9697571118041773\n", "\n", "Classification done -- CPU time: 0.08086180686950684 seconds\n"]}], "source": ["import learn2clean.classification.classifier as cl\n", "#output is accuracy of classification for k=10 cross-validation and execution time \n", "#plus a detailed classification report if verbose = True\n", "\n", "Cl1 = cl.Classifier(dataset = gpsu_encoded.copy(),target = 'Sentiment',strategy = 'LDA', verbose = True).transform()\n", "\n", "Cl2 = cl.Classifier(dataset = gpsu_encoded,target = 'Sentiment',strategy = 'NB',verbose = False).transform()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## >> Regression"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", ">>Regression task\n", "MSE values of cross validation\n", "[[5.27306573e-01 5.41007665e-01 5.31604921e-01 5.34189732e-01\n", "  5.34285549e-01 5.29979682e-01 5.20422726e-01 5.32916849e-01\n", "  5.29568719e-01 5.39058098e-01]\n", " [3.66185120e-01 3.75699767e-01 3.69170084e-01 3.70965092e-01\n", "  3.71031632e-01 3.68041446e-01 3.61404671e-01 3.70081145e-01\n", "  3.67756055e-01 3.74345901e-01]\n", " [2.34358477e-01 2.40447851e-01 2.36268854e-01 2.37417659e-01\n", "  2.37460244e-01 2.35546525e-01 2.31298989e-01 2.36851933e-01\n", "  2.35363875e-01 2.39581377e-01]\n", " [9.15462800e-02 9.39249418e-02 9.22925210e-02 9.27412729e-02\n", "  9.27579079e-02 9.20103614e-02 9.03511677e-02 9.25202862e-02\n", "  9.19390137e-02 9.35864753e-02]\n", " [2.28865700e-02 2.34812355e-02 2.30731303e-02 2.31853182e-02\n", "  2.31894770e-02 2.30025904e-02 2.25877919e-02 2.31300715e-02\n", "  2.29847534e-02 2.33966188e-02]\n", " [3.66185120e-03 3.75699767e-03 3.69170084e-03 3.70965092e-03\n", "  3.71031632e-03 3.68041446e-03 3.61404671e-03 3.70081145e-03\n", "  3.67756055e-03 3.74345901e-03]\n", " [9.15462800e-04 9.39249418e-04 9.22925210e-04 9.27412729e-04\n", "  9.27579079e-04 9.20103614e-04 9.03511677e-04 9.25202862e-04\n", "  9.19390137e-04 9.35864753e-04]\n", " [2.28865700e-04 2.34812355e-04 2.30731303e-04 2.31853182e-04\n", "  2.31894770e-04 2.30025904e-04 2.25877919e-04 2.31300715e-04\n", "  2.29847534e-04 2.33966188e-04]\n", " [1.46474048e-04 1.50279907e-04 1.47668034e-04 1.48386037e-04\n", "  1.48412653e-04 1.47216578e-04 1.44561868e-04 1.48032458e-04\n", "  1.47102422e-04 1.49738361e-04]\n", " [3.66185120e-05 3.75699767e-05 3.69170084e-05 3.70965092e-05\n", "  3.71031632e-05 3.68041446e-05 3.61404671e-05 3.70081145e-05\n", "  3.67756055e-05 3.74345901e-05]\n", " [3.66185120e-07 3.75699767e-07 3.69170084e-07 3.70965092e-07\n", "  3.71031632e-07 3.68041446e-07 3.61404671e-07 3.70081145e-07\n", "  3.67756055e-07 3.74345901e-07]]\n", "alphas vs. MSE in cross-validation\n", "    alpha           MSE\n", "0   1.200  5.320341e-01\n", "1   1.000  3.694681e-01\n", "2   0.800  2.364596e-01\n", "3   0.500  9.236702e-02\n", "4   0.250  2.309176e-02\n", "5   0.100  3.694681e-03\n", "6   0.050  9.236702e-04\n", "7   0.025  2.309176e-04\n", "8   0.020  1.477872e-04\n", "9   0.010  3.694681e-05\n", "10  0.001  3.694681e-07\n", "Best alpha =  0.001\n", "MSE of LASSO with 10  folds for cross-validation: 3.676883428199664e-07\n", "Regression done -- CPU time: 0.07724285125732422 seconds\n", "\n", ">>Regression task\n", "                            OLS Regression Results                            \n", "==============================================================================\n", "Dep. Variable:              Sentiment   R-squared:                       1.000\n", "Model:                            OLS   Adj. R-squared:                  1.000\n", "Method:                 Least Squares   F-statistic:                 1.484e+32\n", "Date:                Fri, 29 Mar 2019   Prob (F-statistic):               0.00\n", "Time:                        13:14:20   Log-Likelihood:             7.8565e+05\n", "No. Observations:               25106   AIC:                        -1.571e+06\n", "Df Residuals:                   25102   BIC:                        -1.571e+06\n", "Df Model:                           3                                         \n", "Covariance Type:            nonrobust                                         \n", "==========================================================================================\n", "                             coef    std err          t      P>|t|      [0.025      0.975]\n", "------------------------------------------------------------------------------------------\n", "const                  -8.815e-15   1.17e-16    -75.545      0.000   -9.04e-15   -8.59e-15\n", "Sentiment                  1.0000   7.19e-17   1.39e+16      0.000       1.000       1.000\n", "Sentiment_Polarity      3.621e-17   1.73e-16      0.209      0.834   -3.03e-16    3.76e-16\n", "Sentiment_Subjectivity -4.624e-16   1.57e-16     -2.947      0.003    -7.7e-16   -1.55e-16\n", "==============================================================================\n", "Omnibus:                     4456.111   <PERSON><PERSON><PERSON>-<PERSON>:                   0.162\n", "Prob(Omnibus):                  0.000   Jarque-Bera (JB):             4596.485\n", "Skew:                           0.982   Prob(JB):                         0.00\n", "Kurtosis:                       2.265   Cond. No.                         10.4\n", "==============================================================================\n", "\n", "Warnings:\n", "[1] Standard Errors assume that the covariance matrix of the errors is correctly specified.\n", "MSE of OLS with 10  folds for cross-validation: 3.87743270893407e-29\n", "Regression done -- CPU time: 0.04571890830993652 seconds\n", "\n", ">>Regression task\n", "Earth Model\n", "--------------------------------------\n", "Basis Function  Pruned  Coefficient   \n", "--------------------------------------\n", "(Intercept)     No      -4.29269e-15  \n", "h(Sentiment-0)  No      1             \n", "h(0-Sentiment)  Yes     None          \n", "--------------------------------------\n", "MSE: 0.0000, GCV: 0.0000, RSQ: 1.0000, GRSQ: 1.0000\n", "MSE of MARS with 10 folds for cross-validation: 0.024210667788893026\n", "Regression done -- CPU time: 2.5640130043029785 seconds\n"]}], "source": ["import learn2clean.regression.regressor as rg\n", "# output is MSE and computation time, with regression summary if verbose = True\n", " \n", "    \n", "rg1 = rg.Regressor(dataset = gpsu_encoded,target = 'Sentiment',strategy= 'LASSO', verbose = True).transform()\n", "\n", "rg3 = rg.Regressor(dataset = gpsu_encoded,target = 'Sentiment',strategy= 'OLS',verbose = True).transform()\n", "\n", "rg2 = rg.Regressor(dataset = gpsu_encoded,target = 'Sentiment',strategy= 'MARS',verbose = True).transform()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## >> Clustering"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", ">>Clustering task\n", "Note: The clustering is applied on the training dataset only.\n", "Best silhouette = 0.6267  for k= 2\n", "Quality of clustering 0.6267\n", "Labels distribution:\n", "1    8337\n", "0    8322\n", "Name: cluster_ID, dtype: int64\n", "Clustering done -- CPU time: 63.12605404853821 seconds\n"]}, {"data": {"text/plain": ["{'quality_metric': 0.6267,\n", " 'result': {'train':        Sentiment_Polarity  Sentiment_Subjectivity  New_ID  cluster_ID\n", "  0                0.675919            6.791192e-01       1           1\n", "  1                0.388889            1.000000e-07       2           1\n", "  3                0.933333            7.896552e-01       3           1\n", "  6                0.081633            5.967453e-01       4           1\n", "  9                0.388889            1.000000e-07       5           1\n", "  10               0.888889            9.259259e-01       6           1\n", "  11               0.388889            1.000000e-07       7           1\n", "  12               0.388889            1.000000e-07       8           1\n", "  16               0.758303            8.227331e-01       9           1\n", "  18               0.698387            6.396516e-01      10           1\n", "  19               0.388889            1.000000e-07      11           1\n", "  20               0.778635            6.491863e-01      12           1\n", "  21               0.388889            1.000000e-07      13           1\n", "  23               0.115270            6.168576e-01      14           1\n", "  25               0.911111            8.888889e-01      15           1\n", "  26               0.746534            6.951296e-01      16           1\n", "  27               0.793073            8.337591e-01      17           1\n", "  28               0.706813            7.538343e-01      18           1\n", "  30               0.388889            1.000000e-07      19           1\n", "  34               0.388889            1.000000e-07      20           1\n", "  35               0.768601            9.074074e-01      21           1\n", "  38               0.388889            1.000000e-07      22           1\n", "  40               0.388889            1.000000e-07      23           1\n", "  41               0.834254            7.211465e-01      24           1\n", "  42               0.388889            1.000000e-07      25           1\n", "  44               0.388889            1.000000e-07      26           1\n", "  45               0.073858            8.981481e-01      27           1\n", "  46               0.825887            8.950617e-01      28           1\n", "  47               0.916667            6.791192e-01      29           1\n", "  48               0.944444            9.999999e-01      30           1\n", "  ...                   ...                     ...     ...         ...\n", "  43016            0.071914            6.491863e-01   16630           0\n", "  43024            0.388889            1.000000e-07   16631           0\n", "  43025            0.388889            1.000000e-07   16632           0\n", "  43026            0.388889            1.000000e-07   16633           0\n", "  43027            0.388889            1.000000e-07   16634           0\n", "  43028            0.699947            7.289293e-01   16635           0\n", "  43030            0.388889            1.000000e-07   16636           0\n", "  43031            0.388889            1.000000e-07   16637           0\n", "  43034            0.717111            9.012346e-01   16638           0\n", "  43037            0.082928            5.880052e-01   16639           0\n", "  43044            0.388889            1.000000e-07   16640           0\n", "  43045            0.388889            1.000000e-07   16641           0\n", "  43046            0.833762            8.558110e-01   16642           0\n", "  43047            0.818012            8.888889e-01   16643           0\n", "  43048            0.933333            7.896552e-01   16644           0\n", "  43053            0.388889            1.000000e-07   16645           0\n", "  43056            0.388889            1.000000e-07   16646           0\n", "  43057            0.911111            9.999999e-01   16647           0\n", "  43058            0.711962            6.977980e-01   16648           0\n", "  43059            0.874123            5.639697e-01   16649           0\n", "  43060            0.388889            1.000000e-07   16650           0\n", "  43061            0.778635            7.164768e-01   16651           0\n", "  43063            0.388889            1.000000e-07   16652           0\n", "  43065            0.095377            6.751166e-01   16653           0\n", "  43066            0.388889            1.000000e-07   16654           0\n", "  43068            0.388889            1.000000e-07   16655           0\n", "  43070            0.023324            9.259259e-01   16656           0\n", "  43072            0.388889            1.000000e-07   16657           0\n", "  43073            0.023324            9.835391e-01   16658           0\n", "  43075            0.933333            7.896552e-01   16659           0\n", "  \n", "  [16659 rows x 4 columns],\n", "  'test':       Sentiment  Sentiment_Polarity  Sentiment_Subjectivity  \\\n", "  4             2            0.743952            5.769829e-01   \n", "  5             3            0.388889            1.000000e-07   \n", "  8             3            0.388889            1.000000e-07   \n", "  13            0            0.103682            5.739218e-01   \n", "  14            3            0.388889            1.000000e-07   \n", "  17            3            0.388889            1.000000e-07   \n", "  24            0            0.149306            5.236111e-01   \n", "  31            2            0.853535            8.888889e-01   \n", "  32            0            0.096899            5.000000e-01   \n", "  33            3            0.388889            1.000000e-07   \n", "  36            3            0.388889            1.000000e-07   \n", "  37            3            0.388889            1.000000e-07   \n", "  39            3            0.388889            1.000000e-07   \n", "  43            3            0.388889            1.000000e-07   \n", "  49            1            0.388889            1.000000e-07   \n", "  54            1            0.388889            1.000000e-07   \n", "  55            3            0.388889            1.000000e-07   \n", "  60            2            0.577778            8.059530e-01   \n", "  61            1            0.388889            6.831795e-01   \n", "  62            0            0.138889            5.203704e-01   \n", "  63            2            0.774194            5.444444e-01   \n", "  64            2            0.752688            6.412648e-01   \n", "  66            3            0.388889            1.000000e-07   \n", "  70            1            0.388889            1.000000e-07   \n", "  79            3            0.388889            1.000000e-07   \n", "  81            2            0.846275            6.045323e-01   \n", "  82            3            0.388889            1.000000e-07   \n", "  87            2            0.888889            8.142466e-01   \n", "  93            3            0.388889            1.000000e-07   \n", "  94            1            0.388889            8.888889e-01   \n", "  ...         ...                 ...                     ...   \n", "  20565         3            0.388889            1.000000e-07   \n", "  20567         2            0.998264            9.506173e-01   \n", "  20573         2            0.693967            5.997618e-01   \n", "  20577         3            0.388889            1.000000e-07   \n", "  20582         3            0.388889            1.000000e-07   \n", "  20583         2            0.792298            5.922881e-01   \n", "  20585         3            0.388889            1.000000e-07   \n", "  20588         2            0.924444            8.888889e-01   \n", "  20590         3            0.388889            1.000000e-07   \n", "  20592         2            0.803030            7.188676e-01   \n", "  20593         3            0.388889            1.000000e-07   \n", "  20596         3            0.388889            1.000000e-07   \n", "  20599         0            0.097384            8.888889e-01   \n", "  20601         3            0.388889            1.000000e-07   \n", "  20605         3            0.388889            1.000000e-07   \n", "  20606         3            0.388889            1.000000e-07   \n", "  20607         2            0.803451            7.505904e-01   \n", "  20608         2            0.799062            8.462361e-01   \n", "  20610         3            0.388889            1.000000e-07   \n", "  20625         3            0.388889            1.000000e-07   \n", "  20630         1            0.388889            1.000000e-07   \n", "  20634         0            0.104651            5.111111e-01   \n", "  20850         2            0.740570            6.877113e-01   \n", "  20893         2            0.720430            9.074074e-01   \n", "  20937         3            0.388889            1.000000e-07   \n", "  21039         3            0.388889            1.000000e-07   \n", "  21086         3            0.388889            1.000000e-07   \n", "  21108         3            0.388889            1.000000e-07   \n", "  21157         1            0.388889            1.000000e-07   \n", "  21198         2            0.944444            9.999999e-01   \n", "  \n", "                                                        App  \\\n", "  4                                   10 Best Foods for You   \n", "  5                                   10 Best Foods for You   \n", "  8                                   10 Best Foods for You   \n", "  13                                  10 Best Foods for You   \n", "  14                                  10 Best Foods for You   \n", "  17                                  10 Best Foods for You   \n", "  24                                  10 Best Foods for You   \n", "  31                                  10 Best Foods for You   \n", "  32                                  10 Best Foods for You   \n", "  33                                  10 Best Foods for You   \n", "  36                                  10 Best Foods for You   \n", "  37                                  10 Best Foods for You   \n", "  39                                  10 Best Foods for You   \n", "  43                                  10 Best Foods for You   \n", "  49                                  10 Best Foods for You   \n", "  54                                  10 Best Foods for You   \n", "  55                                  10 Best Foods for You   \n", "  60                                  10 Best Foods for You   \n", "  61                                  10 Best Foods for You   \n", "  62                                  10 Best Foods for You   \n", "  63                                  10 Best Foods for You   \n", "  64                                  10 Best Foods for You   \n", "  66                                  10 Best Foods for You   \n", "  70                                  10 Best Foods for You   \n", "  79                                  10 Best Foods for You   \n", "  81                                  10 Best Foods for You   \n", "  82                                  10 Best Foods for You   \n", "  87                                  10 Best Foods for You   \n", "  93                                  10 Best Foods for You   \n", "  94                                  10 Best Foods for You   \n", "  ...                                                   ...   \n", "  20565                                             CW Seed   \n", "  20567                                           CWT To Go   \n", "  20573                                           CWT To Go   \n", "  20577                                           CWT To Go   \n", "  20582                                           CWT To Go   \n", "  20583                                           CWT To Go   \n", "  20585                                           CWT To Go   \n", "  20588                                           CWT To Go   \n", "  20590                                           CWT To Go   \n", "  20592                                           CWT To Go   \n", "  20593                                           CWT To Go   \n", "  20596                                           CWT To Go   \n", "  20599                                           CWT To Go   \n", "  20601                                           CWT To Go   \n", "  20605                                           CWT To Go   \n", "  20606                                           CWT To Go   \n", "  20607  Cache Cleaner-DU Speed Booster (booster & cleaner)   \n", "  20608  Cache Cleaner-DU Speed Booster (booster & cleaner)   \n", "  20610  Cache Cleaner-DU Speed Booster (booster & cleaner)   \n", "  20625  Cache Cleaner-DU Speed Booster (booster & cleaner)   \n", "  20630  Cache Cleaner-DU Speed Booster (booster & cleaner)   \n", "  20634  Cache Cleaner-DU Speed Booster (booster & cleaner)   \n", "  20850                         Calculator - unit converter   \n", "  20893                                Calculator Plus Free   \n", "  20937                      Calculator with Percent (Free)   \n", "  21039                                        Call Blocker   \n", "  21086                                        Call Blocker   \n", "  21108                                        <PERSON> Blocker   \n", "  21157                         Call Control - <PERSON> Blocker   \n", "  21198                      Call of Duty:Black Ops Zombies   \n", "  \n", "                                                                                           Translated_Review  \\\n", "  4                                                                                             Best idea us   \n", "  5                                                                                                 Best way   \n", "  8                                                                                     Looking forward app,   \n", "  13                  Greatest ever Completely awesome maintain health.... This must ppl there... Love it!!!   \n", "  14                                                     Good health...... Good health first priority.......   \n", "  17                  Mrs <PERSON><PERSON> bhati I thankful developers,to make kind app, really good healthy food body   \n", "  24                                                                                   An excellent A useful   \n", "  31                                   Thanks advice. Downloaded Adobe reader still can't get recipe book?..   \n", "  32                                                                      No recipe book Unable recipe book.   \n", "  33                                                                          Absolutely Fabulous Phenomenal   \n", "  36                                                                                                    Best   \n", "  37     WEALTH OF INFORMATION Very informative ... easy understand. (The ads middle page interupted read...   \n", "  39                                                                                         Great app. Love   \n", "  43                                               Waste time It needs internet time n ask calls information   \n", "  49                                                                              Lovely Best everyone needs   \n", "  54                                                                                     Thanks Thank u much   \n", "  55                                                                                   Love This really good   \n", "  60                                                                                        Very informative   \n", "  61                                                                                                     Luv   \n", "  62                                                                                                   great   \n", "  63                                                                                               Wonderful   \n", "  64                                                                                    Doesn't work... Zero   \n", "  66                                                                  10 best foods 4u Excellent chose foods   \n", "  70                                                                                             I Love Good   \n", "  79                                                                                                    Good   \n", "  81                                                                                             Great Great   \n", "  82     Food list easy I predibetic, I scared. All Dr. said potatoes, rice, bread. He never told I could...   \n", "  87                                                                                      I like Was helpful   \n", "  93                                       Not good Its good u net connection u can't play app.so much good.   \n", "  94                                       Awesome resources I begin new journey. I can't see great results!   \n", "  ...                                                                                                    ...   \n", "  20565                                                                                                 good   \n", "  20567  It's good offline archive travel itineraries. Nowadays gate changes delay notifications pretty w...   \n", "  20573                                            Use time. Have also installed wife's phone knows I am!!;)   \n", "  20577                                                                          Really helpful well thought   \n", "  20582                                                                                           Super easy   \n", "  20583                                                                                            This Good   \n", "  20585                           This terrible. It constantly logs out. Search abilities extremely limited.   \n", "  20588  The usually OK apart login updates. The latest update let login, sends email saying I tried crea...   \n", "  20590                             It's ok app. Travel can't travel booking technologically advanced world!   \n", "  20592  Significant battery drain...I know uses much power ranks #3 stock Android battery manager....Had...   \n", "  20593  The best for frequent travelers. Simple fast efficient. One year after my first feedback it is s...   \n", "  20596        Current version seems missing itinerary details (booked CWT) flight check view screen frozen.   \n", "  20599             - Great travel Businesses, IN every trip integration preferred travel partners fantastic   \n", "  20601                                                 Very easy . When I call . Very professional friendly   \n", "  20605                                         Great app, easy too. It help organise business trip details.   \n", "  20606                                                          Very good design pretty useful information.   \n", "  20607  Floating window working properly 7.1 Samsung c9 pro. Sometimes appears disappeared. New scanning...   \n", "  20608  Great app. Just want stop innerupting everything I even I'm using example I'll watching tube pop...   \n", "  20610                                      Needs stop notifying much I need clean ect.Very,very annoying!!   \n", "  20625                                                                                  <PERSON><PERSON> making   \n", "  20630                                                                                              It good   \n", "  20634                                                                      It clean cache all. Please look   \n", "  20850      Its great calculator India India different number system , usually comma two digit , help that?   \n", "  20893                                         I long time ago. Thought I'd try again. The dark theme nice.   \n", "  20937                                         Only change I would make actually multiplication sign, star.   \n", "  21039  So far able block annoying machines, recordings telemarketers I blocked. Finally I get 300 dummy...   \n", "  21086  Finally call blocker works. Also option block phone numbers starting certain number range (witho...   \n", "  21108  New pop ad free version pops several times time open app. It repeats: SEE WHO IS CALLING Dont fe...   \n", "  21157                                         Since recent update requires set default dialer block calls.   \n", "  21198  This GAME DOESN'T EVEN LAUNCH!!! , every time I go play it, goes black home screen says \"restart...   \n", "  \n", "         New_ID  \\\n", "  4           1   \n", "  5           2   \n", "  8           3   \n", "  13          4   \n", "  14          5   \n", "  17          6   \n", "  24          7   \n", "  31          8   \n", "  32          9   \n", "  33         10   \n", "  36         11   \n", "  37         12   \n", "  39         13   \n", "  43         14   \n", "  49         15   \n", "  54         16   \n", "  55         17   \n", "  60         18   \n", "  61         19   \n", "  62         20   \n", "  63         21   \n", "  64         22   \n", "  66         23   \n", "  70         24   \n", "  79         25   \n", "  81         26   \n", "  82         27   \n", "  87         28   \n", "  93         29   \n", "  94         30   \n", "  ...       ...   \n", "  20565    4277   \n", "  20567    4278   \n", "  20573    4279   \n", "  20577    4280   \n", "  20582    4281   \n", "  20583    4282   \n", "  20585    4283   \n", "  20588    4284   \n", "  20590    4285   \n", "  20592    4286   \n", "  20593    4287   \n", "  20596    4288   \n", "  20599    4289   \n", "  20601    4290   \n", "  20605    4291   \n", "  20606    4292   \n", "  20607    4293   \n", "  20608    4294   \n", "  20610    4295   \n", "  20625    4296   \n", "  20630    4297   \n", "  20634    4298   \n", "  20850    4299   \n", "  20893    4300   \n", "  20937    4301   \n", "  21039    4302   \n", "  21086    4303   \n", "  21108    4304   \n", "  21157    4305   \n", "  21198    4306   \n", "  \n", "                                                                                                         row  \n", "  4                                   2*0.7439516129032258*0.5769828775775953*10BestFoodsforYou*Bestideaus*1  \n", "  5                                   3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*Bestway*2  \n", "  8                        3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*Lookingforwardapp,*3  \n", "  13     0*0.10368217054263565*0.5739218315744468*10BestFoodsforYou*GreatesteverCompletelyawesomemaintain...  \n", "  14     3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*Goodhealth......Goodhealthfirstprio...  \n", "  17     3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*<PERSON><PERSON><PERSON><PERSON><PERSON>devel<PERSON>s,t...  \n", "  24                         0*0.14930555555555558*0.5236111111111111*10BestFoodsforYou*AnexcellentAuseful*7  \n", "  31     2*0.8535353535353535*0.8888888888888888*10BestFoodsforYou*Thanksadvice.DownloadedAdobereaderstil...  \n", "  32                             0*0.09689922480620156*0.5*10BestFoodsforYou*NorecipebookUnablerecipebook.*9  \n", "  33            3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*AbsolutelyFabulousPhenomenal*10  \n", "  36                                    3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*Best*11  \n", "  37     3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*WEALTHOFINFORMATIONVeryinformative....  \n", "  39                           3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*Greatapp.Love*13  \n", "  43     3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*WastetimeItneedsinternettimenaskcal...  \n", "  49                 1*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*LovelyBesteveryoneneeds*15  \n", "  54                        1*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*ThanksThankumuch*16  \n", "  55                      3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*LoveThisreallygood*17  \n", "  60                            2*0.5777777777777778*0.8059529568963533*10BestFoodsforYou*Veryinformative*18  \n", "  61                                        1*0.3888888888888889*0.6831794991626757*10BestFoodsforYou*Luv*19  \n", "  62                                      0*0.1388888888888889*0.5203703703703704*10BestFoodsforYou*great*20  \n", "  63                                  2*0.7741935483870966*0.5444444444444445*10BestFoodsforYou*Wonderful*21  \n", "  64                         2*0.7526881720430106*0.6412648436437143*10BestFoodsforYou*Doesn'twork...Zero*22  \n", "  66        3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*10bestfoods4uExcellentchosefoods*23  \n", "  70                               1*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*ILoveGood*24  \n", "  79                                    3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*Good*25  \n", "  81                                  2*0.8462752525252525*0.604532291605932*10BestFoodsforYou*GreatGreat*26  \n", "  82     3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*FoodlisteasyIpredibetic,Iscared.All...  \n", "  87                            2*0.8888888888888888*0.8142465500956068*10BestFoodsforYou*IlikeWashelpful*28  \n", "  93     3*0.3888888888888889*9.999999977795539e-08*10BestFoodsforYou*NotgoodItsgoodunetconnectionucan'tp...  \n", "  94     1*0.3888888888888889*0.8888888888888888*10BestFoodsforYou*AwesomeresourcesIbeginnewjourney.Ican'...  \n", "  ...                                                                                                    ...  \n", "  20565                                          3*0.3888888888888889*9.999999977795539e-08*CWSeed*good*4277  \n", "  20567  2*0.9982638888888888*0.9506172839506173*CWTToGo*It'sgoodofflinearchivetravelitineraries.Nowadays...  \n", "  20573  2*0.693967322999581*0.599761830302324*CWTToGo*Usetime.<PERSON><PERSON>oinstalledwife'sphoneknowsIam!!;)*4279  \n", "  20577                     3*0.3888888888888889*9.999999977795539e-08*CWTToGo*Reallyhelpfulwellthought*4280  \n", "  20582                                    3*0.3888888888888889*9.999999977795539e-08*CWTToGo*Supereasy*4281  \n", "  20583                                        2*0.7922979797979797*0.5922881075933378*CWTToGo*ThisGood*4282  \n", "  20585  3*0.3888888888888889*9.999999977795539e-08*CWTToGo*Thisterrible.Itconstantlylogsout.Searchabilit...  \n", "  20588  2*0.9244444444444444*0.8888888888888888*CWTToGo*TheusuallyOKapartloginupdates.Thelatestupdatelet...  \n", "  20590  3*0.3888888888888889*9.999999977795539e-08*CWTToGo*It'sokapp.Travelcan'ttravelbookingtechnologic...  \n", "  20592  2*0.803030303030303*0.718867610560566*CWTToGo*Significantbatterydrain...Iknowusesmuchpowerranks#...  \n", "  20593  3*0.3888888888888889*9.999999977795539e-08*CWTToGo*Thebestforfrequenttravelers.Simplefastefficie...  \n", "  20596  3*0.3888888888888889*9.999999977795539e-08*CWTToGo*Currentversionseemsmissingitinerarydetails(bo...  \n", "  20599  0*0.09738372093023256*0.8888888888888888*CWTToGo*-GreattravelBusinesses,INeverytripintegrationpr...  \n", "  20601  3*0.3888888888888889*9.999999977795539e-08*CWTToGo*Veryeasy.WhenIcall.Veryprofessionalfriendly*4290  \n", "  20605  3*0.3888888888888889*9.999999977795539e-08*CWTToGo*Greatapp,easytoo.Ithelporganisebusinesstripde...  \n", "  20606       3*0.3888888888888889*9.999999977795539e-08*CWTToGo*Verygooddesignprettyusefulinformation.*4292  \n", "  20607  2*0.8034511784511784*0.7505903762475795*CacheCleaner-DUSpeedBooster(booster&cleaner)*Floatingwin...  \n", "  20608  2*0.799062049062049*0.8462361238641563*CacheCleaner-DUSpeedBooster(booster&cleaner)*Greatapp.Jus...  \n", "  20610  3*0.3888888888888889*9.999999977795539e-08*CacheCleaner-DUSpeedBooster(booster&cleaner)*Needssto...  \n", "  20625  3*0.3888888888888889*9.999999977795539e-08*CacheCleaner-DUSpeedBooster(booster&cleaner)*Eythankf...  \n", "  20630  1*0.3888888888888889*9.999999977795539e-08*CacheCleaner-DUSpeedBooster(booster&cleaner)*Itgood*4297  \n", "  20634  0*0.10465116279069767*0.5111111111111111*CacheCleaner-DUSpeedBooster(booster&cleaner)*Itcleancac...  \n", "  20850  2*0.7405700631507082*0.6877113228322491*Calculator-unitconverter*ItsgreatcalculatorIndiaIndiadif...  \n", "  20893  2*0.7204301075268816*0.9074074074074074*CalculatorPlusFree*Ilongtimeago.ThoughtI'dtryagain.Theda...  \n", "  20937  3*0.3888888888888889*9.999999977795539e-08*CalculatorwithPercent(Free)*OnlychangeIwouldmakeactua...  \n", "  21039  3*0.3888888888888889*9.999999977795539e-08*CallBlocker*Sofarableblockannoyingmachines,recordings...  \n", "  21086  3*0.3888888888888889*9.999999977795539e-08*CallBlocker*Finallycallblockerworks.Alsooptionblockph...  \n", "  21108  3*0.3888888888888889*9.999999977795539e-08*CallBlocker*Newpopadfreeversionpopsseveraltimestimeop...  \n", "  21157  1*0.3888888888888889*9.999999977795539e-08*CallControl-CallBlocker*Sincerecentupdaterequiressetd...  \n", "  21198  2*0.9444444444444444*0.9999999000000003*CallofDuty:BlackOpsZombies*ThisGAMEDOESN'TEVENLAUNCH!!!,...  \n", "  \n", "  [4306 rows x 7 columns],\n", "  'target': 34343    2\n", "  10243    1\n", "  30341    3\n", "  4717     2\n", "  12560    2\n", "  44471    3\n", "  58596    0\n", "  29621    0\n", "  5505     3\n", "  17941    1\n", "  10823    2\n", "  49525    3\n", "  15620    3\n", "  36572    3\n", "  30104    3\n", "  18118    2\n", "  25233    2\n", "  63488    2\n", "  35067    2\n", "  13726    3\n", "  48658    2\n", "  26438    1\n", "  14161    3\n", "  46399    0\n", "  8806     3\n", "  41839    2\n", "  1691     2\n", "  50042    2\n", "  46077    2\n", "  58592    0\n", "          ..\n", "  57534    2\n", "  31680    2\n", "  4480     2\n", "  57760    2\n", "  53987    3\n", "  53376    3\n", "  47061    3\n", "  61939    3\n", "  51823    1\n", "  54958    3\n", "  60605    2\n", "  28306    2\n", "  49832    2\n", "  45209    3\n", "  31732    2\n", "  35026    1\n", "  62824    3\n", "  48961    3\n", "  25030    0\n", "  1994     3\n", "  7704     0\n", "  53346    3\n", "  20299    0\n", "  19032    0\n", "  63685    3\n", "  27495    3\n", "  7949     0\n", "  42442    2\n", "  9927     2\n", "  61512    2\n", "  Name: Sentiment, Length: 43077, dtype: int64,\n", "  'target_test': 36370    3\n", "  60949    2\n", "  38637    3\n", "  6180     3\n", "  1755     2\n", "  32426    3\n", "  38935    2\n", "  52427    2\n", "  36560    3\n", "  61949    3\n", "  27469    3\n", "  37650    2\n", "  28434    0\n", "  36134    0\n", "  41600    3\n", "  18643    1\n", "  196      2\n", "  7308     3\n", "  6749     3\n", "  37514    0\n", "  61643    2\n", "  58168    3\n", "  9368     2\n", "  57276    3\n", "  8164     0\n", "  16280    1\n", "  54624    2\n", "  2524     1\n", "  24010    2\n", "  31282    3\n", "          ..\n", "  59484    1\n", "  19900    1\n", "  1835     1\n", "  49538    3\n", "  39821    3\n", "  61685    1\n", "  13637    3\n", "  22322    3\n", "  49642    2\n", "  22518    3\n", "  52959    2\n", "  24108    3\n", "  61866    3\n", "  54694    2\n", "  62587    3\n", "  28116    3\n", "  21000    3\n", "  29640    0\n", "  27644    3\n", "  1975     3\n", "  10535    2\n", "  46249    3\n", "  13632    3\n", "  11008    0\n", "  13622    3\n", "  6242     2\n", "  52053    2\n", "  4675     3\n", "  46919    2\n", "  40371    2\n", "  Name: Sentiment, Length: 21218, dtype: int64}}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import learn2clean.clustering.clusterer as ct\n", "# clustering is applied to one dataset (i.e., the training set if two datasets are given in the path)\n", "# output is silhouette, best k, and computation time, plus the training dataset with cluster IDs\n", "\n", "ct.Clusterer(dataset = gpsu_encoded,strategy= 'KMEANS', verbose=True).transform()\n", "#ct.Clusterer(dataset = gpsu_encoded,strategy='HCA', verbose = True).transform()\n", "#ct.Clusterer(dataset = gpsu_encoded,strategy='HCA', metric= 'euclidean', verbose = True).transform()\n", "#ct.Clusterer(dataset = gpsu_encoded,strategy='HCA', metric= 'cosine', verbose = True).transform()\n", "#ct.Clusterer(dataset = gpsu_encoded,strategy='HCA', metric= 'cityblock', verbose = True).transform()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## - Create your own pipeline"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Reading csv : googleplaystore_reviews.csv ...\n", "Reading data ...\n", "CPU time: 0.27039003372192383 seconds\n", "Profiling datasets\n", "                Attribute     Type  Num. Missing Values  Num. Unique Values             Sknewness  Kurtosis\n", "0      Sentiment_Polarity  float64              26863.0              6196.0  -0.10457655084633158  0.646756\n", "1  Sentiment_Subjectivity  float64              26863.0              4531.0   -0.3063336025424886 -0.282853\n", "2                     App   object                  0.0              1074.0                   N/A       N/A\n", "3       Translated_Review   object              26868.0             27995.0                   N/A       N/A\n", "4               Sentiment   object              26863.0                 4.0                   N/A       N/A\n", "\n", "> Number of categorical features in the training set: 3\n", "> Number of numerical features in the training set: 2\n", "> Number of data samples : 64295\n", "\n", "> Top sparse features (% missing values on dataset set):\n", "Translated_Review         41.7\n", "Sentiment_Subjectivity    41.7\n", "Sentiment_Polarity        41.7\n", "Sentiment                 41.7\n", "dtype: float64\n", "\n", "> Task : classification\n", "Positive    16070\n", "Negative     5631\n", "Neutral      3401\n", "Name: Sentiment, dtype: int64\n", "\n", "Encoding target...\n", "Encoding target done...\n", ">>Imputation \n", "* For train dataset\n", "Before imputation:\n", "Total 53928 missing values in ['Translated_Review', 'Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "- 35950 numerical missing values in ['Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "- 17978 non-numerical missing values in ['Translated_Review']\n", "After imputation:\n", "Total 40512 missing values\n", "- 0 numerical missing values\n", "- 40512 non-numerical missing values\n", "* For test dataset\n", "Before imputation:\n", "Total 26666 missing values in ['Translated_Review', 'Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "- 17776 numerical missing values in ['Sentiment_Polarity', 'Sentiment_Subjectivity']\n", "- 8890 non-numerical missing values in ['Translated_Review']\n", "After imputation:\n", "Total 31086 missing values\n", "- 0 numerical missing values\n", "- 31086 non-numerical missing values\n", "Imputation done -- CPU time: 0.1578660011291504 seconds\n", "\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.06437301635742188 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "20 outlying rows have been removed\n", "* For test dataset\n", "LOF requires no missing values, so missing values have been removed using DROP.\n", "20 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.5824182033538818 seconds\n", "\n", "\n", ">>Classification task\n", "{'mean_fit_time': array([0.00416225]), 'std_fit_time': array([0.00034319]), 'mean_score_time': array([0.00050819]), 'std_score_time': array([6.67215363e-05]), 'params': [{}], 'split0_test_score': array([0.6393011]), 'split1_test_score': array([0.6393011]), 'split2_test_score': array([0.63960728]), 'split3_test_score': array([0.63960728]), 'mean_test_score': array([0.63945415]), 'std_test_score': array([0.00015309]), 'rank_test_score': array([1], dtype=int32), 'split0_train_score': array([0.63950519]), 'split1_train_score': array([0.63950519]), 'split2_train_score': array([0.63940313]), 'split3_train_score': array([0.63940313]), 'mean_train_score': array([0.63945416]), 'std_train_score': array([5.10297788e-05])}\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.6394541536988269\n", "\n", "Classification done -- CPU time: 0.049063920974731445 seconds\n"]}, {"data": {"text/plain": ["{'quality_metric': 0.6394541536988269}"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# create your preprocessing pipeline for classification\n", "\n", "import learn2clean.loading.reader as rd \n", "import learn2clean.normalization.normalizer as nl \n", "import learn2clean.feature_selection.feature_selector as fs\n", "import learn2clean.duplicate_detection.duplicate_detector as dd\n", "import learn2clean.outlier_detection.outlier_detector as od\n", "import learn2clean.imputation.imputer as imp\n", "import learn2clean.classification.classifier as cl\n", "\n", "d_enc = rd.Reader(sep=',',verbose=True, encoding=True) \n", "gpsu  = [\"../datasets/googleplaystore_reviews.csv\"]\n", "gpsu_encoded = d_enc.train_test_split(gpsu, 'Sentiment')\n", "\n", "# replace numerical missing values by median\n", "d1 = imp.Imputer(dataset=gpsu_encoded, strategy = 'MEDIAN',verbose=False).transform()\n", "# decima scaling for numerical variables\n", "d2 = nl.Normalizer(dataset=d1, strategy='DS', exclude = 'Sentiment', verbose=False).transform()\n", "# eliminate 20 LOF outliers\n", "d3 = od.Outlier_detector(dataset=d2, strategy='LOF', threshold= 0.2,verbose=False).transform()\n", "\n", "# classify with LDA\n", "cl.Classifier(dataset=d3,strategy = 'LDA', target = 'Sentiment', verbose =True).transform()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Learn2clean data preprocessing pipeline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Classification with Learn2Clean"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"scrolled": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Start Learn2Clean\n", "Learn2Clean - Pipeline construction -- CPU time: 0.13373112678527832 seconds\n", "=== Start Pipeline Execution ===\n", "\n", "\n", "Strategy# 0 : Greedy traversal for starting state DS\n", "DS -> IQR -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.03691911697387695 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed\n", "* For test dataset\n", "0 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.035826921463012695 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive <PERSON> classification for 10 cross-validation : 0.6394541536988269\n", "\n", "Classification done -- CPU time: 0.06967592239379883 seconds\n", "End Pipeline CPU time: 0.14260268211364746 seconds\n", "\n", "\n", "Strategy# 1 : Greedy traversal for starting state MM\n", "MM -> AD -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.02458024024963379 seconds\n", "\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'AD'.\n", "Number of duplicate rows removed: 7\n", "* For test dataset\n", "Metric is not considered for 'AD'.\n", "Number of duplicate rows removed: 3\n", "Deduplication done -- CPU time: 1.076220989227295 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6395425423627328\n", "\n", "Classification done -- CPU time: 0.06733894348144531 seconds\n", "End Pipeline CPU time: 1.1684529781341553 seconds\n", "\n", "\n", "Strategy# 2 : Greedy traversal for starting state ZS\n", "ZS -> ED -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Normalization \n", "* For train dataset\n", "... train dataset\n", "* For test dataset\n", "... test dataset\n", "Normalization done -- CPU time: 0.024267911911010742 seconds\n", "\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "Initial number of rows: 16708\n", "After deduplication: Number of rows: 16150\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "Initial number of rows: 4306\n", "After deduplication: Number of rows: 4228\n", "Deduplication done -- CPU time: 0.013769865036010742 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6408668730650154\n", "\n", "Classification done -- CPU time: 0.06619596481323242 seconds\n", "End Pipeline CPU time: 0.10438299179077148 seconds\n", "\n", "\n", "Strategy# 3 : Greedy traversal for starting state WR\n", "WR -> IQR -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Apply WR feature selection\n", "Input variables must be non-negative. WR feature selection is only applied to positive variables.\n", "After feature selection:\n", "3 features remain\n", "['Sentiment', 'Sentiment_Subjectivity', 'Sentiment_Polarity']\n", "Feature selection done -- CPU time: 0.01888108253479004 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed\n", "* For test dataset\n", "0 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.03946709632873535 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive <PERSON> classification for 10 cross-validation : 0.6394541536988269\n", "\n", "Classification done -- CPU time: 0.07353591918945312 seconds\n", "End Pipeline CPU time: 0.13321208953857422 seconds\n", "\n", "\n", "Strategy# 4 : Greedy traversal for starting state LC\n", "LC -> LOF -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Apply LC feature selection with threshold= 0.3\n", "1 features with linear correlation greater than 0.30.\n", "\n", "List of correlated variables to be removed : ['Sentiment_Subjectivity']\n", "After feature selection:\n", "4 features remain\n", "['App', 'Sentiment', 'Sentiment_Polarity', 'Translated_Review']\n", "Feature selection done -- CPU time: 0.015712738037109375 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "30 outlying rows have been removed\n", "* For test dataset\n", "30 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.48274779319763184 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6393619573039098\n", "\n", "Classification done -- CPU time: 0.058493852615356445 seconds\n", "End Pipeline CPU time: 0.5583679676055908 seconds\n", "\n", "\n", "Strategy# 5 : Greedy traversal for starting state Tree\n", "Tree -> ED -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "Initial number of rows: 16708\n", "After deduplication: Number of rows: 16150\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "Initial number of rows: 4306\n", "After deduplication: Number of rows: 4228\n", "Deduplication done -- CPU time: 0.015867233276367188 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6408668730650154\n", "\n", "Classification done -- CPU time: 0.06902289390563965 seconds\n", "End Pipeline CPU time: 0.08496212959289551 seconds\n", "\n", "\n", "Strategy# 6 : Greedy traversal for starting state ZSB\n", "ZSB -> ED -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "3507 outlying rows have been removed:\n", "* For test dataset\n", "2137 outlying rows have been removed:\n", "Outlier detection and removal done -- CPU time: 0.047081947326660156 seconds\n", "\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "Initial number of rows: 13201\n", "After deduplication: Number of rows: 12644\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "Initial number of rows: 2169\n", "After deduplication: Number of rows: 2091\n", "Deduplication done -- CPU time: 0.012030839920043945 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6393546346093009\n", "\n", "Classification done -- CPU time: 0.06135296821594238 seconds\n", "End Pipeline CPU time: 0.1205739974975586 seconds\n", "\n", "\n", "Strategy# 7 : Greedy traversal for starting state LOF\n", "LOF -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "30 outlying rows have been removed\n", "* For test dataset\n", "30 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.571540117263794 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6394651636886917\n", "\n", "Classification done -- CPU time: 0.06820893287658691 seconds\n", "End Pipeline CPU time: 0.639833927154541 seconds\n", "\n", "\n", "Strategy# 8 : Greedy traversal for starting state IQR\n", "IQR -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed\n", "* For test dataset\n", "0 outlying rows have been removed\n", "Outlier detection and removal done -- CPU time: 0.0419771671295166 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive <PERSON> classification for 10 cross-validation : 0.6394541536988269\n", "\n", "Classification done -- CPU time: 0.07733488082885742 seconds\n", "End Pipeline CPU time: 0.11939001083374023 seconds\n", "\n", "\n", "Strategy# 9 : Greedy traversal for starting state CC\n", "CC -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Consistency checking\n", "* For train dataset\n", "Constraints from the file: gpsu_example_constraints.tdda\n", "Constraints passing: 16\n", "\n", "Constraints failing: 2\n", "\n", "* For test dataset\n", "Constraints from the file: gpsu_example_constraints.tdda\n", "Constraints passing: 16\n", "\n", "Constraints failing: 2\n", "\n", "Consistency checking done -- CPU time: 0.2267320156097412 seconds\n", "\n", ">>Classification task\n", "Accuracy of Naive <PERSON> classification for 10 cross-validation : 0.6394541536988269\n", "\n", "Classification done -- CPU time: 0.06755876541137695 seconds\n", "End Pipeline CPU time: 0.2943580150604248 seconds\n", "\n", "\n", "Strategy# 10 : Greedy traversal for starting state PC\n", "PC -> NB\n", "\n", "Start pipeline\n", "-------------\n", ">>Consistency checking\n", "* For train dataset\n", "Number of pattern violations on variable ' App 'for pattern# 0 : 15366\n", "Number of pattern violations on variable ' Sentiment 'for pattern# 0 : 16708\n", "****** 0 16708\n", "No record from the dataset satisfied the patterns!\n", "Will return empty dataset - please change our patterns\n", "* For test dataset\n", "Number of pattern violations on variable ' App 'for pattern# 0 : 3940\n", "Number of pattern violations on variable ' Sentiment 'for pattern# 0 : 4306\n", "****** 0 4306\n", "No record from the dataset satisfied the patterns!\n", "Will return empty dataset - please change our patterns\n", "Consistency checking done -- CPU time: 0.09444499015808105 seconds\n", "\n", ">>Classification task\n", "Error: Need at least one continous variable and 10 observations for classification\n", "Classification done -- CPU time: 0.002087116241455078 seconds\n", "End Pipeline CPU time: 0.09660196304321289 seconds\n", "\n", "\n", "Strategy# 11 : Greedy traversal for starting state ED\n", "ED -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "Initial number of rows: 16708\n", "After deduplication: Number of rows: 16150\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "Initial number of rows: 4306\n", "After deduplication: Number of rows: 4228\n", "Deduplication done -- CPU time: 0.015558958053588867 seconds\n", "\n", "\n", ">>Classification task\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6408668730650154\n", "\n", "Classification done -- CPU time: 0.06667399406433105 seconds\n", "End Pipeline CPU time: 0.08294820785522461 seconds\n", "\n", "\n", "Strategy# 12 : Greedy traversal for starting state AD\n", "AD -> NB\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'AD'.\n", "Number of duplicate rows removed: 7\n", "* For test dataset\n", "Metric is not considered for 'AD'.\n", "Number of duplicate rows removed: 3\n", "Deduplication done -- CPU time: 0.886221170425415 seconds\n", "\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.6395425423627328\n", "\n", "Classification done -- CPU time: 0.06791400909423828 seconds\n", "End Pipeline CPU time: 0.954207181930542 seconds\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Classification task\n", "Accuracy of Naive <PERSON> classification for 10 cross-validation : 0.6394541536988269\n", "\n", "Classification done -- CPU time: 0.0760650634765625 seconds\n", "End Pipeline CPU time: 0.07609391212463379 seconds\n", "\n", "==== Recap ====\n", "\n", "List of strategies tried by Learn2Clean:\n", "['DS -> IQR -> NB', 'MM -> AD -> NB', 'ZS -> ED -> NB', 'WR -> IQR -> NB', 'LC -> LOF -> NB', 'Tree -> ED -> NB', 'ZSB -> ED -> NB', 'LOF -> NB', 'IQR -> NB', 'CC -> NB', 'PC -> NB', 'ED -> NB', 'AD -> NB']\n", "\n", "List of corresponding quality metrics ****\n", " [{'quality_metric': 0.6394541536988269}, {'quality_metric': 0.6395425423627328}, {'quality_metric': 0.6408668730650154}, {'quality_metric': 0.6394541536988269}, {'quality_metric': 0.6393619573039098}, {'quality_metric': 0.6408668730650154}, {'quality_metric': 0.6393546346093009}, {'quality_metric': 0.6394651636886917}, {'quality_metric': 0.6394541536988269}, {'quality_metric': 0.6394541536988269}, {'quality_metric': None}, {'quality_metric': 0.6408668730650154}, {'quality_metric': 0.6395425423627328}, {'quality_metric': 0.6394541536988269}]\n", "\n", "Strategy ZS -> ED -> NB for maximal accuracy : 0.6408668730650154 for NB\n", "\n", "=== End of Learn2Clean - Pipeline execution -- CPU time: 4.58355188369751 seconds\n", "\n", "**** Best strategy ****\n", "('gpsu_example', 'learn2clean', 'NB', 'Sentiment', None, 'ZS -> ED -> NB', 'accuracy', 0.6408668730650154, 4.58355188369751)\n"]}], "source": ["import learn2clean.qlearning.qlearner as ql\n", "# Learn2clean finds the best strategy 'ZS -> ED -> NB'for maximal accuracy : 0.0.6408668730650154 for NB\n", "# in 4.58 seconds\n", "# The best strategy is stored in EOF of 'gpsu_example_results.txt' in 'save' directory as\n", "# ('gpsu_example', 'learn2clean', 'NB', 'Sentiment', None, 'ZS -> ED -> NB', 'accuracy', 0.6408668730650154, 4.58355188369751)\n", "l2c_c1assification1=ql.Qlearner(dataset = gpsu_encoded,goal='NB',target_goal='Sentiment',\n", "                                target_prepare=None, file_name = 'gpsu_example', verbose = False)\n", "l2c_c1assification1.learn2clean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Random data preprocessing pipelines"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "--------------------------\n", "Random cleaning strategy:\n", " LC -> ZSB -> ED -> LDA\n", "--------------------------\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Feature selection \n", "Before feature selection:\n", "5 features \n", "Apply LC feature selection with threshold= 0.3\n", "0 features with linear correlation greater than 0.30.\n", "\n", "List of correlated variables to be removed : []\n", "After feature selection:\n", "5 features remain\n", "['Sentiment_Polarity', 'Translated_Review', 'App', 'Sentiment', 'Sentiment_Subjectivity']\n", "Feature selection done -- CPU time: 0.017850160598754883 seconds\n", "\n", "\n", ">>Outlier detection and removal:\n", "* For train dataset\n", "0 outlying rows have been removed:\n", "* For test dataset\n", "0 outlying rows have been removed:\n", "Outlier detection and removal done -- CPU time: 0.06990385055541992 seconds\n", "\n", "\n", ">>Duplicate detection and removal:\n", "* For train dataset\n", "Metric is not considered for 'ED'.\n", "Initial number of rows: 43077\n", "After deduplication: Number of rows: 22180\n", "* For test dataset\n", "Metric is not considered for 'ED'.\n", "Initial number of rows: 21218\n", "After deduplication: Number of rows: 12146\n", "Deduplication done -- CPU time: 0.02665233612060547 seconds\n", "\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.9280734809232218\n", "\n", "Classification done -- CPU time: 0.1755199432373047 seconds\n", "End Pipeline CPU time: 0.35216379165649414 seconds\n", "('gpsu_example', 'random', 'LDA', 'Sentiment', None, 'LC -> ZSB -> ED -> LDA', 'accuracy', ({'quality_metric': 0.9280734809232218}, 0.35216259956359863))\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>App</th>\n", "      <th>Translated_Review</th>\n", "      <th>Sentiment</th>\n", "      <th>Sentiment_Polarity</th>\n", "      <th>Sentiment_Subjectivity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>43353</th>\n", "      <td>Fair: A New Way To Own A Car</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38028</th>\n", "      <td>EMT-B Pocket Prep</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46171</th>\n", "      <td>Firefox Browser fast &amp; private</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49297</th>\n", "      <td>Free Dating Hook Up Messenger</td>\n", "      <td>Try out! It's completely fun provides constant array free date babes. You gotta give try worked ...</td>\n", "      <td>Positive</td>\n", "      <td>0.327881</td>\n", "      <td>0.463540</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14051</th>\n", "      <td>Best Car Wallpapers</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59741</th>\n", "      <td>Hangouts</td>\n", "      <td>Good</td>\n", "      <td>Positive</td>\n", "      <td>0.700000</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58266</th>\n", "      <td>HBO NOW: Stream TV &amp; Movies</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>974</th>\n", "      <td>4 in a Row</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>56608</th>\n", "      <td>Google Photos</td>\n", "      <td>This awesome. I things before. Im new play it. I enjoying every minute. Thank Google</td>\n", "      <td>Positive</td>\n", "      <td>0.545455</td>\n", "      <td>0.684848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27276</th>\n", "      <td>ClassDojo</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38048</th>\n", "      <td>EMT-B Pocket Prep</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62968</th>\n", "      <td>Home workouts - fat burning, abs, legs, arms,chest</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30580</th>\n", "      <td>Credit Sesame</td>\n", "      <td>Please update credit report.I $646 credit cards balance. I 00000 credit balance.</td>\n", "      <td>Neutral</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34871</th>\n", "      <td>Disney Magic Kingdoms: Build Your Own Magical Park</td>\n", "      <td>Really fun game fully character. Great story experience. Smooth game play minimal lag disruption...</td>\n", "      <td>Positive</td>\n", "      <td>0.200000</td>\n", "      <td>0.464286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3319</th>\n", "      <td>ASUS Quick Memo</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6446</th>\n", "      <td>Amazon Kindle</td>\n", "      <td>Would give 5 star rating back lighting adjustable. It much bright damaging eyes.</td>\n", "      <td>Positive</td>\n", "      <td>0.350000</td>\n", "      <td>0.400000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29730</th>\n", "      <td>Couple - Relationship App</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35597</th>\n", "      <td>Domino's Pizza USA</td>\n", "      <td>pretty cool convenient. Deducted star limitations customization occur occur ordering phone chang...</td>\n", "      <td>Positive</td>\n", "      <td>0.212240</td>\n", "      <td>0.584375</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19005</th>\n", "      <td>BuzzFeed: News, Tasty, Quizzes</td>\n", "      <td>This free sending notifications now.and starting save storage tank</td>\n", "      <td>Positive</td>\n", "      <td>0.200000</td>\n", "      <td>0.450000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36020</th>\n", "      <td>Dr. <PERSON> &amp; Toto's Treehouse</td>\n", "      <td>love soo cute love game best</td>\n", "      <td>Positive</td>\n", "      <td>0.420000</td>\n", "      <td>0.580000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17386</th>\n", "      <td>Boys Photo Editor - Six Pack &amp; Men's Suit</td>\n", "      <td>Normal</td>\n", "      <td>Positive</td>\n", "      <td>0.150000</td>\n", "      <td>0.650000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59424</th>\n", "      <td>HTC Speak</td>\n", "      <td>Camera issues This fix camera issues completely. Still rotate properly. However, fixed screen lo...</td>\n", "      <td>Positive</td>\n", "      <td>0.066667</td>\n", "      <td>0.233333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>47286</th>\n", "      <td>Flippy Campus - Buy &amp; sell on campus at a discount</td>\n", "      <td>Good</td>\n", "      <td>Positive</td>\n", "      <td>0.700000</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21878</th>\n", "      <td><PERSON><PERSON>e Counter - Macros</td>\n", "      <td>I love simplicity pleasant uncluttered design. I would love see feature adding individual food i...</td>\n", "      <td>Positive</td>\n", "      <td>0.433333</td>\n", "      <td>0.641667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61736</th>\n", "      <td>High-Powered Flashlight</td>\n", "      <td>Very strong light. I know eggs gone bad</td>\n", "      <td>Positive</td>\n", "      <td>0.087778</td>\n", "      <td>0.773333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>44678</th>\n", "      <td>Farming Simulator 18</td>\n", "      <td>That tractors speed increase move tractor field another field option available means good game p...</td>\n", "      <td>Positive</td>\n", "      <td>0.233333</td>\n", "      <td>0.466667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4874</th>\n", "      <td>AirBrush: Easy Photo Editor</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35670</th>\n", "      <td>Domofond ÐÐµÐ´Ð²Ð¸Ð¶Ð¸Ð¼Ð¾ÑÑÑ. ÐÑÐ¿Ð¸ÑÑ, ÑÐ½ÑÑÑ ÐºÐ²Ð°ÑÑÐ¸ÑÑ.</td>\n", "      <td>All is well But there is a remark, where is the type of apartments - studio?) In the web version...</td>\n", "      <td>Neutral</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38043</th>\n", "      <td>EMT-B Pocket Prep</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49767</th>\n", "      <td>Free phone calls, free texting SMS on free number</td>\n", "      <td>I mobile signal village WiFi calling built phone standard godsend. Downloading installing simple...</td>\n", "      <td>Positive</td>\n", "      <td>0.155000</td>\n", "      <td>0.406429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58929</th>\n", "      <td>HOTEL DEALS</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39765</th>\n", "      <td>Emoji Keyboard - <PERSON><PERSON>,<PERSON><PERSON>, <PERSON><PERSON>, Emoticon</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14647</th>\n", "      <td>Bible</td>\n", "      <td>Excellent!</td>\n", "      <td>Positive</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7682</th>\n", "      <td>Angry Birds Rio</td>\n", "      <td>Fling birds structures- classic idea!!! Still best angry bird games left. With collectable items...</td>\n", "      <td>Positive</td>\n", "      <td>0.087792</td>\n", "      <td>0.335204</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33274</th>\n", "      <td>Dating for 50 plus Mature Singles â FINALLY</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25708</th>\n", "      <td>Chess Free</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29511</th>\n", "      <td>Couch to 10K Running Trainer</td>\n", "      <td>This getting going steady rate. I really feel I follow I WILL 10k 14 weeks!</td>\n", "      <td>Positive</td>\n", "      <td>0.208333</td>\n", "      <td>0.350000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12782</th>\n", "      <td>Barcode Scanner</td>\n", "      <td>I help ensure vendor compliance Global Supply Chain. It works perfectly.</td>\n", "      <td>Positive</td>\n", "      <td>0.500000</td>\n", "      <td>0.500000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30729</th>\n", "      <td>Cricbuzz - Live Cricket Scores &amp; News</td>\n", "      <td>This best Cricket App. I loved it. It everything cricket fan wants. Discussing commenting amazin...</td>\n", "      <td>Positive</td>\n", "      <td>0.452841</td>\n", "      <td>0.581250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>59266</th>\n", "      <td>HTC Sense Input-AR</td>\n", "      <td>Superior Mast like</td>\n", "      <td>Positive</td>\n", "      <td>0.700000</td>\n", "      <td>0.900000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>62683</th>\n", "      <td>Home ScoutingÂ® MLS Mobile</td>\n", "      <td>Overall good app. I would like see larger pictures, ability driving directions go straight GPS n...</td>\n", "      <td>Positive</td>\n", "      <td>0.225000</td>\n", "      <td>0.375000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21813</th>\n", "      <td><PERSON><PERSON>e Counter - Macros</td>\n", "      <td>Simple A simple &amp; clear log log macros track weight. Let users edit weights like edit past food ...</td>\n", "      <td>Negative</td>\n", "      <td>-0.054697</td>\n", "      <td>0.320216</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41360</th>\n", "      <td>Expedia Hotels, Flights &amp; Car Rental Travel Deals</td>\n", "      <td>Great rates could give selection hotels area searching. I feel like getting nearby hotels I sear...</td>\n", "      <td>Positive</td>\n", "      <td>0.800000</td>\n", "      <td>0.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27417</th>\n", "      <td>Classical music for baby</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33461</th>\n", "      <td>Davis's Drug Guide for Nurses</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36230</th>\n", "      <td>Dragon Hills</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>41504</th>\n", "      <td>Expedia Hotels, Flights &amp; Car Rental Travel Deals</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>58554</th>\n", "      <td>HD Camera for Android</td>\n", "      <td>The quality functionality great auto focus great</td>\n", "      <td>Positive</td>\n", "      <td>0.800000</td>\n", "      <td>0.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49485</th>\n", "      <td>Free Meditation - Take a Break</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22525</th>\n", "      <td>Calorie Counter by FatSecret</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12349</th>\n", "      <td>Bangla Newspaper â Prothom Alo</td>\n", "      <td>good free news</td>\n", "      <td>Positive</td>\n", "      <td>0.550000</td>\n", "      <td>0.700000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>60994</th>\n", "      <td>Helix Jump</td>\n", "      <td>I enjoy game. Too bad I paid ads option still receive constant pop ads videos. I emailed respons...</td>\n", "      <td>Negative</td>\n", "      <td>-0.103333</td>\n", "      <td>0.366667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29354</th>\n", "      <td>Cooking Madness - A Chef's Restaurant Games</td>\n", "      <td>Nice large game takes space phone do.â</td>\n", "      <td>Positive</td>\n", "      <td>0.138095</td>\n", "      <td>0.609524</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55280</th>\n", "      <td>Google Calendar</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15013</th>\n", "      <td><PERSON><PERSON> â Birthday reminder</td>\n", "      <td>Good come email folder notify server miss notifications sometimes. Handy though</td>\n", "      <td>Positive</td>\n", "      <td>0.650000</td>\n", "      <td>0.750000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>61701</th>\n", "      <td>High Blood Pressure Symptoms</td>\n", "      <td>Nothing here. Nothing enticements \"go pro.\" NOT RECOMMENDED</td>\n", "      <td>Neutral</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>43613</th>\n", "      <td>Family GPS Tracker and Chat + Baby Monitor Online</td>\n", "      <td>Not good</td>\n", "      <td>Negative</td>\n", "      <td>-0.350000</td>\n", "      <td>0.600000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6494</th>\n", "      <td>Amazon Kindle</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>63043</th>\n", "      <td>HomeWork</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>36392</th>\n", "      <td>Draw A Stickman</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>43077 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                                                                                 App  \\\n", "43353                                                   Fair: A New Way To Own A Car   \n", "38028                                                              EMT-B Pocket Prep   \n", "46171                                                 Firefox Browser fast & private   \n", "49297                                                  Free Dating Hook Up Messenger   \n", "14051                                                            Best Car Wallpapers   \n", "59741                                                                       Hangouts   \n", "58266                                                    HBO NOW: Stream TV & Movies   \n", "974                                                                       4 in a Row   \n", "56608                                                                  Google Photos   \n", "27276                                                                      ClassDojo   \n", "38048                                                              EMT-B Pocket Prep   \n", "62968                             Home workouts - fat burning, abs, legs, arms,chest   \n", "30580                                                                  Credit Sesame   \n", "34871                             Disney Magic Kingdoms: Build Your Own Magical Park   \n", "3319                                                                 ASUS Quick Memo   \n", "6446                                                                   Amazon Kindle   \n", "29730                                                      Couple - Relationship App   \n", "35597                                                             Domino's Pizza USA   \n", "19005                                                 BuzzFeed: News, Tasty, Quizzes   \n", "36020                                                   Dr. Panda & Toto's Treehouse   \n", "17386                                      Boys Photo Editor - Six Pack & Men's Suit   \n", "59424                                                                      HTC Speak   \n", "47286                             Flippy Campus - Buy & sell on campus at a discount   \n", "21878                                                       Calorie Counter - Macros   \n", "61736                                                        High-Powered Flashlight   \n", "44678                                                           Farming Simulator 18   \n", "4874                                                     AirBrush: Easy Photo Editor   \n", "35670  Domofond ÐÐµÐ´Ð²Ð¸Ð¶Ð¸Ð¼Ð¾ÑÑÑ. ÐÑÐ¿Ð¸ÑÑ, ÑÐ½ÑÑÑ ÐºÐ²Ð°ÑÑÐ¸ÑÑ.   \n", "38043                                                              EMT-B Pocket Prep   \n", "49767                              Free phone calls, free texting SMS on free number   \n", "...                                                                              ...   \n", "58929                                                                    HOTEL DEALS   \n", "39765                             Emoji Keyboard - <PERSON><PERSON>,<PERSON><PERSON>, <PERSON><PERSON>, Em<PERSON>on   \n", "14647                                                                          Bible   \n", "7682                                                                 Angry Birds Rio   \n", "33274                                  <PERSON><PERSON> for 50 plus Mature Singles â FINALLY   \n", "25708                                                                     Chess Free   \n", "29511                                                   <PERSON>uch to 10K Running Trainer   \n", "12782                                                                Barcode Scanner   \n", "30729                                          Cricbuzz - Live Cricket Scores & News   \n", "59266                                                             HTC Sense Input-AR   \n", "62683                                                     Home ScoutingÂ® MLS Mobile   \n", "21813                                                       Calorie Counter - Macros   \n", "41360                              Expedia Hotels, Flights & Car Rental Travel Deals   \n", "27417                                                       Classical music for baby   \n", "33461                                                  Davis's Drug Guide for Nurses   \n", "36230                                                                   Dragon Hills   \n", "41504                              Expedia Hotels, Flights & Car Rental Travel Deals   \n", "58554                                                          HD Camera for Android   \n", "49485                                                 Free Meditation - Take a Break   \n", "22525                                                   Calorie Counter by FatSecret   \n", "12349                                               Bangla Newspaper â Prothom Alo   \n", "60994                                                                     Helix Jump   \n", "29354                                    Cooking Madness - A Chef's Restaurant Games   \n", "55280                                                                Google Calendar   \n", "15013                                                  Birdays â Birthday reminder   \n", "61701                                                   High Blood Pressure Symptoms   \n", "43613                              Family GPS Tracker and Chat + Baby Monitor Online   \n", "6494                                                                   Amazon Kindle   \n", "63043                                                                       HomeWork   \n", "36392                                                                Draw A <PERSON>   \n", "\n", "                                                                                         Translated_Review  \\\n", "43353                                                                                                  NaN   \n", "38028                                                                                                  NaN   \n", "46171                                                                                                  NaN   \n", "49297  Try out! It's completely fun provides constant array free date babes. You gotta give try worked ...   \n", "14051                                                                                                  NaN   \n", "59741                                                                                                 Good   \n", "58266                                                                                                  NaN   \n", "974                                                                                                    NaN   \n", "56608                 This awesome. I things before. Im new play it. I enjoying every minute. Thank Google   \n", "27276                                                                                                  NaN   \n", "38048                                                                                                  NaN   \n", "62968                                                                                                  NaN   \n", "30580                     Please update credit report.I $646 credit cards balance. I 00000 credit balance.   \n", "34871  Really fun game fully character. Great story experience. Smooth game play minimal lag disruption...   \n", "3319                                                                                                   NaN   \n", "6446                      Would give 5 star rating back lighting adjustable. It much bright damaging eyes.   \n", "29730                                                                                                  NaN   \n", "35597  pretty cool convenient. Deducted star limitations customization occur occur ordering phone chang...   \n", "19005                                   This free sending notifications now.and starting save storage tank   \n", "36020                                                                         love soo cute love game best   \n", "17386                                                                                               Normal   \n", "59424  Camera issues This fix camera issues completely. Still rotate properly. However, fixed screen lo...   \n", "47286                                                                                                 Good   \n", "21878  I love simplicity pleasant uncluttered design. I would love see feature adding individual food i...   \n", "61736                                                              Very strong light. I know eggs gone bad   \n", "44678  That tractors speed increase move tractor field another field option available means good game p...   \n", "4874                                                                                                   NaN   \n", "35670  All is well But there is a remark, where is the type of apartments - studio?) In the web version...   \n", "38043                                                                                                  NaN   \n", "49767  I mobile signal village WiFi calling built phone standard godsend. Downloading installing simple...   \n", "...                                                                                                    ...   \n", "58929                                                                                                  NaN   \n", "39765                                                                                                  NaN   \n", "14647                                                                                           Excellent!   \n", "7682   Fling birds structures- classic idea!!! Still best angry bird games left. With collectable items...   \n", "33274                                                                                                  NaN   \n", "25708                                                                                                  NaN   \n", "29511                          This getting going steady rate. I really feel I follow I WILL 10k 14 weeks!   \n", "12782                             I help ensure vendor compliance Global Supply Chain. It works perfectly.   \n", "30729  This best Cricket App. I loved it. It everything cricket fan wants. Discussing commenting amazin...   \n", "59266                                                                                   Superior Mast like   \n", "62683  Overall good app. I would like see larger pictures, ability driving directions go straight GPS n...   \n", "21813  Simple A simple & clear log log macros track weight. Let users edit weights like edit past food ...   \n", "41360  Great rates could give selection hotels area searching. I feel like getting nearby hotels I sear...   \n", "27417                                                                                                  NaN   \n", "33461                                                                                                  NaN   \n", "36230                                                                                                  NaN   \n", "41504                                                                                                  NaN   \n", "58554                                                     The quality functionality great auto focus great   \n", "49485                                                                                                  NaN   \n", "22525                                                                                                  NaN   \n", "12349                                                                                       good free news   \n", "60994  I enjoy game. Too bad I paid ads option still receive constant pop ads videos. I emailed respons...   \n", "29354                                                             Nice large game takes space phone do.â   \n", "55280                                                                                                  NaN   \n", "15013                      Good come email folder notify server miss notifications sometimes. Handy though   \n", "61701                                          Nothing here. Nothing enticements \"go pro.\" NOT RECOMMENDED   \n", "43613                                                                                             Not good   \n", "6494                                                                                                   NaN   \n", "63043                                                                                                  NaN   \n", "36392                                                                                                  NaN   \n", "\n", "      Sentiment  Sentiment_Polarity  Sentiment_Subjectivity  \n", "43353       NaN                 NaN                     NaN  \n", "38028       NaN                 NaN                     NaN  \n", "46171       NaN                 NaN                     NaN  \n", "49297  Positive            0.327881                0.463540  \n", "14051       NaN                 NaN                     NaN  \n", "59741  Positive            0.700000                0.600000  \n", "58266       NaN                 NaN                     NaN  \n", "974         NaN                 NaN                     NaN  \n", "56608  Positive            0.545455                0.684848  \n", "27276       NaN                 NaN                     NaN  \n", "38048       NaN                 NaN                     NaN  \n", "62968       NaN                 NaN                     NaN  \n", "30580   Neutral            0.000000                0.000000  \n", "34871  Positive            0.200000                0.464286  \n", "3319        NaN                 NaN                     NaN  \n", "6446   Positive            0.350000                0.400000  \n", "29730       NaN                 NaN                     NaN  \n", "35597  Positive            0.212240                0.584375  \n", "19005  Positive            0.200000                0.450000  \n", "36020  Positive            0.420000                0.580000  \n", "17386  Positive            0.150000                0.650000  \n", "59424  Positive            0.066667                0.233333  \n", "47286  Positive            0.700000                0.600000  \n", "21878  Positive            0.433333                0.641667  \n", "61736  Positive            0.087778                0.773333  \n", "44678  Positive            0.233333                0.466667  \n", "4874        NaN                 NaN                     NaN  \n", "35670   Neutral            0.000000                0.000000  \n", "38043       NaN                 NaN                     NaN  \n", "49767  Positive            0.155000                0.406429  \n", "...         ...                 ...                     ...  \n", "58929       NaN                 NaN                     NaN  \n", "39765       NaN                 NaN                     NaN  \n", "14647  Positive            1.000000                1.000000  \n", "7682   Positive            0.087792                0.335204  \n", "33274       NaN                 NaN                     NaN  \n", "25708       NaN                 NaN                     NaN  \n", "29511  Positive            0.208333                0.350000  \n", "12782  Positive            0.500000                0.500000  \n", "30729  Positive            0.452841                0.581250  \n", "59266  Positive            0.700000                0.900000  \n", "62683  Positive            0.225000                0.375000  \n", "21813  Negative           -0.054697                0.320216  \n", "41360  Positive            0.800000                0.750000  \n", "27417       NaN                 NaN                     NaN  \n", "33461       NaN                 NaN                     NaN  \n", "36230       NaN                 NaN                     NaN  \n", "41504       NaN                 NaN                     NaN  \n", "58554  Positive            0.800000                0.750000  \n", "49485       NaN                 NaN                     NaN  \n", "22525       NaN                 NaN                     NaN  \n", "12349  Positive            0.550000                0.700000  \n", "60994  Negative           -0.103333                0.366667  \n", "29354  Positive            0.138095                0.609524  \n", "55280       NaN                 NaN                     NaN  \n", "15013  Positive            0.650000                0.750000  \n", "61701   Neutral            0.000000                0.000000  \n", "43613  Negative           -0.350000                0.600000  \n", "6494        NaN                 NaN                     NaN  \n", "63043       NaN                 NaN                     NaN  \n", "36392       NaN                 NaN                     NaN  \n", "\n", "[43077 rows x 5 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["import learn2clean.loading.reader as rd \n", "import learn2clean.qlearning.qlearner as ql\n", "\n", "d_enc = rd.Reader(sep=',',verbose=False, encoding=True) \n", "gpsu  = [\"../datasets/googleplaystore_reviews.csv\"]\n", "gpsu_encoded = d_enc.train_test_split(gpsu, 'Sentiment')\n", "\n", "# the results of random cleaning are stored in 'gpsu_example'_results_file.txt in 'save' directory\n", "# appended to the EOF \n", "# random pipeline for LDA classification\n", "random1=ql.Qlearner(gpsu_encoded.copy(),goal='LDA',target_goal='Sentiment',target_prepare=None, verbose = False)\n", "random1.random_cleaning('gpsu_example')\n", "\n", "gpsu_encoded['train']\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Start pipeline\n", "-------------\n", "\n", ">>Classification task\n", "Avg accuracy of CART classification for 10 cross-validation : 0.9987655795802971\n", "\n", "Classification done -- CPU time: 163.18577790260315 seconds\n", "End Pipeline CPU time: 163.2061710357666 seconds\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Classification task\n", "\n", "Accuracy of LDA result for 10 cross-validation : 0.9286027157249234\n", "\n", "Classification done -- CPU time: 0.19683003425598145 seconds\n", "End Pipeline CPU time: 0.2148118019104004 seconds\n", "\n", "Start pipeline\n", "-------------\n", "\n", ">>Classification task\n", "Accuracy of Naive Na<PERSON> classification for 10 cross-validation : 0.9681439891689563\n", "\n", "Classification done -- CPU time: 0.4236910343170166 seconds\n", "End Pipeline CPU time: 0.4411659240722656 seconds\n"]}], "source": ["# no preprocessing: results appended to the EOF 'gpsu_example'_results.txt \n", "\n", "no_prep1=ql.Qlearner(gpsu_encoded.copy(),goal='CART',target_goal='Sentiment',target_prepare=None, verbose = False)\n", "no_prep1.no_prep('gpsu_example')\n", "\n", "no_prep2=ql.Qlearner(gpsu_encoded.copy(),goal='LDA',target_goal='Sentiment',target_prepare=None, verbose = False)\n", "no_prep2.no_prep('gpsu_example')\n", "\n", "no_prep3=ql.Qlearner(gpsu_encoded.copy(),goal='NB',target_goal='Sentiment',target_prepare=None, verbose = False)\n", "no_prep3.no_prep('gpsu_example')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:anaconda3]", "language": "python", "name": "conda-env-anaconda3-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.5"}}, "nbformat": 4, "nbformat_minor": 2}