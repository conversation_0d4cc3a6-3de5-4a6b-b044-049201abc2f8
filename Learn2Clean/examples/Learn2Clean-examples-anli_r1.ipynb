{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Learn2Clean Example: ANLI R1 Dataset\n", "\n", "This notebook demonstrates how to apply Learn2Clean to the ANLI R1 (Adversarial Natural Language Inference Round 1) dataset for text classification."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Setting up Learn2Clean with compatible package versions...\n", "/storage/nammt/autogluon/Learn2Clean/python-package\n", "\u001b[33mWARNING: Skipping learn2clean as it is not installed.\u001b[0m\n", "\u001b[33mWARNING: Skipping learn2clean as it is not installed.\u001b[0m\n", "Obtaining file:///storage/nammt/autogluon/Learn2Clean/python-package\n", "Obtaining file:///storage/nammt/autogluon/Learn2Clean/python-package\n", "Installing collected packages: learn2clean\n", "  Running setup.py develop for learn2clean\n", "Installing collected packages: learn2clean\n", "  Running setup.py develop for learn2clean\n", "Successfully installed learn2clean\n", "\u001b[33mWARNING: You are using pip version 20.3.4; however, version 24.0 is available.\n", "You should consider upgrading via the '/storage/nammt/autogluon/learn2clean_env/bin/python3.7 -m pip install --upgrade pip' command.\u001b[0m\n", "Successfully installed learn2clean\n", "\u001b[33mWARNING: You are using pip version 20.3.4; however, version 24.0 is available.\n", "You should consider upgrading via the '/storage/nammt/autogluon/learn2clean_env/bin/python3.7 -m pip install --upgrade pip' command.\u001b[0m\n", "Installing compatible dependencies...\n", "Installing compatible dependencies...\n", "Collecting python-<PERSON><PERSON><PERSON><PERSON>\n", "Collecting python-<PERSON><PERSON><PERSON><PERSON>\n", "  Downloading python_Levenshtein-0.23.0-py3-none-any.whl (9.4 kB)\n", "  Downloading python_Levenshtein-0.23.0-py3-none-any.whl (9.4 kB)\n", "Collecting fuzzywuzzy\n", "Collecting fuzzywuzzy\n", "  Downloading fuzzywuzzy-0.18.0-py2.py3-none-any.whl (18 kB)\n", "  Downloading fuzzywuzzy-0.18.0-py2.py3-none-any.whl (18 kB)\n", "Collecting <PERSON><PERSON><PERSON><PERSON>==0.23.0\n", "Collecting <PERSON><PERSON><PERSON><PERSON>==0.23.0\n", "  Downloading Levenshtein-0.23.0-cp37-cp37m-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (170 kB)\n", "\u001b[?25l  Downloading Levenshtein-0.23.0-cp37-cp37m-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (170 kB)\n", "\u001b[K     |████████████████████████████████| 170 kB 1.3 MB/s eta 0:00:01\n", "\u001b[K     |████████████████████████████████| 170 kB 1.3 MB/s \n", "\u001b[?25hCollecting rapidfuzz<4.0.0,>=3.1.0\n", "Collecting rapidfuzz<4.0.0,>=3.1.0\n", "  Downloading rapidfuzz-3.4.0-cp37-cp37m-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.2 MB)\n", "\u001b[K     |██                              | 204 kB 9.9 MB/s eta 0:00:01  Downloading rapidfuzz-3.4.0-cp37-cp37m-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (3.2 MB)\n", "\u001b[K     |████████████████████████████████| 3.2 MB 9.9 MB/s eta 0:00:01     |███████████▋                    | 1.1 MB 9.9 MB/s eta 0:00:01     |███████████▋                    | 1.1 MB 9.9 MB/s eta 0:00:01\n", "\u001b[K     |████████████████████████████████| 3.2 MB 9.9 MB/s eta 0:00:01\n", "\u001b[?25hInstalling collected packages: rapidfuzz, Levenshtein, python-Levenshtein, fuzzywuzzy\n", "Installing collected packages: rapidfuzz, Levenshtein, python-Levenshtein, fuzzywuzzy\n", "Successfully installed Levenshtein-0.23.0 fuzzywuzzy-0.18.0 python-Levenshtein-0.23.0 rapidfuzz-3.4.0\n", "\u001b[33mWARNING: You are using pip version 20.3.4; however, version 24.0 is available.\n", "You should consider upgrading via the '/storage/nammt/autogluon/learn2clean_env/bin/python3.7 -m pip install --upgrade pip' command.\u001b[0m\n", "Successfully installed Levenshtein-0.23.0 fuzzywuzzy-0.18.0 python-Levenshtein-0.23.0 rapidfuzz-3.4.0\n", "\u001b[33mWARNING: You are using pip version 20.3.4; however, version 24.0 is available.\n", "You should consider upgrading via the '/storage/nammt/autogluon/learn2clean_env/bin/python3.7 -m pip install --upgrade pip' command.\u001b[0m\n", "✓ Installed alternative string matching libraries\n", "✓ Using existing numpy, pandas, scikit-learn, scipy, matplotlib\n", "⚠ Skipping fancyimpute and string matching libraries due to version conflicts\n", "/storage/nammt/autogluon/Learn2Clean/examples\n", "\n", "✓ Learn2Clean installed with core functionality!\n", "Note: Some advanced features (fancy imputation, string similarity) may be limited\n", "✓ Installed alternative string matching libraries\n", "✓ Using existing numpy, pandas, scikit-learn, scipy, matplotlib\n", "⚠ Skipping fancyimpute and string matching libraries due to version conflicts\n", "/storage/nammt/autogluon/Learn2Clean/examples\n", "\n", "✓ Learn2Clean installed with core functionality!\n", "Note: Some advanced features (fancy imputation, string similarity) may be limited\n"]}], "source": ["# Install Learn2Clean with compatible versions - avoiding dependency conflicts\n", "import os\n", "import sys\n", "\n", "print(\"Setting up Learn2Clean with compatible package versions...\")\n", "\n", "if os.path.exists('../python-package'):\n", "    %cd ../python-package\n", "    \n", "    # First uninstall any existing Learn2Clean\n", "    !pip uninstall -y learn2clean\n", "    \n", "    # Install Learn2Clean without dependencies to avoid conflicts\n", "    !pip install -e . --no-deps\n", "    \n", "    # Now install compatible versions of the dependencies we need\n", "    print(\"Installing compatible dependencies...\")\n", "    \n", "    # Install string matching libraries with fallback\n", "    try:\n", "        # Try alternative string matching that might compile better\n", "        !pip install python-<PERSON><PERSON><PERSON><PERSON> fuzzywuzzy\n", "        print(\"✓ Installed alternative string matching libraries\")\n", "    except:\n", "        print(\"⚠ Warning: Advanced string matching not available, using basic alternatives\")\n", "    \n", "    # Install basic ML dependencies we already have\n", "    print(\"✓ Using existing numpy, pandas, scikit-learn, scipy, matplotlib\")\n", "    \n", "    # Skip problematic dependencies (fancyimpute, py_stringmatching, py_stringsimjoin)\n", "    print(\"⚠ Skipping fancyimpute and string matching libraries due to version conflicts\")\n", "    \n", "    %cd ../examples\n", "    \n", "    print(\"\\n✓ Learn2Clean installed with core functionality!\")\n", "    print(\"Note: Some advanced features (fancy imputation, string similarity) may be limited\")\n", "    \n", "else:\n", "    print(\"Learn2Clean python-package directory not found. Please check the path.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1) Dataset Loading and Preparation"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading ANLI R1 dataset...\n", "Downloading and preparing dataset None/plain_text to /home/<USER>/.cache/huggingface/datasets/facebook___parquet/plain_text-bc7c6c9c4d1b458b/0.0.0/14a00e99c0d15a23649d0db8944380ac81082d4b021f398733dd84f3a6c569a7...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Downloading data files: 100%|██████████| 3/3 [00:00<00:00, 1115.61it/s]\n", "Extracting data files: 100%|██████████| 3/3 [00:00<00:00, 239.68it/s]\n", "                                                                     \r"]}, {"name": "stdout", "output_type": "stream", "text": ["Error loading ANLI: {'train_r1', 'dev_r1', 'test_r2', 'train_r2', 'dev_r2', 'test_r1', 'train_r3', 'dev_r3', 'test_r3'}\n"]}], "source": ["# Load required libraries\n", "import pandas as pd\n", "import numpy as np\n", "from datasets import load_dataset\n", "import os\n", "\n", "def load_anli_r1_dataset():\n", "    \"\"\"Load and prepare ANLI R1 dataset for text classification\"\"\"\n", "    print(\"Loading ANLI R1 dataset...\")\n", "    \n", "    try:\n", "        dataset = load_dataset(\"facebook/anli\")\n", "        \n", "        def prepare_anli_data(split_data):\n", "            data = []\n", "            for item in split_data:\n", "                # Combine premise and hypothesis for NLI\n", "                text_features = f\"[PREMISE] {item['premise']} [HYPOTHESIS] {item['hypothesis']}\"\n", "                \n", "                data.append({\n", "                    'text': text_features,\n", "                    'premise': item['premise'],\n", "                    'hypothesis': item['hypothesis'],\n", "                    'label': item['label']\n", "                })\n", "            return pd.DataFrame(data)\n", "        \n", "        train_df = prepare_anli_data(dataset['train_r1'])\n", "        val_df = prepare_anli_data(dataset['dev_r1'])\n", "        test_df = prepare_anli_data(dataset['test_r1'])\n", "\n", "        print(f\"ANLI R1 loaded: Train={len(train_df)}, Val={len(val_df)}, Test={len(test_df)}\")\n", "        return train_df, val_df, test_df\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading ANLI: {e}\")\n", "        return None, None, None\n", "\n", "# Load the dataset\n", "train_df, val_df, test_df = load_anli_r1_dataset()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Display basic information about the dataset\n", "if train_df is not None:\n", "    print(\"Dataset shape:\")\n", "    print(f\"Train: {train_df.shape}\")\n", "    print(f\"Validation: {val_df.shape}\")\n", "    print(f\"Test: {test_df.shape}\")\n", "    \n", "    print(\"\\nFirst few rows:\")\n", "    display(train_df.head())\n", "    \n", "    print(\"\\nLabel distribution:\")\n", "    print(train_df['label'].value_counts())\n", "    \n", "    print(\"\\nColumn info:\")\n", "    print(train_df.info())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2) Prepare Data for Learn2Clean\n", "\n", "Learn2Clean works with CSV files, so we need to save our data and create a reader function."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Datasets saved successfully!\n", "Train size: 16946\n", "Validation size: 1000\n", "Test size: 1000\n", "\n", "IMPORTANT: Train/val/test kept separate to avoid data leakage for AutoGluon!\n"]}], "source": ["# Create datasets directory if it doesn't exist\n", "os.makedirs('../datasets/anli_r1', exist_ok=True)\n", "\n", "# Save datasets as CSV files - KEEP TRAIN AND VALIDATION SEPARATE!\n", "if train_df is not None:\n", "    # Save train, validation, and test separately to avoid data leakage\n", "    train_df.to_csv('../datasets/anli_r1/anli_r1_train.csv', index=False, encoding='utf-8')\n", "    val_df.to_csv('../datasets/anli_r1/anli_r1_val.csv', index=False, encoding='utf-8')\n", "    test_df.to_csv('../datasets/anli_r1/anli_r1_test.csv', index=False, encoding='utf-8')\n", "    \n", "    print(\"Datasets saved successfully!\")\n", "    print(f\"Train size: {len(train_df)}\")\n", "    print(f\"Validation size: {len(val_df)}\")\n", "    print(f\"Test size: {len(test_df)}\")\n", "    print(\"\\nIMPORTANT: Train/val/test kept separate to avoid data leakage for AutoGluon!\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded train dataset shape: (16946, 4)\n", "Columns: ['text', 'premise', 'hypothesis', 'label']\n", "\n", "Dataset split sizes:\n", "Train: 16946\n", "Validation: 1000\n", "Test: 1000\n"]}], "source": ["# Define dataset reader function for Learn2Clean\n", "def read_dataset(name):\n", "    \"\"\"Load datasets for Learn2Clean processing\"\"\"\n", "    import pandas as pd\n", "    if name == \"anli_r1\":\n", "        df = pd.read_csv('../datasets/anli_r1/anli_r1_train.csv', sep=',', encoding='utf-8')\n", "    elif name == \"anli_r1_val\":\n", "        df = pd.read_csv('../datasets/anli_r1/anli_r1_val.csv', sep=',', encoding='utf-8')\n", "    elif name == \"anli_r1_test\":\n", "        df = pd.read_csv('../datasets/anli_r1/anli_r1_test.csv', sep=',', encoding='utf-8')\n", "    else: \n", "        raise ValueError('Invalid dataset name')               \n", "    return df\n", "\n", "# Test the reader function\n", "test_load = read_dataset(\"anli_r1\")\n", "print(f\"Loaded train dataset shape: {test_load.shape}\")\n", "print(f\"Columns: {test_load.columns.tolist()}\")\n", "\n", "# Verify all splits\n", "print(f\"\\nDataset split sizes:\")\n", "print(f\"Train: {len(read_dataset('anli_r1'))}\")\n", "print(f\"Validation: {len(read_dataset('anli_r1_val'))}\")\n", "print(f\"Test: {len(read_dataset('anli_r1_test'))}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3) Data Profiling with Learn2Clean"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "cannot import name 'loading' from 'Learn2Clean' (/storage/nammt/autogluon/Learn2Clean/python-package/learn2clean/__init__.py)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "\u001b[0;32m/tmp/ipykernel_2133952/2273101813.py\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[0msys\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mappend\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mos\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpath\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mabspath\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m'../python-package'\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 6\u001b[0;31m \u001b[0;32mimport\u001b[0m \u001b[0mlearn2clean\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mloading\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mreader\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mrd\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m      7\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mlearn2clean\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnormalization\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnormalizer\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mnl\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      8\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mpandas\u001b[0m \u001b[0;32mas\u001b[0m \u001b[0mpd\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mImportError\u001b[0m: cannot import name 'loading' from 'Learn2Clean' (/storage/nammt/autogluon/Learn2Clean/python-package/learn2clean/__init__.py)"]}], "source": ["# Add Learn2Clean to Python path\n", "import sys\n", "import os\n", "sys.path.append(os.path.abspath('../python-package'))\n", "\n", "import learn2clean.loading.reader as rd \n", "import learn2clean.normalization.normalizer as nl \n", "import pandas as pd\n", "\n", "# Execute profiling function for ANLI R1 dataset\n", "rd.profile_summary(read_dataset('anli_r1'), plot=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check the target variable\n", "anli_data = read_dataset('anli_r1')\n", "print(\"Target variable (label) distribution:\")\n", "print(anli_data['label'].value_counts())\n", "print(\"\\nTarget variable head:\")\n", "print(anli_data['label'].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4) Learn2Clean Data Processing\n", "\n", "Now we'll use Learn2Clean's Reader class to process the ANLI R1 dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create Learn2Clean reader with encoding for text classification\n", "d_enc = rd.Reader(sep=',', verbose=True, encoding=True) \n", "\n", "# Process ANLI R1 dataset - ONLY TRAIN DATA for Learn2Clean optimization\n", "# This avoids data leakage by not using validation data in preprocessing decisions\n", "anli_r1_files = [\"../datasets/anli_r1/anli_r1_train.csv\"]\n", "anli_r1_encoded = d_enc.train_test_split(anli_r1_files, 'label')\n", "\n", "print(\"\\nProcessed dataset structure (TRAIN ONLY):\")\n", "print(f\"Train shape: {anli_r1_encoded['train'].shape}\")\n", "print(f\"Target shape: {anli_r1_encoded['target'].shape}\")\n", "print(f\"Target name: {anli_r1_encoded['target'].name}\")\n", "print(\"\\nNote: Only training data used for Learn2Clean to avoid data leakage!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5) Manual Data Cleaning Pipeline\n", "\n", "Let's create a manual preprocessing pipeline for text classification."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add Learn2Clean to Python path (if not already done)\n", "import sys\n", "import os\n", "if '../python-package' not in sys.path:\n", "    sys.path.append(os.path.abspath('../python-package'))\n", "\n", "# Import Learn2Clean modules for manual pipeline\n", "import learn2clean.loading.reader as rd \n", "import learn2clean.normalization.normalizer as nl \n", "import learn2clean.feature_selection.feature_selector as fs\n", "import learn2clean.duplicate_detection.duplicate_detector as dd\n", "import learn2clean.outlier_detection.outlier_detector as od\n", "import learn2clean.imputation.imputer as imp\n", "import learn2clean.classification.classifier as cl\n", "\n", "# Create a copy of the dataset for manual processing\n", "manual_dataset = anli_r1_encoded.copy()\n", "\n", "print(\"Starting manual preprocessing pipeline...\")\n", "\n", "# Step 1: <PERSON><PERSON> missing values\n", "print(\"\\n1. Imputation - Replace missing values\")\n", "imputer = imp.Imputer(dataset=manual_dataset, strategy='median', verbose=True)\n", "manual_dataset = imputer.transform()\n", "\n", "# Step 2: Duplicate detection\n", "print(\"\\n2. Duplicate Detection\")\n", "dup_detector = dd.Duplicate_detector(dataset=manual_dataset, strategy='drop_duplicates', verbose=True)\n", "manual_dataset = dup_detector.transform()\n", "\n", "# Step 3: Feature selection for text data\n", "print(\"\\n3. Feature Selection\")\n", "feat_selector = fs.Feature_selector(dataset=manual_dataset, strategy='WR', exclude='label', verbose=True)\n", "manual_dataset = feat_selector.transform()\n", "\n", "print(\"\\nManual preprocessing completed!\")\n", "print(f\"Final train shape: {manual_dataset['train'].shape}\")\n", "print(f\"Final test shape: {manual_dataset['test'].shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6) Classification with Manual Pipeline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test classification with manually cleaned data\n", "print(\"Testing classification with manually cleaned data...\")\n", "\n", "# Try different classifiers\n", "classifiers = ['CART', 'NB', 'LDA']\n", "\n", "for clf_name in classifiers:\n", "    try:\n", "        print(f\"\\nTesting {clf_name} classifier:\")\n", "        classifier = cl.Classifier(dataset=manual_dataset, goal=clf_name, target_goal='label', verbose=True)\n", "        result = classifier.transform()\n", "        print(f\"{clf_name} classification completed successfully\")\n", "    except Exception as e:\n", "        print(f\"Error with {clf_name}: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7) Automated Learn2Clean Pipeline\n", "\n", "Now let's use Learn2Clean's Q-learning approach to automatically find the best preprocessing pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add Learn2Clean to Python path (if not already done)\n", "import sys\n", "import os\n", "if '../python-package' not in sys.path:\n", "    sys.path.append(os.path.abspath('../python-package'))\n", "\n", "import learn2clean.qlearning.qlearner as ql\n", "\n", "# Create a fresh copy of the dataset for Learn2Clean\n", "l2c_dataset = anli_r1_encoded.copy()\n", "\n", "print(\"Starting Learn2Clean automated pipeline...\")\n", "print(\"This may take several minutes to find the optimal preprocessing sequence.\")\n", "\n", "# Learn2Clean for CART classification\n", "l2c_classification = ql.<PERSON><PERSON><PERSON>(\n", "    dataset=l2c_dataset,\n", "    goal='CART', \n", "    target_goal='label',\n", "    threshold=0.6, \n", "    target_prepare=None, \n", "    file_name='anli_r1_example', \n", "    verbose=False\n", ")\n", "\n", "# Run Learn2Clean optimization\n", "l2c_classification.learn2clean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8) Random Baseline Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare with random preprocessing pipeline\n", "random_dataset = anli_r1_encoded.copy()\n", "\n", "print(\"Running random preprocessing pipeline for comparison...\")\n", "\n", "# Random preprocessing pipeline for CART classification\n", "random_pipeline = ql.<PERSON><PERSON><PERSON>(\n", "    dataset=random_dataset,\n", "    goal='CART',\n", "    target_goal='label',\n", "    target_prepare=None, \n", "    verbose=False\n", ")\n", "\n", "try:\n", "    random_pipeline.random_cleaning('anli_r1_random_example')\n", "    print(\"Random pipeline completed successfully\")\n", "except Exception as e:\n", "    print(f\"Random pipeline error: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9) Results Analysis\n", "\n", "The results of Learn2Clean and random cleaning are stored in the 'save' directory as text files."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check if results files exist and display them\n", "import os\n", "\n", "results_files = [\n", "    'save/anli_r1_example_results.txt',\n", "    'save/anli_r1_random_example_results_file.txt'\n", "]\n", "\n", "for file_path in results_files:\n", "    if os.path.exists(file_path):\n", "        print(f\"\\n=== Results from {file_path} ===\")\n", "        with open(file_path, 'r') as f:\n", "            content = f.read()\n", "            print(content[-500:])  # Show last 500 characters\n", "    else:\n", "        print(f\"Results file not found: {file_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10) Applying Learned Preprocessing to Validation Data\n", "\n", "After Learn2Clean finds the optimal preprocessing pipeline on training data, we need to apply the same transformations to validation data for AutoGluon."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load validation data separately\n", "val_data = read_dataset('anli_r1_val')\n", "test_data = read_dataset('anli_r1_test')\n", "\n", "print(f\"Validation data shape: {val_data.shape}\")\n", "print(f\"Test data shape: {test_data.shape}\")\n", "\n", "# TODO: Apply the optimal preprocessing pipeline found by Learn2Clean\n", "# to validation and test data using the same transformations\n", "# (same imputation values, same normalization parameters, etc.)\n", "\n", "print(\"\\nFor AutoGluon training:\")\n", "print(\"1. Use the Learn2Clean optimized training data\")\n", "print(\"2. Apply the SAME preprocessing pipeline to validation data\")\n", "print(\"3. Keep test data completely separate until final evaluation\")\n", "print(\"4. This ensures no data leakage and valid model evaluation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook demonstrated how to apply Learn2Clean to the ANLI R1 dataset for natural language inference classification **while avoiding data leakage**. The key steps were:\n", "\n", "1. **Data Loading**: Loaded the ANLI R1 dataset and prepared it for text classification\n", "2. **Data Separation**: Kept train/validation/test splits separate to avoid data leakage\n", "3. **Profiling**: Used Learn2Clean's profiling capabilities to understand the data\n", "4. **Manual Pipeline**: Created a manual preprocessing pipeline with imputation, duplicate detection, and feature selection\n", "5. **Automated Pipeline**: Used Learn2Clean's Q-learning approach on TRAINING DATA ONLY\n", "6. **Comparison**: Compared Learn2Clean results with random preprocessing baselines\n", "7. **Validation Preparation**: Prepared to apply learned preprocessing to validation data\n", "\n", "**Critical for AutoGluon**: The preprocessing pipeline learned on training data must be applied to validation data using the same parameters (same imputation values, normalization statistics, etc.) to ensure valid evaluation and avoid data leakage."]}], "metadata": {"kernelspec": {"display_name": "learn2clean_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.17"}}, "nbformat": 4, "nbformat_minor": 4}