{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Learn2Clean Example: ScienceQA Dataset\n", "\n", "This notebook demonstrates how to apply Learn2Clean to the ScienceQA dataset, focusing on cleaning text features for multimodal question answering."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 0) Setup Learn2Clean Environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install Learn2Clean in development mode\n", "import os\n", "if os.path.exists('../python-package'):\n", "    %cd ../python-package\n", "    !pip install -e .\n", "    %cd ../examples\n", "else:\n", "    print(\"Learn2Clean python-package directory not found. Please check the path.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1) Dataset Loading and Preparation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load required libraries\n", "import pandas as pd\n", "import numpy as np\n", "from datasets import load_dataset\n", "from sklearn.model_selection import train_test_split\n", "import os\n", "\n", "def load_scienceqa_dataset():\n", "    \"\"\"Load and prepare ScienceQA dataset focusing on text features\"\"\"\n", "    print(\"Loading ScienceQA dataset...\")\n", "\n", "    try:\n", "        dataset = load_dataset(\"MothMalone/SLMS-KD-Benchmarks\", \"scienceqa\")\n", "\n", "        def prepare_scienceqa_data(split_data):\n", "            data = []\n", "            for item in split_data:\n", "                # Focus on text features only as requested\n", "                text_features = item['question']\n", "                if item['choices']:\n", "                    text_features += \" [CHOICES] \" + \" | \".join(item['choices'])\n", "                if item['hint']:\n", "                    text_features += \" [HINT] \" + item['hint']\n", "                if item['lecture']:\n", "                    text_features += \" [LECTURE] \" + item['lecture']\n", "\n", "                row = {\n", "                    'text': text_features,\n", "                    'question': item['question'],\n", "                    'task': item['task'],\n", "                    'grade': item['grade'],\n", "                    'subject': item['subject'],\n", "                    'topic': item['topic'],\n", "                    'category': item['category'],\n", "                    'answer': item['answer']\n", "                }\n", "                \n", "                # Add text length as a feature for cleaning\n", "                row['text_length'] = len(text_features)\n", "                row['question_length'] = len(item['question'])\n", "                row['has_choices'] = 1 if item['choices'] else 0\n", "                row['has_hint'] = 1 if item['hint'] else 0\n", "                row['has_lecture'] = 1 if item['lecture'] else 0\n", "                \n", "                data.append(row)\n", "            return pd.DataFrame(data)\n", "\n", "        # Use existing train/val/test splits if available\n", "        train_df = prepare_scienceqa_data(dataset['train'])\n", "        val_df = prepare_scienceqa_data(dataset['validation']) if 'validation' in dataset else None\n", "        test_df = prepare_scienceqa_data(dataset['test']) if 'test' in dataset else None\n", "\n", "        # If no validation/test splits, create them\n", "        if val_df is None or test_df is None:\n", "            print(\"Creating validation/test splits from train data\")\n", "            temp_df, test_df = train_test_split(train_df, test_size=0.2, random_state=42)\n", "            train_df, val_df = train_test_split(temp_df, test_size=0.25, random_state=42)  # 0.25 * 0.8 = 0.2\n", "\n", "        print(f\"ScienceQA loaded: Train={len(train_df)}, Val={len(val_df)}, Test={len(test_df)}\")\n", "        return train_df, val_df, test_df\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading ScienceQA: {e}\")\n", "        return None, None, None\n", "\n", "# Load the dataset\n", "train_df, val_df, test_df = load_scienceqa_dataset()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display basic information about the dataset\n", "if train_df is not None:\n", "    print(\"Dataset shape:\")\n", "    print(f\"Train: {train_df.shape}\")\n", "    print(f\"Validation: {val_df.shape}\")\n", "    print(f\"Test: {test_df.shape}\")\n", "    \n", "    print(\"\\nFirst few rows:\")\n", "    display(train_df.head())\n", "    \n", "    print(\"\\nAnswer distribution:\")\n", "    print(train_df['answer'].value_counts())\n", "    \n", "    print(\"\\nSubject distribution:\")\n", "    print(train_df['subject'].value_counts())\n", "    \n", "    print(\"\\nText feature statistics:\")\n", "    print(f\"Average text length: {train_df['text_length'].mean():.2f}\")\n", "    print(f\"Average question length: {train_df['question_length'].mean():.2f}\")\n", "    print(f\"Percentage with choices: {train_df['has_choices'].mean()*100:.1f}%\")\n", "    print(f\"Percentage with hints: {train_df['has_hint'].mean()*100:.1f}%\")\n", "    print(f\"Percentage with lectures: {train_df['has_lecture'].mean()*100:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2) Prepare Data for Learn2Clean\n", "\n", "Learn2Clean works with CSV files, so we need to save our data and create a reader function."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create datasets directory if it doesn't exist\n", "os.makedirs('../datasets/scienceqa', exist_ok=True)\n", "\n", "# Save datasets as CSV files - KEEP TRAIN AND VALIDATION SEPARATE!\n", "if train_df is not None:\n", "    # Save train, validation, and test separately to avoid data leakage\n", "    train_df.to_csv('../datasets/scienceqa/scienceqa_train.csv', index=False, encoding='utf-8')\n", "    val_df.to_csv('../datasets/scienceqa/scienceqa_val.csv', index=False, encoding='utf-8')\n", "    test_df.to_csv('../datasets/scienceqa/scienceqa_test.csv', index=False, encoding='utf-8')\n", "    \n", "    print(\"Datasets saved successfully!\")\n", "    print(f\"Train size: {len(train_df)}\")\n", "    print(f\"Validation size: {len(val_df)}\")\n", "    print(f\"Test size: {len(test_df)}\")\n", "    print(\"\\nIMPORTANT: Train/val/test kept separate to avoid data leakage for AutoGluon!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define dataset reader function for Learn2Clean\n", "def read_dataset(name):\n", "    \"\"\"Load datasets for Learn2Clean processing\"\"\"\n", "    import pandas as pd\n", "    if name == \"scienceqa\":\n", "        df = pd.read_csv('../datasets/scienceqa/scienceqa_train.csv', sep=',', encoding='utf-8')\n", "    elif name == \"scienceqa_test\":\n", "        df = pd.read_csv('../datasets/scienceqa/scienceqa_test.csv', sep=',', encoding='utf-8')\n", "    else: \n", "        raise ValueError('Invalid dataset name')               \n", "    return df\n", "\n", "# Test the reader function\n", "test_load = read_dataset(\"scienceqa\")\n", "print(f\"Loaded dataset shape: {test_load.shape}\")\n", "print(f\"Columns: {test_load.columns.tolist()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3) Data Profiling with Learn2Clean"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import learn2clean.loading.reader as rd \n", "import learn2clean.normalization.normalizer as nl \n", "import pandas as pd\n", "\n", "# Execute profiling function for ScienceQA dataset\n", "rd.profile_summary(read_dataset('scienceqa'), plot=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check the target variable\n", "scienceqa_data = read_dataset('scienceqa')\n", "print(\"Target variable (answer) distribution:\")\n", "print(scienceqa_data['answer'].value_counts())\n", "print(\"\\nTarget variable head:\")\n", "print(scienceqa_data['answer'].head())\n", "\n", "print(\"\\nText features analysis:\")\n", "print(f\"Text length statistics:\")\n", "print(scienceqa_data['text_length'].describe())\n", "print(f\"\\nQuestion length statistics:\")\n", "print(scienceqa_data['question_length'].describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4) Learn2Clean Data Processing\n", "\n", "Now we'll use Learn2Clean's Reader class to process the ScienceQA dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create Learn2Clean reader with encoding for classification\n", "d_enc = rd.Reader(sep=',', verbose=True, encoding=True) \n", "\n", "# Process ScienceQA dataset - ONLY TRAIN DATA for Learn2Clean optimization\n", "# This avoids data leakage by not using validation data in preprocessing decisions\n", "scienceqa_files = [\"../datasets/scienceqa/scienceqa_train.csv\"]\n", "scienceqa_encoded = d_enc.train_test_split(scienceqa_files, 'answer')\n", "\n", "print(\"\\nProcessed dataset structure (TRAIN ONLY):\")\n", "print(f\"Train shape: {scienceqa_encoded['train'].shape}\")\n", "print(f\"Target shape: {scienceqa_encoded['target'].shape}\")\n", "print(f\"Target name: {scienceqa_encoded['target'].name}\")\n", "print(\"\\nNote: Only training data used for Learn2Clean to avoid data leakage!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5) Manual Data Cleaning Pipeline for Text Features\n", "\n", "Let's create a manual preprocessing pipeline focusing on text feature cleaning."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import Learn2Clean modules for manual pipeline\n", "import learn2clean.loading.reader as rd \n", "import learn2clean.normalization.normalizer as nl \n", "import learn2clean.feature_selection.feature_selector as fs\n", "import learn2clean.duplicate_detection.duplicate_detector as dd\n", "import learn2clean.outlier_detection.outlier_detector as od\n", "import learn2clean.imputation.imputer as imp\n", "import learn2clean.classification.classifier as cl\n", "\n", "# Create a copy of the dataset for manual processing\n", "manual_dataset = scienceqa_encoded.copy()\n", "\n", "print(\"Starting manual preprocessing pipeline for text features...\")\n", "\n", "# Step 1: <PERSON>le missing values in text features\n", "print(\"\\n1. Imputation - Replace missing values\")\n", "imputer = imp.Imputer(dataset=manual_dataset, strategy='median', verbose=True)\n", "manual_dataset = imputer.transform()\n", "\n", "# Step 2: Outlier detection for text length features\n", "print(\"\\n2. Outlier Detection for text features\")\n", "outlier_detector = od.Outlier_detector(dataset=manual_dataset, strategy='LOF', verbose=True)\n", "manual_dataset = outlier_detector.transform()\n", "\n", "# Step 3: Duplicate detection\n", "print(\"\\n3. Duplicate Detection\")\n", "dup_detector = dd.Duplicate_detector(dataset=manual_dataset, strategy='drop_duplicates', verbose=True)\n", "manual_dataset = dup_detector.transform()\n", "\n", "# Step 4: Feature selection focusing on text-related features\n", "print(\"\\n4. Feature Selection for text features\")\n", "feat_selector = fs.Feature_selector(dataset=manual_dataset, strategy='WR', exclude='answer', verbose=True)\n", "manual_dataset = feat_selector.transform()\n", "\n", "# Step 5: Normalization of numerical text features\n", "print(\"\\n5. Normalization of text length features\")\n", "normalizer = nl.Normalizer(dataset=manual_dataset, strategy='standard', exclude='answer', verbose=True)\n", "manual_dataset = normalizer.transform()\n", "\n", "print(\"\\nManual preprocessing completed!\")\n", "print(f\"Final train shape: {manual_dataset['train'].shape}\")\n", "print(f\"Final test shape: {manual_dataset['test'].shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6) Classification with Manual Pipeline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test classification with manually cleaned data\n", "print(\"Testing classification with manually cleaned text features...\")\n", "\n", "# Try different classifiers suitable for multiclass classification\n", "classifiers = ['CART', 'NB', 'LDA']\n", "\n", "for clf_name in classifiers:\n", "    try:\n", "        print(f\"\\nTesting {clf_name} classifier:\")\n", "        classifier = cl.Classifier(dataset=manual_dataset, goal=clf_name, target_goal='answer', verbose=True)\n", "        result = classifier.transform()\n", "        print(f\"{clf_name} classification completed successfully\")\n", "    except Exception as e:\n", "        print(f\"Error with {clf_name}: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7) Automated Learn2Clean Pipeline\n", "\n", "Now let's use Learn2Clean's Q-learning approach to automatically find the best preprocessing pipeline for text features."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import learn2clean.qlearning.qlearner as ql\n", "\n", "# Create a fresh copy of the dataset for Learn2Clean\n", "l2c_dataset = scienceqa_encoded.copy()\n", "\n", "print(\"Starting Learn2Clean automated pipeline for text features...\")\n", "print(\"This may take several minutes to find the optimal preprocessing sequence.\")\n", "\n", "# Learn2Clean for CART classification\n", "l2c_classification = ql.<PERSON><PERSON><PERSON>(\n", "    dataset=l2c_dataset,\n", "    goal='CART', \n", "    target_goal='answer',\n", "    threshold=0.6, \n", "    target_prepare=None, \n", "    file_name='scienceqa_example', \n", "    verbose=False\n", ")\n", "\n", "# Run Learn2Clean optimization\n", "l2c_classification.learn2clean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8) Alternative Classifier Testing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Learn2Clean with different classifiers\n", "classifiers_to_test = ['NB', 'LDA']\n", "\n", "for clf in classifiers_to_test:\n", "    try:\n", "        print(f\"\\nTesting Learn2Clean with {clf} classifier...\")\n", "        l2c_alt = ql.<PERSON><PERSON><PERSON>(\n", "            dataset=scienceqa_encoded.copy(),\n", "            goal=clf,\n", "            target_goal='answer',\n", "            threshold=0.6,\n", "            target_prepare=None,\n", "            file_name=f'scienceqa_{clf.lower()}_example',\n", "            verbose=False\n", "        )\n", "        l2c_alt.learn2clean()\n", "        print(f\"Learn2Clean with {clf} completed\")\n", "    except Exception as e:\n", "        print(f\"Error with {clf}: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9) Random Baseline Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare with random preprocessing pipeline\n", "random_dataset = scienceqa_encoded.copy()\n", "\n", "print(\"Running random preprocessing pipeline for comparison...\")\n", "\n", "# Random preprocessing pipeline for CART classification\n", "random_pipeline = ql.<PERSON><PERSON><PERSON>(\n", "    dataset=random_dataset,\n", "    goal='CART',\n", "    target_goal='answer',\n", "    target_prepare=None, \n", "    verbose=False\n", ")\n", "\n", "try:\n", "    random_pipeline.random_cleaning('scienceqa_random_example')\n", "    print(\"Random pipeline completed successfully\")\n", "except Exception as e:\n", "    print(f\"Random pipeline error: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10) Results Analysis\n", "\n", "The results of Learn2Clean and random cleaning are stored in the 'save' directory as text files."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check if results files exist and display them\n", "import os\n", "\n", "results_files = [\n", "    'save/scienceqa_example_results.txt',\n", "    'save/scienceqa_nb_example_results.txt',\n", "    'save/scienceqa_lda_example_results.txt',\n", "    'save/scienceqa_random_example_results_file.txt'\n", "]\n", "\n", "for file_path in results_files:\n", "    if os.path.exists(file_path):\n", "        print(f\"\\n=== Results from {file_path} ===\")\n", "        with open(file_path, 'r') as f:\n", "            content = f.read()\n", "            print(content[-500:])  # Show last 500 characters\n", "    else:\n", "        print(f\"Results file not found: {file_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook demonstrated how to apply Learn2Clean to the ScienceQA dataset with a focus on cleaning text features. The key steps were:\n", "\n", "1. **Data Loading**: Loaded the ScienceQA dataset and prepared text features for analysis\n", "2. **Text Feature Engineering**: Created additional features like text length, presence of choices/hints/lectures\n", "3. **Data Preparation**: Converted the dataset to CSV format for Learn2Clean compatibility\n", "4. **Profiling**: Used Learn2Clean's profiling capabilities to understand text feature distributions\n", "5. **Manual Pipeline**: Created a comprehensive preprocessing pipeline focusing on text feature cleaning\n", "6. **Automated Pipeline**: Used Learn2Clean's Q-learning approach to automatically optimize text feature preprocessing\n", "7. **Multi-Classifier Testing**: Tested different classifiers (CART, NB, LDA) to find the best approach\n", "8. **Comparison**: Compared Learn2Clean results with random preprocessing baselines\n", "\n", "Learn2Clean automatically discovers the best sequence of data cleaning operations specifically for text features to maximize classification performance on the ScienceQA dataset. The focus on text features allows for targeted cleaning while preserving the multimodal nature of the original dataset."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}