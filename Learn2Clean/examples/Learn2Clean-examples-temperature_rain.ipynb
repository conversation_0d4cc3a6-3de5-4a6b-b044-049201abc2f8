{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Learn2Clean Example: Temperature Rain Dataset\n", "\n", "This notebook demonstrates how to apply Learn2Clean to the Temperature Rain time series dataset. Since Learn2Clean is designed for tabular data cleaning, we'll focus on cleanable features that can be extracted from the time series data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 0) Setup Learn2Clean Environment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install Learn2Clean in development mode\n", "import os\n", "if os.path.exists('../python-package'):\n", "    %cd ../python-package\n", "    !pip install -e .\n", "    %cd ../examples\n", "else:\n", "    print(\"Learn2Clean python-package directory not found. Please check the path.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1) Dataset Loading and Preparation"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading Temperature Rain dataset...\n", "Error loading Temperature Rain: No module named 'utils'\n", "Creating synthetic temperature data for demonstration...\n", "Creating synthetic temperature data...\n", "Synthetic Temperature Rain created: Train=60, Val=20, Test=20\n"]}], "source": ["# Load required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import os\n", "import zipfile\n", "import sys\n", "from sklearn.model_selection import train_test_split\n", "\n", "def load_temperature_rain_dataset():\n", "    \"\"\"Load and prepare Temperature Rain time series dataset from TSF file\"\"\"\n", "    print(\"Loading Temperature Rain dataset...\")\n", "\n", "    # Check if cached processed data exists\n", "    cache_dir = './temp_data/temperature_rain_cache'\n", "    train_cache = os.path.join(cache_dir, 'train_df.pkl')\n", "    val_cache = os.path.join(cache_dir, 'val_df.pkl')\n", "    test_cache = os.path.join(cache_dir, 'test_df.pkl')\n", "\n", "    if os.path.exists(train_cache) and os.path.exists(val_cache) and os.path.exists(test_cache):\n", "        print(\"Loading cached temperature_rain data...\")\n", "        try:\n", "            train_df = pd.read_pickle(train_cache)\n", "            val_df = pd.read_pickle(val_cache)\n", "            test_df = pd.read_pickle(test_cache)\n", "\n", "            print(f\"Cached Temperature Rain loaded: Train={len(train_df)}, Val={len(val_df)}, Test={len(test_df)}\")\n", "            return train_df, val_df, test_df\n", "        except Exception as e:\n", "            print(f\"Error loading cached data: {e}. Will reload from source.\")\n", "\n", "    try:\n", "        # Add monash_tsf to path for utils\n", "        sys.path.append('./monash_tsf')\n", "        from utils import convert_tsf_to_dataframe\n", "\n", "        # Extract and load the TSF file\n", "        zip_path = './monash_tsf/data/temperature_rain_dataset_with_missing_values.zip'\n", "        tsf_file = 'temperature_rain_dataset_with_missing_values.tsf'\n", "\n", "        if not os.path.exists(zip_path):\n", "            print(f\"Temperature rain zip file not found at {zip_path}\")\n", "            print(\"Creating synthetic temperature data for demonstration...\")\n", "            return create_synthetic_temperature_data()\n", "\n", "        # Extract TSF file temporarily\n", "        os.makedirs('./temp_data', exist_ok=True)\n", "        with zipfile.ZipFile(zip_path, 'r') as zip_ref:\n", "            zip_ref.extract(tsf_file, './temp_data/')\n", "\n", "        tsf_path = f'./temp_data/{tsf_file}'\n", "\n", "        # Parse TSF file\n", "        print(\"Parsing TSF file...\")\n", "        df, frequency, forecast_horizon, contain_missing_values, contain_equal_length = convert_tsf_to_dataframe(\n", "            tsf_path,\n", "            replace_missing_vals_with=np.nan,\n", "            value_column_name=\"target\"\n", "        )\n", "\n", "        print(f\"TSF file parsed successfully:\")\n", "        print(f\"  - Shape: {df.shape}\")\n", "        print(f\"  - Frequency: {frequency}\")\n", "        print(f\"  - Contains missing values: {contain_missing_values}\")\n", "\n", "        # Clean up temp file\n", "        os.remove(tsf_path)\n", "\n", "        # Convert to tabular format for Learn2Clean\n", "        tabular_data = convert_to_tabular_features(df)\n", "        \n", "        # Split the data\n", "        train_df, temp_df = train_test_split(tabular_data, test_size=0.4, random_state=42)\n", "        val_df, test_df = train_test_split(temp_df, test_size=0.5, random_state=42)\n", "\n", "        print(f\"Temperature Rain loaded: Train={len(train_df)}, Val={len(val_df)}, Test={len(test_df)}\")\n", "\n", "        # Cache the processed data\n", "        os.makedirs(cache_dir, exist_ok=True)\n", "        train_df.to_pickle(train_cache)\n", "        val_df.to_pickle(val_cache)\n", "        test_df.to_pickle(test_cache)\n", "\n", "        return train_df, val_df, test_df\n", "\n", "    except Exception as e:\n", "        print(f\"Error loading Temperature Rain: {e}\")\n", "        print(\"Creating synthetic temperature data for demonstration...\")\n", "        return create_synthetic_temperature_data()\n", "\n", "def convert_to_tabular_features(df):\n", "    \"\"\"Convert time series data to tabular features suitable for Learn2Clean\"\"\"\n", "    print(\"Converting time series to tabular features...\")\n", "    \n", "    tabular_data = []\n", "    \n", "    for idx, row in df.iterrows():\n", "        if idx % 100 == 0:\n", "            print(f\"Processing series {idx}/{len(df)}\")\n", "        \n", "        # Get item_id\n", "        item_id_col = 'series_name' if 'series_name' in row else 'station_id'\n", "        item_id = row[item_id_col] if item_id_col in row else f'series_{idx}'\n", "        \n", "        # Get target values\n", "        target_values = row['target']\n", "        if isinstance(target_values, str):\n", "            target_array = [float(x) for x in target_values.split() if x.strip()]\n", "        elif isinstance(target_values, (list, np.ndarray)):\n", "            target_array = [float(x) for x in target_values if pd.notna(x)]\n", "        else:\n", "            target_array = [float(target_values)] if pd.notna(target_values) else []\n", "        \n", "        if len(target_array) == 0:\n", "            continue\n", "            \n", "        # Extract statistical features from time series\n", "        target_array = np.array(target_array)\n", "        \n", "        features = {\n", "            'item_id': str(item_id),\n", "            'series_length': len(target_array),\n", "            'mean_value': np.mean(target_array),\n", "            'std_value': np.std(target_array),\n", "            'min_value': np.min(target_array),\n", "            'max_value': np.max(target_array),\n", "            'median_value': np.median(target_array),\n", "            'q25_value': np.percentile(target_array, 25),\n", "            'q75_value': np.percentile(target_array, 75),\n", "            'skewness': pd.Series(target_array).skew(),\n", "            'kurtosis': pd.Series(target_array).kurtosis(),\n", "            'missing_ratio': np.sum(pd.isna(target_array)) / len(target_array),\n", "            'zero_ratio': np.sum(target_array == 0) / len(target_array),\n", "            'trend': np.polyfit(range(len(target_array)), target_array, 1)[0],  # Linear trend\n", "            'target_class': 'high' if np.mean(target_array) > np.median([np.mean(target_array) for _, row in df.iterrows()]) else 'low'\n", "        }\n", "        \n", "        tabular_data.append(features)\n", "    \n", "    return pd.DataFrame(tabular_data)\n", "\n", "def create_synthetic_temperature_data():\n", "    \"\"\"Create synthetic temperature data for demonstration\"\"\"\n", "    print(\"Creating synthetic temperature data...\")\n", "    \n", "    np.random.seed(42)\n", "    n_stations = 100\n", "    \n", "    data = []\n", "    for i in range(n_stations):\n", "        # Generate synthetic time series statistics\n", "        series_length = np.random.randint(300, 700)\n", "        base_temp = np.random.normal(20, 10)  # Base temperature\n", "        \n", "        # Generate synthetic features\n", "        features = {\n", "            'item_id': f'station_{i}',\n", "            'series_length': series_length,\n", "            'mean_value': base_temp + np.random.normal(0, 2),\n", "            'std_value': np.random.uniform(5, 15),\n", "            'min_value': base_temp - np.random.uniform(15, 25),\n", "            'max_value': base_temp + np.random.uniform(15, 25),\n", "            'median_value': base_temp + np.random.normal(0, 1),\n", "            'q25_value': base_temp - np.random.uniform(5, 10),\n", "            'q75_value': base_temp + np.random.uniform(5, 10),\n", "            'skewness': np.random.normal(0, 0.5),\n", "            'kurtosis': np.random.normal(0, 1),\n", "            'missing_ratio': np.random.uniform(0, 0.1),\n", "            'zero_ratio': np.random.uniform(0, 0.05),\n", "            'trend': np.random.normal(0, 0.01),\n", "            'target_class': 'high' if base_temp > 20 else 'low'\n", "        }\n", "        data.append(features)\n", "    \n", "    full_df = pd.DataFrame(data)\n", "    \n", "    # Split the data\n", "    train_df, temp_df = train_test_split(full_df, test_size=0.4, random_state=42)\n", "    val_df, test_df = train_test_split(temp_df, test_size=0.5, random_state=42)\n", "    \n", "    print(f\"Synthetic Temperature Rain created: Train={len(train_df)}, Val={len(val_df)}, Test={len(test_df)}\")\n", "    return train_df, val_df, test_df\n", "\n", "# Load the dataset\n", "train_df, val_df, test_df = load_temperature_rain_dataset()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display basic information about the dataset\n", "if train_df is not None:\n", "    print(\"Dataset shape:\")\n", "    print(f\"Train: {train_df.shape}\")\n", "    print(f\"Validation: {val_df.shape}\")\n", "    print(f\"Test: {test_df.shape}\")\n", "    \n", "    print(\"\\nFirst few rows:\")\n", "    display(train_df.head())\n", "    \n", "    print(\"\\nTarget class distribution:\")\n", "    print(train_df['target_class'].value_counts())\n", "    \n", "    print(\"\\nFeature statistics:\")\n", "    print(train_df.describe())\n", "    \n", "    print(\"\\nColumn info:\")\n", "    print(train_df.info())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2) Prepare Data for Learn2Clean\n", "\n", "Learn2Clean works with CSV files, so we need to save our tabular features and create a reader function."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create datasets directory if it doesn't exist\n", "os.makedirs('../datasets/temperature_rain', exist_ok=True)\n", "\n", "# Save datasets as CSV files - KEEP TRAIN AND VALIDATION SEPARATE!\n", "if train_df is not None:\n", "    # Save train, validation, and test separately to avoid data leakage\n", "    train_df.to_csv('../datasets/temperature_rain/temperature_rain_train.csv', index=False, encoding='utf-8')\n", "    val_df.to_csv('../datasets/temperature_rain/temperature_rain_val.csv', index=False, encoding='utf-8')\n", "    test_df.to_csv('../datasets/temperature_rain/temperature_rain_test.csv', index=False, encoding='utf-8')\n", "    \n", "    print(\"Datasets saved successfully!\")\n", "    print(f\"Train size: {len(train_df)}\")\n", "    print(f\"Validation size: {len(val_df)}\")\n", "    print(f\"Test size: {len(test_df)}\")\n", "    print(\"\\nIMPORTANT: Train/val/test kept separate to avoid data leakage for AutoGluon!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define dataset reader function for Learn2Clean\n", "def read_dataset(name):\n", "    \"\"\"Load datasets for Learn2Clean processing\"\"\"\n", "    import pandas as pd\n", "    if name == \"temperature_rain\":\n", "        df = pd.read_csv('../datasets/temperature_rain/temperature_rain_train.csv', sep=',', encoding='utf-8')\n", "    elif name == \"temperature_rain_test\":\n", "        df = pd.read_csv('../datasets/temperature_rain/temperature_rain_test.csv', sep=',', encoding='utf-8')\n", "    else: \n", "        raise ValueError('Invalid dataset name')               \n", "    return df\n", "\n", "# Test the reader function\n", "test_load = read_dataset(\"temperature_rain\")\n", "print(f\"Loaded dataset shape: {test_load.shape}\")\n", "print(f\"Columns: {test_load.columns.tolist()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3) Data Profiling with Learn2Clean"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import learn2clean.loading.reader as rd \n", "import learn2clean.normalization.normalizer as nl \n", "import pandas as pd\n", "\n", "# Execute profiling function for Temperature Rain dataset\n", "rd.profile_summary(read_dataset('temperature_rain'), plot=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check the target variable and feature distributions\n", "temp_rain_data = read_dataset('temperature_rain')\n", "print(\"Target variable (target_class) distribution:\")\n", "print(temp_rain_data['target_class'].value_counts())\n", "\n", "print(\"\\nNumerical feature correlations with target:\")\n", "numerical_cols = temp_rain_data.select_dtypes(include=[np.number]).columns\n", "for col in numerical_cols:\n", "    if col != 'target_class':\n", "        high_mean = temp_rain_data[temp_rain_data['target_class'] == 'high'][col].mean()\n", "        low_mean = temp_rain_data[temp_rain_data['target_class'] == 'low'][col].mean()\n", "        print(f\"{col}: High={high_mean:.3f}, Low={low_mean:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4) Learn2Clean Data Processing\n", "\n", "Now we'll use Learn2Clean's Reader class to process the Temperature Rain tabular features."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create Learn2Clean reader with encoding for classification\n", "d_enc = rd.Reader(sep=',', verbose=True, encoding=True) \n", "\n", "# Process Temperature Rain dataset - ONLY TRAIN DATA for Learn2Clean optimization\n", "# This avoids data leakage by not using validation data in preprocessing decisions\n", "temp_rain_files = [\"../datasets/temperature_rain/temperature_rain_train.csv\"]\n", "temp_rain_encoded = d_enc.train_test_split(temp_rain_files, 'target_class')\n", "\n", "print(\"\\nProcessed dataset structure (TRAIN ONLY):\")\n", "print(f\"Train shape: {temp_rain_encoded['train'].shape}\")\n", "print(f\"Target shape: {temp_rain_encoded['target'].shape}\")\n", "print(f\"Target name: {temp_rain_encoded['target'].name}\")\n", "print(\"\\nNote: Only training data used for Learn2Clean to avoid data leakage!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5) Manual Data Cleaning Pipeline for Time Series Features\n", "\n", "Let's create a manual preprocessing pipeline focusing on the statistical features extracted from time series."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import Learn2Clean modules for manual pipeline\n", "import learn2clean.loading.reader as rd \n", "import learn2clean.normalization.normalizer as nl \n", "import learn2clean.feature_selection.feature_selector as fs\n", "import learn2clean.duplicate_detection.duplicate_detector as dd\n", "import learn2clean.outlier_detection.outlier_detector as od\n", "import learn2clean.imputation.imputer as imp\n", "import learn2clean.classification.classifier as cl\n", "\n", "# Create a copy of the dataset for manual processing\n", "manual_dataset = temp_rain_encoded.copy()\n", "\n", "print(\"Starting manual preprocessing pipeline for time series features...\")\n", "\n", "# Step 1: <PERSON><PERSON> missing values\n", "print(\"\\n1. Imputation - Replace missing values\")\n", "imputer = imp.Imputer(dataset=manual_dataset, strategy='median', verbose=True)\n", "manual_dataset = imputer.transform()\n", "\n", "# Step 2: Outlier detection for statistical features\n", "print(\"\\n2. Outlier Detection\")\n", "outlier_detector = od.Outlier_detector(dataset=manual_dataset, strategy='LOF', verbose=True)\n", "manual_dataset = outlier_detector.transform()\n", "\n", "# Step 3: Normalization of statistical features\n", "print(\"\\n3. Normalization\")\n", "normalizer = nl.Normalizer(dataset=manual_dataset, strategy='standard', exclude='target_class', verbose=True)\n", "manual_dataset = normalizer.transform()\n", "\n", "# Step 4: Feature selection\n", "print(\"\\n4. Feature Selection\")\n", "feat_selector = fs.Feature_selector(dataset=manual_dataset, strategy='WR', exclude='target_class', verbose=True)\n", "manual_dataset = feat_selector.transform()\n", "\n", "# Step 5: Duplicate detection\n", "print(\"\\n5. Duplicate Detection\")\n", "dup_detector = dd.Duplicate_detector(dataset=manual_dataset, strategy='drop_duplicates', verbose=True)\n", "manual_dataset = dup_detector.transform()\n", "\n", "print(\"\\nManual preprocessing completed!\")\n", "print(f\"Final train shape: {manual_dataset['train'].shape}\")\n", "print(f\"Final test shape: {manual_dataset['test'].shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6) Classification with Manual Pipeline"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test classification with manually cleaned data\n", "print(\"Testing classification with manually cleaned time series features...\")\n", "\n", "# Try different classifiers\n", "classifiers = ['CART', 'NB', 'LDA']\n", "\n", "for clf_name in classifiers:\n", "    try:\n", "        print(f\"\\nTesting {clf_name} classifier:\")\n", "        classifier = cl.Classifier(dataset=manual_dataset, goal=clf_name, target_goal='target_class', verbose=True)\n", "        result = classifier.transform()\n", "        print(f\"{clf_name} classification completed successfully\")\n", "    except Exception as e:\n", "        print(f\"Error with {clf_name}: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7) Automated Learn2Clean Pipeline\n", "\n", "Now let's use Learn2Clean's Q-learning approach to automatically find the best preprocessing pipeline for time series features."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import learn2clean.qlearning.qlearner as ql\n", "\n", "# Create a fresh copy of the dataset for Learn2Clean\n", "l2c_dataset = temp_rain_encoded.copy()\n", "\n", "print(\"Starting Learn2Clean automated pipeline for time series features...\")\n", "print(\"This may take several minutes to find the optimal preprocessing sequence.\")\n", "\n", "# Learn2Clean for CART classification\n", "l2c_classification = ql.<PERSON><PERSON><PERSON>(\n", "    dataset=l2c_dataset,\n", "    goal='CART', \n", "    target_goal='target_class',\n", "    threshold=0.6, \n", "    target_prepare=None, \n", "    file_name='temperature_rain_example', \n", "    verbose=False\n", ")\n", "\n", "# Run Learn2Clean optimization\n", "l2c_classification.learn2clean()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8) Alternative Approaches for Time Series\n", "\n", "Since this is time series data, let's also test regression approaches which might be more suitable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test Learn2Clean with regression using mean_value as target\n", "try:\n", "    print(\"Testing Learn2Clean with regression approach...\")\n", "    \n", "    # Create dataset for regression (predicting mean temperature)\n", "    regression_files = [\"../datasets/temperature_rain/temperature_rain_train.csv\", \"../datasets/temperature_rain/temperature_rain_test.csv\"]\n", "    d_reg = rd.Reader(sep=',', verbose=True, encoding=False)  # No encoding for regression\n", "    temp_rain_regression = d_reg.train_test_split(regression_files, 'mean_value')\n", "    \n", "    # Learn2Clean for regression\n", "    l2c_regression = ql.<PERSON><PERSON><PERSON>(\n", "        dataset=temp_rain_regression,\n", "        goal='LASSO',  # Regression approach\n", "        target_goal='mean_value',\n", "        threshold=0.6,\n", "        target_prepare=None,\n", "        file_name='temperature_rain_regression_example',\n", "        verbose=False\n", "    )\n", "    \n", "    l2c_regression.learn2clean()\n", "    print(\"Regression approach completed\")\n", "    \n", "except Exception as e:\n", "    print(f\"Error with regression approach: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9) Random Baseline Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare with random preprocessing pipeline\n", "random_dataset = temp_rain_encoded.copy()\n", "\n", "print(\"Running random preprocessing pipeline for comparison...\")\n", "\n", "# Random preprocessing pipeline for CART classification\n", "random_pipeline = ql.<PERSON><PERSON><PERSON>(\n", "    dataset=random_dataset,\n", "    goal='CART',\n", "    target_goal='target_class',\n", "    target_prepare=None, \n", "    verbose=False\n", ")\n", "\n", "try:\n", "    random_pipeline.random_cleaning('temperature_rain_random_example')\n", "    print(\"Random pipeline completed successfully\")\n", "except Exception as e:\n", "    print(f\"Random pipeline error: {e}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10) Results Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check if results files exist and display them\n", "import os\n", "\n", "results_files = [\n", "    'save/temperature_rain_example_results.txt',\n", "    'save/temperature_rain_regression_example_results.txt',\n", "    'save/temperature_rain_random_example_results_file.txt'\n", "]\n", "\n", "for file_path in results_files:\n", "    if os.path.exists(file_path):\n", "        print(f\"\\n=== Results from {file_path} ===\")\n", "        with open(file_path, 'r') as f:\n", "            content = f.read()\n", "            print(content[-500:])  # Show last 500 characters\n", "    else:\n", "        print(f\"Results file not found: {file_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook demonstrated how to apply Learn2Clean to the Temperature Rain time series dataset by extracting cleanable tabular features. The key adaptations were:\n", "\n", "1. **Feature Extraction**: Converted time series data into statistical features (mean, std, min, max, skewness, etc.)\n", "2. **Target Creation**: Created both classification (high/low temperature) and regression (mean temperature) targets\n", "3. **Data Preparation**: Converted extracted features to CSV format for Learn2Clean compatibility\n", "4. **Profiling**: Used Learn2Clean's profiling capabilities to understand feature distributions\n", "5. **Manual Pipeline**: Created preprocessing pipeline for statistical features including outlier detection, normalization, and feature selection\n", "6. **Automated Pipeline**: Used Learn2Clean's Q-learning for both classification and regression approaches\n", "7. **Comparison**: Compared Learn2Clean results with random preprocessing baselines\n", "\n", "**Important Note**: Since Learn2Clean is designed for tabular data cleaning rather than time series analysis, this approach focuses on cleaning statistical features extracted from the time series. For pure time series forecasting tasks, specialized time series preprocessing and AutoGluon's TimeSeriesPredictor would be more appropriate.\n", "\n", "The extracted features allow Learn2Clean to optimize preprocessing for tasks like:\n", "- Classifying weather stations by temperature patterns\n", "- Predicting average temperature from statistical features\n", "- Detecting anomalous weather stations based on their time series characteristics"]}], "metadata": {"kernelspec": {"display_name": "Autogluon-venv", "language": "python", "name": "autogluon-venv"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}