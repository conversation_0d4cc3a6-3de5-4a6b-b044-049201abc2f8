
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta charset="utf-8" />
    <title>Preprocessing &#8212; Python  documentation</title>
    <link rel="stylesheet" href="_static/alabaster.css" type="text/css" />
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    <script type="text/javascript" id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
    <script type="text/javascript" src="_static/jquery.js"></script>
    <script type="text/javascript" src="_static/underscore.js"></script>
    <script type="text/javascript" src="_static/doctools.js"></script>
    <script type="text/javascript" src="_static/language_data.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
    <link rel="next" title="Author" href="authors.html" />
    <link rel="prev" title="&lt;no title&gt;" href="installation.html" />
   
  <link rel="stylesheet" href="_static/custom.css" type="text/css" />
  
  
  <meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9" />

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <div class="section" id="preprocessing">
<h1>Preprocessing<a class="headerlink" href="#preprocessing" title="Permalink to this headline">¶</a></h1>
<div class="section" id="loading">
<h2>Loading<a class="headerlink" href="#loading" title="Permalink to this headline">¶</a></h2>
</div>
</div>
<div class="section" id="preparation">
<h1>Preparation<a class="headerlink" href="#preparation" title="Permalink to this headline">¶</a></h1>
<div class="section" id="normalization">
<h2>Normalization<a class="headerlink" href="#normalization" title="Permalink to this headline">¶</a></h2>
</div>
<div class="section" id="missing-values">
<h2>Missing values<a class="headerlink" href="#missing-values" title="Permalink to this headline">¶</a></h2>
</div>
<div class="section" id="feature-selection">
<h2>Feature Selection<a class="headerlink" href="#feature-selection" title="Permalink to this headline">¶</a></h2>
</div>
<div class="section" id="outlier-detection">
<h2>Outlier Detection<a class="headerlink" href="#outlier-detection" title="Permalink to this headline">¶</a></h2>
</div>
<div class="section" id="duplicate-detection">
<h2>Duplicate Detection<a class="headerlink" href="#duplicate-detection" title="Permalink to this headline">¶</a></h2>
</div>
<div class="section" id="consistency-checking">
<h2>Consistency Checking<a class="headerlink" href="#consistency-checking" title="Permalink to this headline">¶</a></h2>
</div>
</div>
<div class="section" id="models">
<h1>Models<a class="headerlink" href="#models" title="Permalink to this headline">¶</a></h1>
<div class="section" id="classification">
<h2>Classification<a class="headerlink" href="#classification" title="Permalink to this headline">¶</a></h2>
</div>
<div class="section" id="clustering">
<h2>Clustering<a class="headerlink" href="#clustering" title="Permalink to this headline">¶</a></h2>
</div>
<div class="section" id="regression">
<h2>Regression<a class="headerlink" href="#regression" title="Permalink to this headline">¶</a></h2>
</div>
</div>
<div class="section" id="qlearning">
<h1>Qlearning<a class="headerlink" href="#qlearning" title="Permalink to this headline">¶</a></h1>
</div>


          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="index.html">Python</a></h1>








<h3>Navigation</h3>
<p class="caption"><span class="caption-text">Features</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Preprocessing</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#loading">Loading</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="#preparation">Preparation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#normalization">Normalization</a></li>
<li class="toctree-l2"><a class="reference internal" href="#missing-values">Missing values</a></li>
<li class="toctree-l2"><a class="reference internal" href="#feature-selection">Feature Selection</a></li>
<li class="toctree-l2"><a class="reference internal" href="#outlier-detection">Outlier Detection</a></li>
<li class="toctree-l2"><a class="reference internal" href="#duplicate-detection">Duplicate Detection</a></li>
<li class="toctree-l2"><a class="reference internal" href="#consistency-checking">Consistency Checking</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="#models">Models</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#classification">Classification</a></li>
<li class="toctree-l2"><a class="reference internal" href="#clustering">Clustering</a></li>
<li class="toctree-l2"><a class="reference internal" href="#regression">Regression</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="#qlearning">Qlearning</a></li>
</ul>
<p class="caption"><span class="caption-text">Contribution</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="authors.html">Author</a></li>
</ul>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="index.html">Documentation overview</a><ul>
      <li>Previous: <a href="installation.html" title="previous chapter">&lt;no title&gt;</a></li>
      <li>Next: <a href="authors.html" title="next chapter">Author</a></li>
  </ul></li>
</ul>
</div>
<div id="searchbox" style="display: none" role="search">
  <h3>Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &copy;.
      
      |
      Powered by <a href="http://sphinx-doc.org/">Sphinx 2.0.0</a>
      &amp; <a href="https://github.com/bitprophet/alabaster">Alabaster 0.7.12</a>
      
      |
      <a href="_sources/features.rst.txt"
          rel="nofollow">Page source</a>
    </div>

    

    
  </body>
</html>