Preprocessing
=============

Loading
-------

.. automodule:: learn2clean.loading.reader
   :members:

Preparation
===========

Normalization
-------------

.. automodule:: learn2clean.normalization.normalizer
   :members:

Missing values
--------------

.. automodule:: learn2clean.imputation.imputer
   :members:

Feature Selection
-----------------

.. automodule:: learn2clean.feature_selection.feature_selector
   :members:

Outlier Detection
-----------------

.. automodule:: learn2clean.outlier_detection.outlier_detector

Duplicate Detection
-----------------

.. automodule:: learn2clean.duplicate_detection.duplicate_detector

Consistency Checking
--------------------

.. automodule:: learn2clean.consistency_checking.consistency_checker

Models
======

Classification
--------------

.. automodule:: learn2clean.classification.classifier
   :members:


Clustering
----------

.. automodule:: learn2clean.clustering.clusterer
   :members:

Regression
----------

.. automodule:: learn2clean.regression.regressor
   :members:


Qlearning
=========

.. automodule:: learn2clean.qlearning.qlearner
   :members:

