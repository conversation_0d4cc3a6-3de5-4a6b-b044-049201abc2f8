Search.setIndex({docnames:["authors","features","index","installation","learn2clean","learn2clean.classification","learn2clean.clustering","learn2clean.consistency_checking","learn2clean.duplicate_detection","learn2clean.feature_selection","learn2clean.imputation","learn2clean.loading","learn2clean.normalization","learn2clean.outlier_detection","learn2clean.qlearning","learn2clean.regression","modules"],envversion:{"sphinx.domains.c":1,"sphinx.domains.changeset":1,"sphinx.domains.cpp":1,"sphinx.domains.javascript":1,"sphinx.domains.math":2,"sphinx.domains.python":1,"sphinx.domains.rst":1,"sphinx.domains.std":1,sphinx:56},filenames:["authors.rst","features.rst","index.rst","installation.rst","learn2clean.rst","learn2clean.classification.rst","learn2clean.clustering.rst","learn2clean.consistency_checking.rst","learn2clean.duplicate_detection.rst","learn2clean.feature_selection.rst","learn2clean.imputation.rst","learn2clean.loading.rst","learn2clean.normalization.rst","learn2clean.outlier_detection.rst","learn2clean.qlearning.rst","learn2clean.regression.rst","modules.rst"],objects:{},objnames:{},objtypes:{},terms:{The:2,accept:2,approxim:2,articl:2,base:2,berti:[0,2],can:2,chain:2,check:2,citat:0,classif:[4,16],classifi:[2,4,16],clean:2,cluster:[4,16],collinear:2,com:0,conf:2,consist:2,consistency_check:[4,16],constraint:2,content:16,data:2,dataset:2,decim:2,dedupl:2,defin:2,detect:2,discoveri:2,distanc:2,duplic:2,duplicate_detect:[4,16],duplicate_detector:[4,16],email:0,equat:2,equil:[0,2],evalu:2,exact:2,expect:2,factor:2,featur:2,feature_select:[4,16],feature_selector:[4,16],francisco:2,free:2,frequent:2,given:2,googl:0,googlescholar:0,http:0,icd:2,imput:[2,4,16],inter:2,iqr:2,ird:0,jaccard:2,laur:[0,2],learn:2,librari:2,lif:0,linkedin:0,load:[4,16],local:2,lof:2,mai:2,manag:2,max:2,maxim:2,method:2,metric:2,mice:2,min:2,miss:2,model:2,modul:16,most:2,mrs:0,multipl:2,nearest:2,neighbour:2,normal:[2,4,16],optim:2,outlier:2,outlier_detect:[4,16],outlier_detector:[4,16],packag:16,pageperso:0,part:2,pattern:2,perform:2,phwxwjwaaaaj:0,pipelin:2,prepar:2,preperar:2,preprint:2,preprocess:2,proceed:2,python:2,qlearn:[4,16],qlearner:[4,16],qualiti:2,quartil:2,rang:2,ratio:2,reader:[4,16],regress:[4,16],regressor:[4,16],reinforc:2,remov:2,replac:2,repres:2,result:2,round:2,san:2,scale:2,scholar:0,score:2,select:2,sequenc:2,similar:2,submodul:[4,16],subpackag:16,subset:2,svc:2,task:2,techniqu:2,tree:2,trip:2,tutori:2,two:2,type:2,univ:0,used:2,user:[0,2],using:2,valu:2,variou:2,web:[0,2],welcom:0,wrapper:2,www:0,you:0,zsb:2},titles:["Author","Preprocessing","Welcome to Learn2Clean\u2019s documentation","&lt;no title&gt;","learn2clean package","learn2clean.classification package","learn2clean.clustering package","learn2clean.consistency_checking package","learn2clean.duplicate_detection package","learn2clean.feature_selection package","learn2clean.imputation package","learn2clean.loading package","learn2clean.normalization package","learn2clean.outlier_detection package","learn2clean.qlearning package","learn2clean.regression package","learn2clean"],titleterms:{author:0,check:1,classif:[1,5],classifi:5,cluster:[1,6],consist:1,consistency_check:7,content:[4,5,6,7,8,9,10,11,12,13,14,15],contributor:0,detect:1,develop:0,document:2,duplic:1,duplicate_detect:8,duplicate_detector:8,featur:1,feature_select:9,feature_selector:9,imput:10,lead:0,learn2clean:[2,4,5,6,7,8,9,10,11,12,13,14,15,16],link:2,load:[1,11],miss:1,model:1,modul:[4,5,6,7,8,9,10,11,12,13,14,15],normal:[1,12],outlier:1,outlier_detect:13,outlier_detector:13,packag:[4,5,6,7,8,9,10,11,12,13,14,15],prepar:1,preprocess:1,qlearn:[1,14],qlearner:14,reader:11,regress:[1,15],regressor:15,select:1,submodul:[5,6,7,8,9,10,11,12,13,14,15],subpackag:4,valu:1,welcom:2}})