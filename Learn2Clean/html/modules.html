
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta charset="utf-8" />
    <title>learn2clean &#8212; Python  documentation</title>
    <link rel="stylesheet" href="_static/alabaster.css" type="text/css" />
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    <script type="text/javascript" id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
    <script type="text/javascript" src="_static/jquery.js"></script>
    <script type="text/javascript" src="_static/underscore.js"></script>
    <script type="text/javascript" src="_static/doctools.js"></script>
    <script type="text/javascript" src="_static/language_data.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
   
  <link rel="stylesheet" href="_static/custom.css" type="text/css" />
  
  
  <meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9" />

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <div class="section" id="learn2clean">
<h1>learn2clean<a class="headerlink" href="#learn2clean" title="Permalink to this headline">¶</a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="learn2clean.html">learn2clean package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="learn2clean.html#subpackages">Subpackages</a><ul>
<li class="toctree-l3"><a class="reference internal" href="learn2clean.classification.html">learn2clean.classification package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.classification.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.classification.html#learn2clean-classification-classifier-module">learn2clean.classification.classifier module</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.classification.html#module-contents">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="learn2clean.clustering.html">learn2clean.clustering package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.clustering.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.clustering.html#learn2clean-clustering-clusterer-module">learn2clean.clustering.clusterer module</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.clustering.html#module-contents">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="learn2clean.consistency_checking.html">learn2clean.consistency_checking package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.consistency_checking.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.consistency_checking.html#learn2clean-consistency-checking-consistency-checker-module">learn2clean.consistency_checking.consistency_checker module</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.consistency_checking.html#module-contents">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="learn2clean.duplicate_detection.html">learn2clean.duplicate_detection package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.duplicate_detection.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.duplicate_detection.html#learn2clean-duplicate-detection-duplicate-detector-module">learn2clean.duplicate_detection.duplicate_detector module</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.duplicate_detection.html#module-contents">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="learn2clean.feature_selection.html">learn2clean.feature_selection package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.feature_selection.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.feature_selection.html#learn2clean-feature-selection-feature-selector-module">learn2clean.feature_selection.feature_selector module</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.feature_selection.html#module-contents">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="learn2clean.imputation.html">learn2clean.imputation package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.imputation.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.imputation.html#learn2clean-imputation-imputer-module">learn2clean.imputation.imputer module</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.imputation.html#module-contents">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="learn2clean.loading.html">learn2clean.loading package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.loading.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.loading.html#learn2clean-loading-reader-module">learn2clean.loading.reader module</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.loading.html#module-contents">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="learn2clean.normalization.html">learn2clean.normalization package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.normalization.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.normalization.html#learn2clean-normalization-normalizer-module">learn2clean.normalization.normalizer module</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.normalization.html#module-contents">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="learn2clean.outlier_detection.html">learn2clean.outlier_detection package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.outlier_detection.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.outlier_detection.html#learn2clean-outlier-detection-outlier-detector-module">learn2clean.outlier_detection.outlier_detector module</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.outlier_detection.html#module-contents">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="learn2clean.qlearning.html">learn2clean.qlearning package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.qlearning.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.qlearning.html#learn2clean-qlearning-qlearner-module">learn2clean.qlearning.qlearner module</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.qlearning.html#module-contents">Module contents</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="learn2clean.regression.html">learn2clean.regression package</a><ul>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.regression.html#submodules">Submodules</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.regression.html#learn2clean-regression-regressor-module">learn2clean.regression.regressor module</a></li>
<li class="toctree-l4"><a class="reference internal" href="learn2clean.regression.html#module-contents">Module contents</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="learn2clean.html#module-contents">Module contents</a></li>
</ul>
</li>
</ul>
</div>
</div>


          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="index.html">Python</a></h1>








<h3>Navigation</h3>
<p class="caption"><span class="caption-text">Features</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="features.html">Preprocessing</a></li>
<li class="toctree-l1"><a class="reference internal" href="features.html#preparation">Preparation</a></li>
<li class="toctree-l1"><a class="reference internal" href="features.html#models">Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="features.html#qlearning">Qlearning</a></li>
</ul>
<p class="caption"><span class="caption-text">Contribution</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="authors.html">Author</a></li>
</ul>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="index.html">Documentation overview</a><ul>
  </ul></li>
</ul>
</div>
<div id="searchbox" style="display: none" role="search">
  <h3>Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &copy;.
      
      |
      Powered by <a href="http://sphinx-doc.org/">Sphinx 2.0.0</a>
      &amp; <a href="https://github.com/bitprophet/alabaster">Alabaster 0.7.12</a>
      
      |
      <a href="_sources/modules.rst.txt"
          rel="nofollow">Page source</a>
    </div>

    

    
  </body>
</html>