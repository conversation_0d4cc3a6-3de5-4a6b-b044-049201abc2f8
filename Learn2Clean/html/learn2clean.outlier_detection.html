
<!DOCTYPE html>

<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta charset="utf-8" />
    <title>learn2clean.outlier_detection package &#8212; Python  documentation</title>
    <link rel="stylesheet" href="_static/alabaster.css" type="text/css" />
    <link rel="stylesheet" href="_static/pygments.css" type="text/css" />
    <script type="text/javascript" id="documentation_options" data-url_root="./" src="_static/documentation_options.js"></script>
    <script type="text/javascript" src="_static/jquery.js"></script>
    <script type="text/javascript" src="_static/underscore.js"></script>
    <script type="text/javascript" src="_static/doctools.js"></script>
    <script type="text/javascript" src="_static/language_data.js"></script>
    <link rel="index" title="Index" href="genindex.html" />
    <link rel="search" title="Search" href="search.html" />
   
  <link rel="stylesheet" href="_static/custom.css" type="text/css" />
  
  
  <meta name="viewport" content="width=device-width, initial-scale=0.9, maximum-scale=0.9" />

  </head><body>
  

    <div class="document">
      <div class="documentwrapper">
        <div class="bodywrapper">
          

          <div class="body" role="main">
            
  <div class="section" id="learn2clean-outlier-detection-package">
<h1>learn2clean.outlier_detection package<a class="headerlink" href="#learn2clean-outlier-detection-package" title="Permalink to this headline">¶</a></h1>
<div class="section" id="submodules">
<h2>Submodules<a class="headerlink" href="#submodules" title="Permalink to this headline">¶</a></h2>
</div>
<div class="section" id="learn2clean-outlier-detection-outlier-detector-module">
<h2>learn2clean.outlier_detection.outlier_detector module<a class="headerlink" href="#learn2clean-outlier-detection-outlier-detector-module" title="Permalink to this headline">¶</a></h2>
</div>
<div class="section" id="module-contents">
<h2>Module contents<a class="headerlink" href="#module-contents" title="Permalink to this headline">¶</a></h2>
</div>
</div>


          </div>
          
        </div>
      </div>
      <div class="sphinxsidebar" role="navigation" aria-label="main navigation">
        <div class="sphinxsidebarwrapper">
<h1 class="logo"><a href="index.html">Python</a></h1>








<h3>Navigation</h3>
<p class="caption"><span class="caption-text">Features</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="features.html">Preprocessing</a></li>
<li class="toctree-l1"><a class="reference internal" href="features.html#preparation">Preparation</a></li>
<li class="toctree-l1"><a class="reference internal" href="features.html#models">Models</a></li>
<li class="toctree-l1"><a class="reference internal" href="features.html#qlearning">Qlearning</a></li>
</ul>
<p class="caption"><span class="caption-text">Contribution</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="authors.html">Author</a></li>
</ul>

<div class="relations">
<h3>Related Topics</h3>
<ul>
  <li><a href="index.html">Documentation overview</a><ul>
  </ul></li>
</ul>
</div>
<div id="searchbox" style="display: none" role="search">
  <h3>Quick search</h3>
    <div class="searchformwrapper">
    <form class="search" action="search.html" method="get">
      <input type="text" name="q" />
      <input type="submit" value="Go" />
    </form>
    </div>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>








        </div>
      </div>
      <div class="clearer"></div>
    </div>
    <div class="footer">
      &copy;.
      
      |
      Powered by <a href="http://sphinx-doc.org/">Sphinx 2.0.0</a>
      &amp; <a href="https://github.com/bitprophet/alabaster">Alabaster 0.7.12</a>
      
      |
      <a href="_sources/learn2clean.outlier_detection.rst.txt"
          rel="nofollow">Page source</a>
    </div>

    

    
  </body>
</html>