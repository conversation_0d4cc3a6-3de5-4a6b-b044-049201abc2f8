[tox]
envlist = py36, flake8
skipsdist=true

[testenv:flake8]
basepython=python3.6
whitelist_externals =flake8
deps=flake8
commands=flake8 learn2clean

[testenv:py36]
whitelist_externals = python
setenv =
    PYTHONPATH = {toxinidir}/python-package

deps =
    -r{toxinidir}/python-package/requirements_dev.txt

sitepackages = True
commands =
    pip install -U pip
    pip install pytest
    ; py.test --basetemp={envtmpdir}
    python -m pytest

; If you want to make tox run the tests with the same versions, create a
; requirements.txt with the pinned versions and uncomment the following lines:
; deps =
;     -r{toxinidir}/python-package/requirements.txt