('gpsu_example', 'learn2clean', 'NB', 'Sentiment', None, 'ZS -> ED -> NB', 'accuracy', 0.6408668730650154, 4.58355188369751)
('gpsu_example', 'learn2clean', 'LDA', 'Sentiment', None, 'LOF -> LDA', 'accuracy', 0.6399448375104929, 3.942518949508667)
('gpsu_example', 'random', 'LASSO', 'Sentiment', None, 'LC -> LASSO', 'MSE', ({'quality_metric': 1.1305978472943574}, 0.07033896446228027))
('gpsu_example', 'random', 'OLS', 'Sentiment', None, 'WR -> IQR -> ED -> OLS', 'MSE', ({'quality_metric': 1072.304535723864}, 0.09471511840820312))
('gpsu_example', 'random', 'MARS', 'Sentiment', None, 'ZS -> LOF -> MARS', 'MSE', ({'quality_metric': 0.5225642524140152}, 4.01341700553894))
('gpsu_example', 'random', 'LASSO', 'Sentiment', None, 'ZS -> LC -> ZSB -> ED -> LASSO', 'MSE', ({'quality_metric': 2.1020031968512622}, 0.15211915969848633))
('gpsu_example', 'random', 'LDA', 'Sentiment', None, 'ZS -> LOF -> ED -> LDA', 'accuracy', ({'quality_metric': 0.9260500235960358}, 0.5381298065185547))
('gpsu_example', 'random', 'LDA', 'Sentiment', None, 'LC -> ZSB -> ED -> LDA', 'accuracy', ({'quality_metric': 0.9280734809232218}, 0.35216259956359863))
('gpsu_example', 'no-prep', 'CART', 'Sentiment', None, 'CART', 'accuracy', ({'quality_metric': 0.9987655795802971}, 163.2061710357666))
('gpsu_example', 'no-prep', 'LDA', 'Sentiment', None, 'LDA', 'accuracy', ({'quality_metric': 0.9286027157249234}, 0.21481084823608398))
('gpsu_example', 'no-prep', 'NB', 'Sentiment', None, 'NB', 'accuracy', ({'quality_metric': 0.9681439891689563}, 0.4411649703979492))
