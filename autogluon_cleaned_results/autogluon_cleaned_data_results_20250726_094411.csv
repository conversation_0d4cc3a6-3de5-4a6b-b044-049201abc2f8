dataset,best_model,test_score,accuracy,total_training_time_seconds,train_size,val_size,test_size,leaderboard,predictions_saved,confusion_matrix_saved
scienceqa,RandomForestEntr,0.40062843676355064,0.40062843676355064,410.2422664165497,5937,1272,1273,"{'model': {0: 'RandomForestEntr', 1: 'LightGBM', 2: 'RandomForestGini', 3: 'LightGBMLarge', 4: 'ExtraTreesGini', 5: 'ExtraTreesEntr', 6: 'WeightedEnsemble_L2', 7: 'KNeighborsUnif', 8: 'XGBoost', 9: 'NeuralNetTorch', 10: 'KNeighborsDist', 11: 'LightGBMXT', 12: 'CatBoost', 13: 'NeuralNetFastAI'}, 'score_test': {0: 0.42419481539670073, 1: 0.41633935585231735, 2: 0.41633935585231735, 3: 0.408483896307934, 4: 0.40455616653574233, 5: 0.40062843676355064, 6: 0.40062843676355064, 7: 0.399057344854674, 8: 0.396700706991359, 9: 0.39591516103692065, 10: 0.389630793401414, 11: 0.38727415553809896, 12: 0.3857030636292223, 13: 0.38334642576590733}, 'score_val': {0: 0.4294720252167061, 1: 0.4405043341213554, 2: 0.4247438928289992, 3: 0.42868400315208827, 4: 0.4294720252167061, 5: 0.4239558707643814, 6: 0.45311268715524033, 7: 0.41134751773049644, 8: 0.4397163120567376, 9: 0.425531914893617, 10: 0.40425531914893614, 11: 0.44838455476753347, 12: 0.43577620173364856, 13: 0.421591804570528}, 'eval_metric': {0: 'accuracy', 1: 'accuracy', 2: 'accuracy', 3: 'accuracy', 4: 'accuracy', 5: 'accuracy', 6: 'accuracy', 7: 'accuracy', 8: 'accuracy', 9: 'accuracy', 10: 'accuracy', 11: 'accuracy', 12: 'accuracy', 13: 'accuracy'}, 'pred_time_test': {0: 0.352783203125, 1: 0.030704021453857422, 2: 0.3793911933898926, 3: 0.15520930290222168, 4: 0.4007604122161865, 5: 0.43901944160461426, 6: 0.6173923015594482, 7: 0.029932022094726562, 8: 0.0632023811340332, 9: 0.2217111587524414, 10: 0.028167009353637695, 11: 0.12150454521179199, 12: 0.026546001434326172, 13: 0.16333436965942383}, 'pred_time_val': {0: 0.22107553482055664, 1: 0.017246007919311523, 2: 0.22532153129577637, 3: 0.08757185935974121, 4: 0.21329951286315918, 5: 0.22252821922302246, 6: 0.45382094383239746, 7: 0.031874895095825195, 8: 0.008445024490356445, 9: 0.2129216194152832, 10: 0.030730724334716797, 11: 0.09503984451293945, 12: 0.003703594207763672, 13: 0.1182248592376709}, 'fit_time': {0: 3.279442548751831, 1: 15.144277095794678, 2: 3.034372329711914, 3: 65.874760389328, 4: 1.7216312885284424, 5: 1.636340856552124, 6: 327.32657766342163, 7: 0.05509805679321289, 8: 11.031558752059937, 9: 213.60650730133057, 10: 0.027148008346557617, 11: 22.540053129196167, 12: 3.2550289630889893, 13: 64.61780214309692}, 'pred_time_test_marginal': {0: 0.352783203125, 1: 0.030704021453857422, 2: 0.3793911933898926, 3: 0.15520930290222168, 4: 0.4007604122161865, 5: 0.43901944160461426, 6: 0.01693582534790039, 7: 0.029932022094726562, 8: 0.0632023811340332, 9: 0.2217111587524414, 10: 0.028167009353637695, 11: 0.12150454521179199, 12: 0.026546001434326172, 13: 0.16333436965942383}, 'pred_time_val_marginal': {0: 0.22107553482055664, 1: 0.017246007919311523, 2: 0.22532153129577637, 3: 0.08757185935974121, 4: 0.21329951286315918, 5: 0.22252821922302246, 6: 0.0019435882568359375, 7: 0.031874895095825195, 8: 0.008445024490356445, 9: 0.2129216194152832, 10: 0.030730724334716797, 11: 0.09503984451293945, 12: 0.003703594207763672, 13: 0.1182248592376709}, 'fit_time_marginal': {0: 3.279442548751831, 1: 15.144277095794678, 2: 3.034372329711914, 3: 65.874760389328, 4: 1.7216312885284424, 5: 1.636340856552124, 6: 0.3863792419433594, 7: 0.05509805679321289, 8: 11.031558752059937, 9: 213.60650730133057, 10: 0.027148008346557617, 11: 22.540053129196167, 12: 3.2550289630889893, 13: 64.61780214309692}, 'stack_level': {0: 1, 1: 1, 2: 1, 3: 1, 4: 1, 5: 1, 6: 2, 7: 1, 8: 1, 9: 1, 10: 1, 11: 1, 12: 1, 13: 1}, 'can_infer': {0: True, 1: True, 2: True, 3: True, 4: True, 5: True, 6: True, 7: True, 8: True, 9: True, 10: True, 11: True, 12: True, 13: True}, 'fit_order': {0: 7, 1: 5, 2: 6, 3: 13, 4: 9, 5: 10, 6: 14, 7: 1, 8: 11, 9: 12, 10: 2, 11: 4, 12: 8, 13: 3}}",True,True
casehold,KNeighborsUnif,0.20342491531802784,0.20342491531802784,0.0,42509,5314,5314,"{'model': {0: 'KNeighborsUnif', 1: 'LightGBMXT', 2: 'LightGBM', 3: 'ExtraTreesGini', 4: 'RandomForestEntr', 5: 'RandomForestGini', 6: 'KNeighborsDist', 7: 'ExtraTreesEntr', 8: 'XGBoost', 9: 'WeightedEnsemble_L2', 10: 'CatBoost', 11: 'NeuralNetFastAI', 12: 'NeuralNetTorch'}, 'score_test': {0: 0.20850583364697026, 1: 0.20794128716597668, 2: 0.20775310500564548, 3: 0.20737674068498307, 4: 0.20700037636432067, 5: 0.20624764772299586, 6: 0.20587128340233346, 7: 0.20511855476100865, 8: 0.20436582611968385, 9: 0.20342491531802784, 10: 0.20286036883703426, 11: 0.20267218667670306, 12: 0.20079036507339104}, 'score_val': {0: 0.20436582611968385, 1: 0.21019947308995107, 2: 0.21321038765525027, 3: 0.20511855476100865, 4: 0.20850583364697026, 5: 0.20737674068498307, 6: 0.20154309371471585, 7: 0.20474219044034625, 8: 0.20587128340233346, 9: 0.2169740308618743, 10: 0.20624764772299586, 11: 0.2162213022205495, 12: 0.20605946556266466}, 'eval_metric': {0: 'accuracy', 1: 'accuracy', 2: 'accuracy', 3: 'accuracy', 4: 'accuracy', 5: 'accuracy', 6: 'accuracy', 7: 'accuracy', 8: 'accuracy', 9: 'accuracy', 10: 'accuracy', 11: 'accuracy', 12: 'accuracy'}, 'pred_time_test': {0: 0.060243844985961914, 1: 0.027439355850219727, 2: 0.011989116668701172, 3: 5.7635204792022705, 4: 5.676282167434692, 5: 5.635837078094482, 6: 0.04972577095031738, 7: 5.748661279678345, 8: 0.33864498138427734, 9: 0.8965358734130859, 10: 0.3075568675994873, 11: 0.8657436370849609, 12: 0.916724443435669}, 'pred_time_val': {0: 0.047995567321777344, 1: 0.017777442932128906, 2: 0.00623774528503418, 3: 0.4388258457183838, 4: 0.5742158889770508, 5: 0.5491499900817871, 6: 0.06292867660522461, 7: 0.44274115562438965, 8: 0.023567914962768555, 9: 1.24284029006958, 10: 0.0057032108306884766, 11: 1.2309982776641846, 12: 0.9528231620788574}, 'fit_time': {0: 0.1561870574951172, 1: 69.45124220848083, 2: 152.88102984428406, 3: 5.101625680923462, 4: 37.826359033584595, 5: 22.245096683502197, 6: 0.15108227729797363, 7: 5.953989028930664, 8: 97.01352453231812, 9: 1613.500163078308, 10: 3.4601070880889893, 11: 1459.3468778133392, 12: 1126.6064188480377}, 'pred_time_test_marginal': {0: 0.060243844985961914, 1: 0.027439355850219727, 2: 0.011989116668701172, 3: 5.7635204792022705, 4: 5.676282167434692, 5: 5.635837078094482, 6: 0.04972577095031738, 7: 5.748661279678345, 8: 0.33864498138427734, 9: 0.018803119659423828, 10: 0.3075568675994873, 11: 0.8657436370849609, 12: 0.916724443435669}, 'pred_time_val_marginal': {0: 0.047995567321777344, 1: 0.017777442932128906, 2: 0.00623774528503418, 3: 0.4388258457183838, 4: 0.5742158889770508, 5: 0.5491499900817871, 6: 0.06292867660522461, 7: 0.44274115562438965, 8: 0.023567914962768555, 9: 0.005604267120361328, 10: 0.0057032108306884766, 11: 1.2309982776641846, 12: 0.9528231620788574}, 'fit_time_marginal': {0: 0.1561870574951172, 1: 69.45124220848083, 2: 152.88102984428406, 3: 5.101625680923462, 4: 37.826359033584595, 5: 22.245096683502197, 6: 0.15108227729797363, 7: 5.953989028930664, 8: 97.01352453231812, 9: 1.2722554206848145, 10: 3.4601070880889893, 11: 1459.3468778133392, 12: 1126.6064188480377}, 'stack_level': {0: 1, 1: 1, 2: 1, 3: 1, 4: 1, 5: 1, 6: 1, 7: 1, 8: 1, 9: 2, 10: 1, 11: 1, 12: 1}, 'can_infer': {0: True, 1: True, 2: True, 3: True, 4: True, 5: True, 6: True, 7: True, 8: True, 9: True, 10: True, 11: True, 12: True}, 'fit_order': {0: 1, 1: 4, 2: 5, 3: 9, 4: 7, 5: 6, 6: 2, 7: 10, 8: 11, 9: 13, 10: 8, 11: 3, 12: 12}}",True,True
anli_r1,NeuralNetTorch,0.339,0.339,424.5404534339905,16946,1000,1000,"{'model': {0: 'NeuralNetTorch', 1: 'WeightedEnsemble_L2', 2: 'LightGBMXT', 3: 'CatBoost', 4: 'LightGBMLarge', 5: 'LightGBM', 6: 'NeuralNetFastAI', 7: 'XGBoost', 8: 'KNeighborsDist', 9: 'ExtraTreesGini', 10: 'ExtraTreesEntr', 11: 'RandomForestEntr', 12: 'KNeighborsUnif', 13: 'RandomForestGini'}, 'score_test': {0: 0.345, 1: 0.339, 2: 0.337, 3: 0.336, 4: 0.334, 5: 0.331, 6: 0.329, 7: 0.317, 8: 0.311, 9: 0.305, 10: 0.301, 11: 0.3, 12: 0.296, 13: 0.29}, 'score_val': {0: 0.336, 1: 0.344, 2: 0.341, 3: 0.342, 4: 0.334, 5: 0.335, 6: 0.342, 7: 0.334, 8: 0.302, 9: 0.299, 10: 0.309, 11: 0.308, 12: 0.299, 13: 0.299}, 'eval_metric': {0: 'accuracy', 1: 'accuracy', 2: 'accuracy', 3: 'accuracy', 4: 'accuracy', 5: 'accuracy', 6: 'accuracy', 7: 'accuracy', 8: 'accuracy', 9: 'accuracy', 10: 'accuracy', 11: 'accuracy', 12: 'accuracy', 13: 'accuracy'}, 'pred_time_test': {0: 0.16300034523010254, 1: 0.3442080020904541, 2: 0.015198469161987305, 3: 0.04885435104370117, 4: 0.012833595275878906, 5: 0.01171731948852539, 6: 0.12810468673706055, 7: 0.07761049270629883, 8: 0.028049468994140625, 9: 0.6555914878845215, 10: 0.7186951637268066, 11: 0.5102124214172363, 12: 0.03040289878845215, 13: 0.36133742332458496}, 'pred_time_val': {0: 0.01437997817993164, 1: 0.11646151542663574, 2: 0.010181188583374023, 3: 0.00339508056640625, 4: 0.006000518798828125, 5: 0.007329225540161133, 6: 0.09690022468566895, 7: 0.006562948226928711, 8: 0.029277563095092773, 9: 0.18065881729125977, 10: 0.2204587459564209, 11: 0.17278599739074707, 12: 0.02781510353088379, 13: 0.20395135879516602}, 'fit_time': {0: 179.3029341697693, 1: 334.41747307777405, 2: 11.496811628341675, 3: 1.5111753940582275, 4: 28.33903193473816, 5: 17.806851625442505, 6: 153.2463459968567, 7: 7.2728190422058105, 8: 0.06900978088378906, 9: 2.072967052459717, 10: 2.2347540855407715, 11: 10.273837566375732, 12: 0.06668472290039062, 13: 7.449227571487427}, 'pred_time_test_marginal': {0: 0.16300034523010254, 1: 0.004248619079589844, 2: 0.015198469161987305, 3: 0.04885435104370117, 4: 0.012833595275878906, 5: 0.01171731948852539, 6: 0.12810468673706055, 7: 0.07761049270629883, 8: 0.028049468994140625, 9: 0.6555914878845215, 10: 0.7186951637268066, 11: 0.5102124214172363, 12: 0.03040289878845215, 13: 0.36133742332458496}, 'pred_time_val_marginal': {0: 0.01437997817993164, 1: 0.0017862319946289062, 2: 0.010181188583374023, 3: 0.00339508056640625, 4: 0.006000518798828125, 5: 0.007329225540161133, 6: 0.09690022468566895, 7: 0.006562948226928711, 8: 0.029277563095092773, 9: 0.18065881729125977, 10: 0.2204587459564209, 11: 0.17278599739074707, 12: 0.02781510353088379, 13: 0.20395135879516602}, 'fit_time_marginal': {0: 179.3029341697693, 1: 0.35701751708984375, 2: 11.496811628341675, 3: 1.5111753940582275, 4: 28.33903193473816, 5: 17.806851625442505, 6: 153.2463459968567, 7: 7.2728190422058105, 8: 0.06900978088378906, 9: 2.072967052459717, 10: 2.2347540855407715, 11: 10.273837566375732, 12: 0.06668472290039062, 13: 7.449227571487427}, 'stack_level': {0: 1, 1: 2, 2: 1, 3: 1, 4: 1, 5: 1, 6: 1, 7: 1, 8: 1, 9: 1, 10: 1, 11: 1, 12: 1, 13: 1}, 'can_infer': {0: True, 1: True, 2: True, 3: True, 4: True, 5: True, 6: True, 7: True, 8: True, 9: True, 10: True, 11: True, 12: True, 13: True}, 'fit_order': {0: 12, 1: 14, 2: 4, 3: 8, 4: 13, 5: 5, 6: 3, 7: 11, 8: 2, 9: 9, 10: 10, 11: 7, 12: 1, 13: 6}}",True,True
