dataset,best_model,test_score,accuracy,total_training_time_seconds,train_size,val_size,test_size,leaderboard,predictions_saved,confusion_matrix_saved
casehold,KNeighborsUnif,0.20342491531802784,0.20342491531802784,0,42509,5314,5314,"{'model': {0: 'KNeighborsUnif', 1: 'LightGBMXT', 2: 'LightGBM', 3: 'ExtraTreesGini', 4: 'RandomForestEntr', 5: 'RandomForestGini', 6: 'KNeighborsDist', 7: 'ExtraTreesEntr', 8: 'XGBoost', 9: 'WeightedEnsemble_L2', 10: 'CatBoost', 11: 'NeuralNetFastAI', 12: 'NeuralNetTorch'}, 'score_test': {0: 0.20850583364697026, 1: 0.20794128716597668, 2: 0.20775310500564548, 3: 0.20737674068498307, 4: 0.20700037636432067, 5: 0.20624764772299586, 6: 0.20587128340233346, 7: 0.20511855476100865, 8: 0.20436582611968385, 9: 0.20342491531802784, 10: 0.20286036883703426, 11: 0.20267218667670306, 12: 0.20079036507339104}, 'score_val': {0: 0.20436582611968385, 1: 0.21019947308995107, 2: 0.21321038765525027, 3: 0.20511855476100865, 4: 0.20850583364697026, 5: 0.20737674068498307, 6: 0.20154309371471585, 7: 0.20474219044034625, 8: 0.20587128340233346, 9: 0.2169740308618743, 10: 0.20624764772299586, 11: 0.2162213022205495, 12: 0.20605946556266466}, 'eval_metric': {0: 'accuracy', 1: 'accuracy', 2: 'accuracy', 3: 'accuracy', 4: 'accuracy', 5: 'accuracy', 6: 'accuracy', 7: 'accuracy', 8: 'accuracy', 9: 'accuracy', 10: 'accuracy', 11: 'accuracy', 12: 'accuracy'}, 'pred_time_test': {0: 0.05612826347351074, 1: 0.20843148231506348, 2: 0.014377832412719727, 3: 0.9977133274078369, 4: 2.0822842121124268, 5: 2.9749886989593506, 6: 0.047096967697143555, 7: 2.0804812908172607, 8: 0.4753563404083252, 9: 7.143906593322754, 10: 0.8493020534515381, 11: 7.1222453117370605, 12: 1.2977211475372314}, 'pred_time_val': {0: 0.047995567321777344, 1: 0.017777442932128906, 2: 0.00623774528503418, 3: 0.4388258457183838, 4: 0.5742158889770508, 5: 0.5491499900817871, 6: 0.06292867660522461, 7: 0.44274115562438965, 8: 0.023567914962768555, 9: 1.24284029006958, 10: 0.0057032108306884766, 11: 1.2309982776641846, 12: 0.9528231620788574}, 'fit_time': {0: 0.1561870574951172, 1: 69.45124220848083, 2: 152.88102984428406, 3: 5.101625680923462, 4: 37.826359033584595, 5: 22.245096683502197, 6: 0.15108227729797363, 7: 5.953989028930664, 8: 97.01352453231812, 9: 1613.500163078308, 10: 3.4601070880889893, 11: 1459.3468778133392, 12: 1126.6064188480377}, 'pred_time_test_marginal': {0: 0.05612826347351074, 1: 0.20843148231506348, 2: 0.014377832412719727, 3: 0.9977133274078369, 4: 2.0822842121124268, 5: 2.9749886989593506, 6: 0.047096967697143555, 7: 2.0804812908172607, 8: 0.4753563404083252, 9: 0.007283449172973633, 10: 0.8493020534515381, 11: 7.1222453117370605, 12: 1.2977211475372314}, 'pred_time_val_marginal': {0: 0.047995567321777344, 1: 0.017777442932128906, 2: 0.00623774528503418, 3: 0.4388258457183838, 4: 0.5742158889770508, 5: 0.5491499900817871, 6: 0.06292867660522461, 7: 0.44274115562438965, 8: 0.023567914962768555, 9: 0.005604267120361328, 10: 0.0057032108306884766, 11: 1.2309982776641846, 12: 0.9528231620788574}, 'fit_time_marginal': {0: 0.1561870574951172, 1: 69.45124220848083, 2: 152.88102984428406, 3: 5.101625680923462, 4: 37.826359033584595, 5: 22.245096683502197, 6: 0.15108227729797363, 7: 5.953989028930664, 8: 97.01352453231812, 9: 1.2722554206848145, 10: 3.4601070880889893, 11: 1459.3468778133392, 12: 1126.6064188480377}, 'stack_level': {0: 1, 1: 1, 2: 1, 3: 1, 4: 1, 5: 1, 6: 1, 7: 1, 8: 1, 9: 2, 10: 1, 11: 1, 12: 1}, 'can_infer': {0: True, 1: True, 2: True, 3: True, 4: True, 5: True, 6: True, 7: True, 8: True, 9: True, 10: True, 11: True, 12: True}, 'fit_order': {0: 1, 1: 4, 2: 5, 3: 9, 4: 7, 5: 6, 6: 2, 7: 10, 8: 11, 9: 13, 10: 8, 11: 3, 12: 12}}",True,True
