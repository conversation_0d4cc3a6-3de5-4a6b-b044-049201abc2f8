2025-07-29 08:43:56,286 - __main__ - INFO - Initialized AutoGluonEmbeddingsEvaluator
2025-07-29 08:43:56,286 - __main__ - INFO - Output directory: autogluon_embeddings_results/run_20250729_084356
2025-07-29 08:43:56,286 - __main__ - INFO - Time limit per model: 3000 seconds
2025-07-29 08:43:56,286 - __main__ - INFO - Using preset: medium_quality
2025-07-29 08:43:56,286 - __main__ - INFO - Starting evaluation for embeddings_dataset using embeddings from /storage/nammt/autogluon/embeddings_full.joblib
2025-07-29 08:43:56,286 - __main__ - INFO - Loading embeddings from /storage/nammt/autogluon/embeddings_full.joblib
2025-07-29 08:43:56,332 - __main__ - INFO - Successfully loaded embeddings as (train, val, test) tuple
2025-07-29 08:43:56,333 - __main__ - INFO - Train embeddings shape: (42509, 384)
2025-07-29 08:43:56,333 - __main__ - INFO - Val embeddings shape: (5314, 384)
2025-07-29 08:43:56,333 - __main__ - INFO - Test embeddings shape: (5314, 384)
2025-07-29 08:43:56,333 - __main__ - WARNING - Generating synthetic labels as no labels were provided
2025-07-29 08:44:13,617 - __main__ - INFO - Generated synthetic labels with 5 classes
2025-07-29 08:44:13,617 - __main__ - INFO - Preparing DataFrames for AutoGluon
2025-07-29 08:44:13,623 - __main__ - INFO - Created DataFrames with shapes: train=(42509, 385), val=(5314, 385), test=(5314, 385)
2025-07-29 08:44:13,624 - __main__ - INFO - Training AutoGluon model for embeddings_dataset (embeddings)...
2025-07-29 08:46:14,688 - __main__ - INFO - Initialized AutoGluonEmbeddingsEvaluator
2025-07-29 08:46:14,689 - __main__ - INFO - Output directory: autogluon_embeddings_results/run_20250729_084614
2025-07-29 08:46:14,689 - __main__ - INFO - Time limit per model: 3000 seconds
2025-07-29 08:46:14,689 - __main__ - INFO - Using preset: medium_quality
2025-07-29 08:46:14,689 - __main__ - INFO - Starting evaluation for embeddings_dataset using embeddings from /storage/nammt/autogluon/embeddings_full.joblib
2025-07-29 08:46:14,689 - __main__ - INFO - Loading embeddings from /storage/nammt/autogluon/embeddings_full.joblib
2025-07-29 08:46:14,734 - __main__ - INFO - Successfully loaded embeddings as (X_train, X_val, X_test) tuple
2025-07-29 08:46:14,734 - __main__ - INFO - Train embeddings shape: (42509, 384)
2025-07-29 08:46:14,734 - __main__ - INFO - Val embeddings shape: (5314, 384)
2025-07-29 08:46:14,734 - __main__ - INFO - Test embeddings shape: (5314, 384)
2025-07-29 08:46:14,735 - __main__ - WARNING - Generating synthetic labels as no labels were provided
2025-07-29 08:48:18,415 - __main__ - INFO - Initialized AutoGluonEmbeddingsEvaluator
2025-07-29 08:48:18,416 - __main__ - INFO - Output directory: autogluon_embeddings_results/run_20250729_084818
2025-07-29 08:48:18,416 - __main__ - INFO - Time limit per model: 3000 seconds
2025-07-29 08:48:18,416 - __main__ - INFO - Using preset: medium_quality
2025-07-29 08:48:18,416 - __main__ - INFO - Starting evaluation for embeddings_dataset using embeddings from /storage/nammt/autogluon/embeddings_full.joblib
2025-07-29 08:48:18,416 - __main__ - INFO - Loading embeddings from /storage/nammt/autogluon/embeddings_full.joblib
2025-07-29 08:48:18,461 - __main__ - INFO - Successfully loaded embeddings as (X_train, X_val, X_test) tuple
2025-07-29 08:48:18,461 - __main__ - INFO - Train embeddings shape: (42509, 384)
2025-07-29 08:48:18,462 - __main__ - INFO - Val embeddings shape: (5314, 384)
2025-07-29 08:48:18,462 - __main__ - INFO - Test embeddings shape: (5314, 384)
2025-07-29 08:48:18,462 - __main__ - INFO - DEBUG: Train embeddings appear to be a structured array with fields: None
2025-07-29 08:48:18,462 - __main__ - ERROR - Error loading embeddings: 'NoneType' object is not iterable
2025-07-29 08:48:18,463 - __main__ - ERROR - Failed to load embeddings properly. Exiting.
2025-07-29 08:53:06,829 - __main__ - INFO - Initialized AutoGluonEmbeddingsEvaluator
2025-07-29 08:53:06,829 - __main__ - INFO - Output directory: autogluon_embeddings_results/run_20250729_085306
2025-07-29 08:53:06,829 - __main__ - INFO - Time limit per model: 3000 seconds
2025-07-29 08:53:06,829 - __main__ - INFO - Using preset: medium_quality
2025-07-29 08:53:06,829 - __main__ - INFO - Starting evaluation for embeddings_dataset using embeddings from /storage/nammt/autogluon/embeddings_full.joblib
2025-07-29 08:53:06,829 - __main__ - INFO - Loading embeddings from /storage/nammt/autogluon/embeddings_full.joblib
2025-07-29 08:53:06,875 - __main__ - INFO - DEBUG: Loaded data type: <class 'tuple'>
2025-07-29 08:53:06,875 - __main__ - INFO - DEBUG: Tuple length: 3
2025-07-29 08:53:06,876 - __main__ - INFO - DEBUG: Item 0 type: <class 'numpy.ndarray'>, shape: (42509, 384)
2025-07-29 08:53:06,876 - __main__ - INFO - DEBUG: Item 1 type: <class 'numpy.ndarray'>, shape: (5314, 384)
2025-07-29 08:53:06,876 - __main__ - INFO - DEBUG: Item 2 type: <class 'numpy.ndarray'>, shape: (5314, 384)
2025-07-29 08:53:06,876 - __main__ - INFO - Successfully loaded embeddings as (X_train, X_val, X_test) tuple
2025-07-29 08:53:06,876 - __main__ - INFO - Train embeddings shape: (42509, 384)
2025-07-29 08:53:06,876 - __main__ - INFO - Val embeddings shape: (5314, 384)
2025-07-29 08:53:06,876 - __main__ - INFO - Test embeddings shape: (5314, 384)
2025-07-29 08:53:06,876 - __main__ - INFO - DEBUG: Train embeddings type: <class 'numpy.ndarray'>
2025-07-29 08:53:06,876 - __main__ - INFO - DEBUG: NumPy array with shape (42509, 384)
2025-07-29 08:53:06,877 - __main__ - INFO - DEBUG: First row sample: [ 0.02741967  0.071001   -0.11623035  0.0128611   0.00438468]
2025-07-29 08:53:06,877 - __main__ - INFO - DEBUG: Train embeddings type: <class 'numpy.ndarray'>, no label fields detected
2025-07-29 08:53:06,877 - __main__ - WARNING - Generating synthetic labels as no labels were provided
2025-07-29 08:53:20,419 - __main__ - INFO - Generated synthetic labels with 5 classes
2025-07-29 08:53:20,420 - __main__ - INFO - Preparing DataFrames for AutoGluon
2025-07-29 08:53:20,424 - __main__ - INFO - Created DataFrames with shapes: train=(42509, 385), val=(5314, 385), test=(5314, 385)
2025-07-29 08:53:20,424 - __main__ - INFO - Training AutoGluon model for embeddings_dataset (embeddings)...
2025-07-29 09:00:50,768 - __main__ - INFO - Initialized AutoGluonEmbeddingsEvaluator
2025-07-29 09:00:50,768 - __main__ - INFO - Output directory: autogluon_embeddings_results/run_20250729_090050
2025-07-29 09:00:50,768 - __main__ - INFO - Time limit per model: 3000 seconds
2025-07-29 09:00:50,768 - __main__ - INFO - Using preset: medium_quality
2025-07-29 09:00:50,768 - __main__ - INFO - Starting evaluation for casehold_full using embeddings from /storage/nammt/autogluon/embeddings_full.joblib
2025-07-29 09:00:50,769 - __main__ - INFO - Loading embeddings from /storage/nammt/autogluon/embeddings_full.joblib
2025-07-29 09:00:50,813 - __main__ - INFO - DEBUG: Loaded data type: <class 'tuple'>
2025-07-29 09:00:50,813 - __main__ - INFO - DEBUG: Tuple length: 3
2025-07-29 09:00:50,813 - __main__ - INFO - DEBUG: Item 0 type: <class 'numpy.ndarray'>, shape: (42509, 384)
2025-07-29 09:00:50,813 - __main__ - INFO - DEBUG: Item 1 type: <class 'numpy.ndarray'>, shape: (5314, 384)
2025-07-29 09:00:50,814 - __main__ - INFO - DEBUG: Item 2 type: <class 'numpy.ndarray'>, shape: (5314, 384)
2025-07-29 09:00:50,814 - __main__ - INFO - Successfully loaded embeddings as (X_train, X_val, X_test) tuple
2025-07-29 09:00:50,814 - __main__ - INFO - Train embeddings shape: (42509, 384)
2025-07-29 09:00:50,814 - __main__ - INFO - Val embeddings shape: (5314, 384)
2025-07-29 09:00:50,814 - __main__ - INFO - Test embeddings shape: (5314, 384)
2025-07-29 09:00:50,814 - __main__ - INFO - DEBUG: Train embeddings type: <class 'numpy.ndarray'>
2025-07-29 09:00:50,814 - __main__ - INFO - DEBUG: NumPy array with shape (42509, 384)
2025-07-29 09:00:50,814 - __main__ - INFO - DEBUG: First row sample: [ 0.02741967  0.071001   -0.11623035  0.0128611   0.00438468]
2025-07-29 09:00:50,815 - __main__ - INFO - DEBUG: Train embeddings type: <class 'numpy.ndarray'>, no label fields detected
2025-07-29 09:00:50,815 - __main__ - WARNING - Generating synthetic labels as no labels were provided
2025-07-29 09:00:54,725 - __main__ - INFO - Generated synthetic labels with 2 classes
2025-07-29 09:00:54,726 - __main__ - INFO - Preparing DataFrames for AutoGluon
2025-07-29 09:00:54,729 - __main__ - INFO - Created DataFrames with shapes: train=(42509, 385), val=(5314, 385), test=(5314, 385)
2025-07-29 09:00:54,730 - __main__ - INFO - Training AutoGluon model for casehold_full (embeddings)...
2025-07-29 09:04:12,227 - __main__ - INFO - Initialized AutoGluonEmbeddingsEvaluator
2025-07-29 09:04:12,227 - __main__ - INFO - Output directory: autogluon_embeddings_results/run_20250729_090412
2025-07-29 09:04:12,227 - __main__ - INFO - Time limit per model: 3000 seconds
2025-07-29 09:04:12,227 - __main__ - INFO - Using preset: medium_quality
2025-07-29 09:04:12,227 - __main__ - INFO - Starting evaluation for casehold_full using embeddings from /storage/nammt/autogluon/embeddings_full.joblib
2025-07-29 09:04:12,227 - __main__ - INFO - Loading embeddings from /storage/nammt/autogluon/embeddings_full.joblib
2025-07-29 09:04:12,274 - __main__ - INFO - DEBUG: Loaded data type: <class 'tuple'>
2025-07-29 09:04:12,274 - __main__ - INFO - DEBUG: Tuple length: 3
2025-07-29 09:04:12,275 - __main__ - INFO - DEBUG: Item 0 type: <class 'numpy.ndarray'>, shape: (42509, 384)
2025-07-29 09:04:12,275 - __main__ - INFO - DEBUG: Item 1 type: <class 'numpy.ndarray'>, shape: (5314, 384)
2025-07-29 09:04:12,275 - __main__ - INFO - DEBUG: Item 2 type: <class 'numpy.ndarray'>, shape: (5314, 384)
2025-07-29 09:04:12,275 - __main__ - INFO - Successfully loaded embeddings as (X_train, X_val, X_test) tuple
2025-07-29 09:04:12,275 - __main__ - INFO - Train embeddings shape: (42509, 384)
2025-07-29 09:04:12,275 - __main__ - INFO - Val embeddings shape: (5314, 384)
2025-07-29 09:04:12,275 - __main__ - INFO - Test embeddings shape: (5314, 384)
2025-07-29 09:04:12,275 - __main__ - INFO - DEBUG: Train embeddings type: <class 'numpy.ndarray'>
2025-07-29 09:04:12,275 - __main__ - INFO - DEBUG: NumPy array with shape (42509, 384)
2025-07-29 09:04:12,276 - __main__ - INFO - DEBUG: First row sample: [ 0.02741967  0.071001   -0.11623035  0.0128611   0.00438468]
2025-07-29 09:04:12,280 - __main__ - INFO - DEBUG: Last column sample values: [-0.04033726 -0.01260601 -0.00262194 -0.01361219 -0.04342444 -0.00029491
  0.06041325 -0.01431128  0.02447725  0.00482581]
2025-07-29 09:04:12,281 - __main__ - INFO - DEBUG: Unique values in last column: 42491 (first 10: [-0.14554007 -0.14125092 -0.14082935 -0.14071876 -0.14070722 -0.1403489
 -0.13893346 -0.13870896 -0.13839486 -0.13831827])
2025-07-29 09:04:12,281 - __main__ - INFO - DEBUG: Is likely label column: False
2025-07-29 09:04:12,281 - __main__ - INFO - DEBUG: Train embeddings type: <class 'numpy.ndarray'>, no label fields detected
2025-07-29 09:04:12,285 - __main__ - INFO - DEBUG: Last column sample values: [-0.04033726 -0.01260601 -0.00262194 -0.01361219 -0.04342444 -0.00029491
  0.06041325 -0.01431128  0.02447725  0.00482581]
2025-07-29 09:04:12,286 - __main__ - INFO - DEBUG: Unique values in last column: 42491 (first 10: [-0.14554007 -0.14125092 -0.14082935 -0.14071876 -0.14070722 -0.1403489
 -0.13893346 -0.13870896 -0.13839486 -0.13831827])
2025-07-29 09:04:12,286 - __main__ - INFO - DEBUG: Is likely label column: False
2025-07-29 09:04:12,286 - __main__ - WARNING - Generating synthetic labels as no labels were provided
2025-07-29 09:04:16,489 - __main__ - INFO - Generated synthetic labels with 2 classes
2025-07-29 09:04:16,489 - __main__ - INFO - Preparing DataFrames for AutoGluon
2025-07-29 09:04:16,492 - __main__ - INFO - Created DataFrames with shapes: train=(42509, 385), val=(5314, 385), test=(5314, 385)
2025-07-29 09:04:16,492 - __main__ - INFO - Training AutoGluon model for casehold_full (embeddings)...
