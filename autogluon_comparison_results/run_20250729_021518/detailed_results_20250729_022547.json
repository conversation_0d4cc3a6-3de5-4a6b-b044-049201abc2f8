{"anli_r1": {"after": {"dataset": "anli_r1", "variant": "after", "performance": {"accuracy": 0.314, "balanced_accuracy": 0.3140775506044967, "mcc": -0.03174027546652221}, "best_model": "RandomForestEntr", "best_val_score": 0.33, "test_score": 0.344, "accuracy": 0.314, "num_models_trained": 14, "total_models_attempted": 14, "leaderboard_top3": [{"model": "RandomForestEntr", "score_val": 0.33}, {"model": "ExtraTreesGini", "score_val": 0.338}, {"model": "ExtraTreesEntr", "score_val": 0.324}], "model_training_times": {"RandomForestEntr": 6.589907169342041, "ExtraTreesGini": 1.4561145305633545, "ExtraTreesEntr": 1.4939920902252197, "KNeighborsUnif": 0.15208935737609863, "NeuralNetFastAI": 6.829256534576416, "KNeighborsDist": 0.1231222152709961, "LightGBM": 26.456484079360962, "RandomForestGini": 4.563036203384399, "XGBoost": 51.114123821258545, "LightGBMLarge": 52.632126569747925, "CatBoost": 25.96704411506653, "NeuralNetTorch": 10.351701974868774, "WeightedEnsemble_L2": 62.47276973724365, "LightGBMXT": 20.803770542144775}, "problem_type": "multiclass", "eval_metric": "accuracy", "predictions_saved": true, "confusion_matrix_saved": true, "model_size_mb": 140.21, "num_parameters": "N/A", "model_type": "TabularPredictor", "total_training_time": 213.08362078666687, "train_size": 2000, "val_size": 1000, "test_size": 1000, "feature_count": 388}}, "casehold": {"after": {"dataset": "casehold", "variant": "after", "performance": {"accuracy": 0.19966127211140383, "balanced_accuracy": 0.20001623967258692, "mcc": 4.436853768321112e-05}, "best_model": "KNeighborsUnif", "best_val_score": 0.1940158073014678, "test_score": 0.20530673692133986, "accuracy": 0.19966127211140383, "num_models_trained": 14, "total_models_attempted": 14, "leaderboard_top3": [{"model": "KNeighborsUnif", "score_val": 0.1940158073014678}, {"model": "NeuralNetFastAI", "score_val": 0.19665035754610463}, {"model": "LightGBMXT", "score_val": 0.19853217914941665}], "model_training_times": {"KNeighborsUnif": 0.3192887306213379, "NeuralNetFastAI": 39.39030647277832, "LightGBMXT": 24.273102045059204, "RandomForestGini": 19.755093574523926, "RandomForestEntr": 28.78807282447815, "ExtraTreesGini": 2.6445140838623047, "ExtraTreesEntr": 2.648583173751831, "LightGBMLarge": 106.08471918106079, "KNeighborsDist": 0.31980419158935547, "LightGBM": 32.639124631881714, "NeuralNetTorch": 47.26995921134949, "XGBoost": 38.78889083862305, "CatBoost": 46.65053057670593, "WeightedEnsemble_L2": 47.34767770767212}, "problem_type": "multiclass", "eval_metric": "accuracy", "predictions_saved": true, "confusion_matrix_saved": true, "model_size_mb": 248.05, "num_parameters": "N/A", "model_type": "TabularPredictor", "total_training_time": 396.64750361442566, "train_size": 9500, "val_size": 5314, "test_size": 5314, "feature_count": 384}}}