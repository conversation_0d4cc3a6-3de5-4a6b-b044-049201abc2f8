{"dataset": "casehold_imbalanced", "preprocessing_method": "tpot", "num_features_after_preprocessing": 1, "timing": {"tpot_fit_time": 0.1, "transform_time": 0, "data_save_time": 0, "preprocessing_total_time": 0.04242968559265137, "autogluon_training_time": 5.050965070724487, "total_pipeline_time": 5.093818664550781, "formatted_total_time": "5.09 seconds"}, "tpot_pipeline": "Pipeline(steps=[('logisticregression',\n                 LogisticRegression(C=0.01, max_iter=1000, random_state=42,\n                                    solver='liblinear'))])", "accuracy": "N/A", "performance": "N/A", "best_model": "N/A", "leaderboard_top3": []}