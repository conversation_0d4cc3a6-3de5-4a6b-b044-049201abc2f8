[{"dataset": "anli_r1_noisy", "preprocessing_method": "tpot", "num_features_after_preprocessing": 1, "timing": {"tpot_fit_time": 0.1, "transform_time": 0, "data_save_time": 0, "preprocessing_total_time": 0.14026737213134766, "autogluon_training_time": 5.156728506088257, "total_pipeline_time": 5.297365665435791, "formatted_total_time": "5.30 seconds"}, "tpot_pipeline": "Pipeline(steps=[('randomforestclassifier',\n                 RandomForestClassifier(bootstrap=False, criterion='entropy',\n                                        max_depth=5, min_samples_split=5,\n                                        n_estimators=200, random_state=42))])", "accuracy": "N/A", "performance": "N/A", "best_model": "N/A", "leaderboard_top3": [], "experiment_timing": {"dataset_processing_time": 5.393049716949463, "overall_experiment_time": 11.724596500396729}}, {"dataset": "casehold_imbalanced", "preprocessing_method": "tpot", "num_features_after_preprocessing": 1, "timing": {"tpot_fit_time": 0.1, "transform_time": 0, "data_save_time": 0, "preprocessing_total_time": 0.04242968559265137, "autogluon_training_time": 5.050965070724487, "total_pipeline_time": 5.093818664550781, "formatted_total_time": "5.09 seconds"}, "tpot_pipeline": "Pipeline(steps=[('logisticregression',\n                 LogisticRegression(C=0.01, max_iter=1000, random_state=42,\n                                    solver='liblinear'))])", "accuracy": "N/A", "performance": "N/A", "best_model": "N/A", "leaderboard_top3": [], "experiment_timing": {"dataset_processing_time": 6.331143379211426, "overall_experiment_time": 11.724596500396729}}]