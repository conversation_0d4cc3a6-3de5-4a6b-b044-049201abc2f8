{"casehold_full": {"after": {"dataset": "casehold_full", "variant": "after", "performance": {"accuracy": 0.20342491531802784, "balanced_accuracy": 0.2046786357535865, "mcc": 0.006589674448348867}, "best_model": "XGBoost", "best_val_score": 0.21019947308995107, "test_score": 0.2118931125329319, "accuracy": 0.20342491531802784, "num_models_trained": 14, "total_models_attempted": 14, "leaderboard_top3": [{"model": "XGBoost", "score_val": 0.21019947308995107}, {"model": "NeuralNetFastAI", "score_val": 0.20681219420398947}, {"model": "LightGBMXT", "score_val": 0.2122694768535943}], "model_training_times": {"XGBoost": 78.23555254936218, "NeuralNetFastAI": 207.4309687614441, "LightGBMXT": 25.87982678413391, "WeightedEnsemble_L2": 190.0562243461609, "ExtraTreesGini": 23.1925630569458, "NeuralNetTorch": 87.86508917808533, "RandomForestEntr": 321.6411144733429, "LightGBMLarge": 47.34784293174744, "LightGBM": 19.333125829696655, "KNeighborsUnif": 1.352872371673584, "KNeighborsDist": 1.314985990524292, "CatBoost": 28.261615991592407, "RandomForestGini": 246.57132196426392, "ExtraTreesEntr": 25.121495962142944}, "problem_type": "multiclass", "eval_metric": "accuracy", "predictions_saved": true, "confusion_matrix_saved": true, "model_size_mb": 3138.15, "num_parameters": "N/A", "model_type": "TabularPredictor", "total_training_time": 1142.6421270370483, "train_size": 43045, "val_size": 5314, "test_size": 5314, "feature_count": 384}}}