{"system": "Linux", "version": "1.3.1", "lite": false, "py_version": "3.10", "py_version_micro": "3.10.12", "packages": {"tifffile": "2025.5.10", "jsonschema-specifications": "2025.4.1", "fsspec": "2025.3.0", "tzdata": "2025.2", "regex": "2024.11.6", "pyzmq": "27.0.0", "attrs": "25.3.0", "flatbuffers": "25.2.10", "packaging": "25.0", "pyarrow": "20.0.0", "libclang": "18.1.1", "rich": "14.0.0", "nvidia-nvtx-cu12": "12.4.127", "nvidia-nvjitlink-cu12": "12.4.127", "nvidia-cuda-runtime-cu12": "12.4.127", "nvidia-cuda-nvrtc-cu12": "12.4.127", "nvidia-cuda-cupti-cu12": "12.4.127", "nvidia-cublas-cu12": "********", "nvidia-cusparse-cu12": "**********", "nvidia-cusolver-cu12": "********", "pillow": "11.3.0", "nvidia-cufft-cu12": "********", "nvidia-curand-cu12": "**********", "nvidia-cudnn-cu12": "********", "ipython": "8.37.0", "jupyter-client": "8.6.3", "pytest": "8.4.1", "thinc": "8.3.6", "nvidia-ml-py3": "7.352.0", "ipykernel": "6.29.5", "colorlog": "6.9.0", "tornado": "6.5.1", "plotly": "6.2.0", "pyyaml": "6.0.2", "protobuf": "5.29.5", "traitlets": "5.14.3", "jupyter-core": "5.8.1", "gdown": "5.2.0", "chardet": "5.2.0", "sentence-transformers": "5.0.0", "tqdm": "4.67.1", "fonttools": "4.58.5", "transformers": "4.49.0", "jsonschema": "4.23.0", "typing-extensions": "4.14.1", "antlr4-python3-runtime": "4.9.3", "lightgbm": "4.6.0", "optuna": "4.4.0", "ordered-set": "4.1.0", "datasets": "4.0.0", "pycryptodome": "3.23.0", "h5py": "3.14.0", "aiohttp": "3.12.14", "orjson": "3.10.18", "matplotlib": "3.10.3", "keras": "3.10.0", "spacy": "3.8.7", "nltk": "3.8.1", "threadpoolctl": "3.6.0", "xxhash": "3.5.0", "langcodes": "3.5.0", "networkx": "3.4.2", "opt-einsum": "3.4.0", "scs": "3.2.7.post2", "triton": "3.2.0", "cloudpickle": "3.1.1", "termcolor": "3.1.0", "prompt-toolkit": "3.0.51", "spacy-legacy": "3.0.12", "preshed": "3.0.10", "xgboost": "3.0.2", "markdown-it-py": "3.0.0", "asttokens": "3.0.0", "ray": "2.44.1", "google-auth": "2.40.3", "imageio": "2.37.0", "pydantic-core": "2.33.2", "requests": "2.32.4", "google-api-core": "2.25.1", "pycparser": "2.22", "nvidia-nccl-cu12": "2.21.5", "pygments": "2.19.2", "tensorflow": "2.19.0", "tensorboard": "2.19.0", "tf-keras": "2.19.0", "pydantic": "2.11.7", "pyprind": "2.11.3", "python-dateutil": "2.9.0.post0", "fastai": "2.8.2", "pytorch-metric-learning": "2.8.1", "tensorboardx": "2.6.4", "aiohappyeyeballs": "2.6.1", "torch": "2.6.0", "plum-dispatch": "2.5.7", "pytorch-lightning": "2.5.2", "lightning": "2.5.2", "srsly": "2.5.1", "fs": "2.4.16", "absl-py": "2.3.1", "omegaconf": "2.3.0", "pandas": "2.2.3", "executing": "2.2.0", "numpy": "2.1.3", "iniconfig": "2.1.0", "sqlalchemy": "2.0.41", "cymem": "2.0.11", "catalogue": "2.0.10", "statsforecast": "2.0.1", "grpcio": "1.73.1", "googleapis-common-protos": "1.70.0", "narwhals": "1.47.0", "proto-plus": "1.26.1", "yarl": "1.20.1", "cffi": "1.17.1", "pdf2image": "1.17.0", "alembic": "1.16.4", "scipy": "1.15.3", "sympy": "1.13.1", "debugpy": "1.8.15", "fastcore": "1.8.5", "accelerate": "1.8.1", "torchmetrics": "1.7.4", "cvxpy": "1.7.1", "pysocks": "1.7.1", "astunparse": "1.6.3", "scikit-learn": "1.6.1", "nest-asyncio": "1.6.0", "pluggy": "1.6.0", "shellingham": "1.5.4", "joblib": "1.5.1", "kiwisolver": "1.4.8", "deap": "1.4.3", "aiosignal": "1.4.0", "mako": "1.3.10", "contourpy": "1.3.2", "autogluon.core": "1.3.1", "autogluon": "1.3.1", "autogluon.features": "1.3.1", "autogluon.timeseries": "1.3.1", "autogluon.tabular": "1.3.1", "autogluon.multimodal": "1.3.1", "autogluon.common": "1.3.1", "language-data": "1.3.0", "text-unidecode": "1.3", "blis": "1.3.0", "mpmath": "1.3.0", "exceptiongroup": "1.3.0", "catboost": "1.2.8", "seqeval": "1.2.2", "marisa-trie": "1.2.1", "nlpaug": "1.1.11", "hf-xet": "1.1.5", "wasabi": "1.1.3", "murmurhash": "1.0.13", "tdda": "1.0.13", "spacy-loggers": "1.0.5", "osqp": "1.0.4", "fastprogress": "1.0.3", "timm": "1.0.3", "patsy": "1.0.1", "future": "1.0.0", "multiprocess": "0.70.16", "numba": "0.61.2", "llvmlite": "0.44.0", "tensorflow-io-gcs-filesystem": "0.37.1", "referencing": "0.36.2", "huggingface-hub": "0.33.4", "rpds-py": "0.26.0", "scikit-image": "0.25.2", "prometheus-client": "0.22.1", "tokenizers": "0.21.2", "cloudpathlib": "0.21.1", "graphviz": "0.21", "torchvision": "0.21.0", "beartype": "0.21.0", "jedi": "0.19.2", "update-checker": "0.18.0", "gluonts": "0.16.2", "optree": "0.16.0", "typer": "0.16.0", "statsmodels": "0.14.5", "lightning-utilities": "0.14.3", "mlforecast": "0.13.6", "seaborn": "0.13.2", "imbalanced-learn": "0.13.0", "tpot": "0.12.2", "toolz": "0.12.1", "cycler": "0.12.1", "opencensus": "0.11.4", "clarabel": "0.11.1", "py4j": "********", "triad": "0.9.8", "fugue": "0.9.1", "parso": "0.8.4", "aiohttp-cors": "0.8.1", "einops": "0.8.1", "tensorboard-data-server": "0.7.2", "jellyfish": "0.7.1", "defusedxml": "0.7.1", "annotated-types": "0.7.0", "stack-data": "0.6.3", "nvidia-cusparselt-cu12": "0.6.2", "gast": "0.6.0", "np-utils": "0.6.0", "colorful": "0.5.7", "safetensors": "0.5.3", "ml-dtypes": "0.5.1", "py-stringmatching": "0.4.5", "evaluate": "0.4.5", "fancyimpute": "0.4.2", "typing-inspection": "0.4.1", "weasel": "0.4.1", "lazy-loader": "0.4", "py-spy": "0.4.0", "pytesseract": "0.3.13", "openmim": "0.3.9", "dill": "0.3.8", "propcache": "0.3.2", "utilsforecast": "0.2.10", "hyperopt": "0.2.7", "adagio": "0.2.6", "pure-eval": "0.2.3", "comm": "0.2.2", "google-pasta": "0.2.0", "sentencepiece": "0.2.0", "model-index": "0.1.11", "matplotlib-inline": "0.1.7", "confection": "0.1.5", "opencensus-context": "0.1.3", "sklearn-compat": "0.1.3", "mdurl": "0.1.2", "namex": "0.1.0", "knnimpute": "0.1.0", "py-stringsimjoin": "0.1.0", "window-ops": "0.0.15", "coreforecast": "0.0.15", "openxlab": "0.0.11", "opendatalab": "0.0.10", "impyute": "0.0.7", "fastdownload": "0.0.7", "fasttransform": "0.0.2", "imblearn": "0.0", "ubuntu-pro-client": "8001", "pytz": "2022.1", "certifi": "2020.6.20", "systemd-python": "234", "setuptools": "59.6.0", "keyring": "23.5.0", "azure-mgmt-compute": "23.1.0", "pip": "22.0.2", "black": "21.10b0", "gevent": "21.8.0", "pyopenssl": "21.0.0", "virtualenv": "20.13.0+ds", "azure-mgmt-resource": "20.0.0", "azure-mgmt-network": "19.2.0", "azure-mgmt-storage": "19.0.0", "azure-mgmt-containerservice": "16.3.0", "azure-mgmt-batch": "16.0.0", "azure-mgmt-redis": "13.0.0", "azure-storage-blob": "12.9.0", "azure-storage-file-share": "12.6.0", "azure-storage-file-datalake": "12.5.0", "azure-storage-queue": "12.1.6", "azure-data-tables": "12.1.1", "kubernetes": "12.0.1", "azure-mgmt-cognitiveservices": "12.0.0", "azure-storage-blob-changefeed": "12.0.0b3", "dropbox": "11.26.0", "azure-search-documents": "11.3.0b5", "azure-mgmt-cdn": "11.0.0", "azure-batch": "11.0.0", "azure-mgmt-loganalytics": "11.0.0", "humanfriendly": "10.0", "azure-mgmt-datamigration": "10.0.0", "azure-mgmt-eventhub": "10.0.0", "azure-mgmt-rdbms": "10.0.0", "azure-mgmt-keyvault": "9.2.0", "azure-mgmt-containerinstance": "9.1.0", "azure-mgmt-advisor": "9.0.0", "azure-mgmt-eventgrid": "9.0.0", "azure-mgmt-devtestlabs": "9.0.0", "azure-mgmt-logic": "9.0.0", "azure-mgmt-hdinsight": "9.0.0", "azure-mgmt-iotcentral": "9.0.0b1", "more-itertools": "8.10.0", "schema-salad": "8.2.20220103095339", "azure-mgmt-containerregistry": "8.2.0", "azure-mgmt-resourcegraph": "8.1.0b1", "click": "8.0.3", "azure-mgmt-media": "8.0.0", "azure-mgmt-consumption": "8.0.0", "azure-servicefabric": "8.0.0.0", "azure-mgmt-search": "8.0.0", "azure-mgmt-dns": "8.0.0", "azure-servicebus": "7.4.0", "coloredlogs": "7.3", "azure-mgmt-servicebus": "7.1.0", "azure-mgmt-hybridcompute": "7.0.0", "azure-mgmt-notificationhubs": "7.0.0", "azure-mgmt-cosmosdb": "7.0.0b2", "azure-mgmt-avs": "7.0.0b1", "azure-mgmt-batchai": "7.0.0b1", "azure-mgmt-scheduler": "7.0.0b1", "snakemake": "6.15.1", "rdflib": "6.1.1", "azure-mgmt-appplatform": "6.1.0", "azure-mgmt-azurestackhci": "6.1.0b1", "azure-mgmt-billing": "6.0.0", "azure-mgmt-managedservices": "6.0.0", "azure-mgmt-commerce": "6.0.0", "azure-mgmt-digitaltwins": "6.0.0", "azure-mgmt-support": "6.0.0", "azure-mgmt-msi": "6.0.0b1", "ssh-import-id": "5.11", "psutil": "5.9.0", "azure-eventhub": "5.6.2", "zope.interface": "5.4.0", "smart-open": "5.2.1", "azure-ai-textanalytics": "5.2.0b3", "nbformat": "5.1.3", "azure-mgmt-netapp": "5.1.0", "multidict": "5.1.0", "zope.hookable": "5.1.0", "configobj": "5.0.6", "docker": "5.0.3", "ftputil": "5.0.3", "smmap": "5.0.0", "xdg": "5", "azure-mgmt-web": "5.0.0", "cachetools": "5.0.0", "beautifulsoup4": "4.10.0", "lxml": "4.8.0", "rsa": "4.8", "pexpect": "4.8.0", "azure-eventgrid": "4.7.0", "importlib-metadata": "4.6.4", "azure-keyvault-keys": "4.5.0b5", "decorator": "4.4.2", "zope.event": "4.4", "azure-keyvault-certificates": "4.4.0b2", "azure-keyvault-secrets": "4.4.0b2", "zope.component": "4.3.0", "azure-keyvault": "4.1.0", "azure-keyvault-administration": "4.1.0b2", "gitdb": "4.0.9", "async-timeout": "4.0.1", "pygobject": "3.42.1", "louis": "3.20.0", "simplejson": "3.17.6", "ply": "3.11", "reportlab": "3.6.8", "filelock": "3.6.0", "asgiref": "3.5.0", "cryptography": "3.4.8", "shellescape": "3.4.1", "markdown": "3.3.6", "cfgv": "3.3.1", "secretstorage": "3.3.1", "stone": "3.3.1", "roman": "3.3", "idna": "3.3", "bcrypt": "3.2.0", "oauthlib": "3.2.0", "azure-ai-formrecognizer": "3.2.0b2", "cwltool": "3.1.20220224085855", "gitpython": "3.1.24", "azure-cognitiveservices-vision-customvision": "3.1.0", "jinja2": "3.0.3", "azure-mgmt-sql": "3.0.1", "azure-mgmt-recoveryservicesbackup": "3.0.0", "azure-mgmt-costmanagement": "3.0.0", "azure-ai-anomalydetector": "3.0.0b3", "pre-commit": "2.17.0", "paramiko": "2.9.3", "pyicu": "2.8.1", "babel": "2.8.0", "parsedatetime": "2.6", "pulp": "2.5.1", "platformdirs": "2.5.1", "prettytable": "2.5.0", "identify": "2.4.10", "pyparsing": "2.4.7", "python-apt": "2.4.0+u<PERSON><PERSON><PERSON>4", "soupsieve": "2.3.1", "pyjwt": "2.3.0", "portalocker": "2.2.1", "azure-mgmt-apimanagement": "2.1.0", "itsdangerous": "2.1.0", "azure-mgmt-kusto": "2.1.0", "azure-mgmt-iothub": "2.1.0", "azure-mgmt-synapse": "2.1.0b2", "charset-normalizer": "2.0.6", "werkzeug": "2.0.2", "markupsafe": "2.0.1", "flask": "2.0.1", "pycups": "2.0.1", "azure-cognitiveservices-search-imagesearch": "2.0.0", "azure-mgmt-authorization": "2.0.0", "azure-cognitiveservices-search-websearch": "2.0.0", "azure-cognitiveservices-search-entitysearch": "2.0.0", "azure-mgmt-maps": "2.0.0", "azure-mgmt-appconfiguration": "2.0.0", "azure-mgmt-maintenance": "2.0.0", "azure-mgmt-powerbiembedded": "2.0.0", "azure-mgmt-servermanager": "2.0.0", "azure-mgmt-servicefabric": "2.0.0", "prov": "2.0.0", "azure-cognitiveservices-language-spellcheck": "2.0.0", "azure-mgmt-datafactory": "2.0.0", "azure-mgmt-monitor": "2.0.0", "azure-cognitiveservices-search-videosearch": "2.0.0", "azure-mgmt-recoveryservices": "2.0.0", "azure-mgmt-datadog": "2.0.0", "azure-cognitiveservices-search-newssearch": "2.0.0", "azure-mgmt-security": "2.0.0b1", "azure-mgmt-confluent": "2.0.0b1", "cliapp": "1.20180812.1", "biopython": "1.79", "docker-compose": "1.29.2", "urllib3": "1.26.5", "botocore": "1.23.34", "certbot": "1.21.0", "acme": "1.21.0", "certbot-nginx": "1.21.0", "boto3": "1.20.34", "azure-core": "1.20.1", "pycairo": "1.20.1", "msal": "1.17.0", "six": "1.16.0", "wrapt": "1.13.3", "avro": "1.11.0", "launchpadlib": "1.10.16", "josepy": "1.10.0", "sparqlwrapper": "1.8.5", "argcomplete": "1.8.1", "azure-identity": "1.7.1", "distro": "1.7.0", "pygraphviz": "1.7", "texttable": "1.6.4", "toposort": "1.6", "configargparse": "1.5.3", "uamqp": "1.5.1", "pynacl": "1.5.0", "appdirs": "1.4.4", "pydot": "1.4.2", "blinker": "1.4", "wadllib": "1.3.6", "yappi": "1.3.3", "macaroonbakery": "1.3.1", "azure-mgmt-core": "1.3.0", "requests-oauthlib": "1.3.0", "azure-appconfiguration": "1.3.0", "dbus-python": "1.2.18", "websocket-client": "1.2.3", "tomli": "1.2.2", "adal": "1.2.2", "ratelimiter": "1.2.0.post0", "frozenlist": "1.2.0", "azure-common": "1.1.28", "azure-eventhub-checkpointstoreblob": "1.1.4", "azure-eventhub-checkpointstoreblob-aio": "1.1.4", "stopit": "1.1.2", "greenlet": "1.1.2", "distro-info": "1.1+ubuntu0.2", "pyrfc3339": "1.1", "azure-mgmt-storagecache": "1.1.0", "azure-mgmt-marketplaceordering": "1.1.0", "azure-mgmt-hybridkubernetes": "1.1.0", "html5lib": "1.1", "azure-communication-chat": "1.1.0", "azure-digitaltwins-core": "1.1.0", "azure-mgmt-relay": "1.1.0", "azure-mgmt-resourcemover": "1.1.0b2", "azure-mgmt-databricks": "1.1.0b1", "azure-mgmt-policyinsights": "1.1.0b1", "azure-mgmt-automation": "1.1.0b1", "azure-mgmt-healthcareapis": "1.1.0b1", "lazr.uri": "1.0.6", "defer": "1.0.6", "msgpack": "1.0.3", "azure-communication-sms": "1.0.1", "azure-ai-metricsadvisor": "1.0.1", "azure-communication-phonenumbers": "1.0.1", "azure-monitor-query": "1.0.1", "azure-security-attestation": "1.0.1", "azure-communication-identity": "1.0.1", "cupshelpers": "1.0", "azure-mgmt-deploymentmanager": "1.0.0", "azure-mgmt-azurestack": "1.0.0", "azure-mgmt-servicefabricmanagedclusters": "1.0.0", "azure-mgmt-managementgroups": "1.0.0", "azure-mgmt-portal": "1.0.0", "azure-mgmt-alertsmanagement": "1.0.0", "azure-cognitiveservices-vision-contentmoderator": "1.0.0", "azure-mgmt-hanaonazure": "1.0.0", "azure-mgmt-baremetalinfrastructure": "1.0.0", "azure-mgmt-logz": "1.0.0", "azure-ai-language-questionanswering": "1.0.0", "azure-mgmt-webpubsub": "1.0.0", "azure-mgmt-redhatopenshift": "1.0.0", "azure-mgmt-datalake-store": "1.0.0", "azure-mgmt-timeseriesinsights": "1.0.0", "azure-mgmt-databox": "1.0.0", "azure-mgmt-elastic": "1.0.0", "azure-mgmt-storagesync": "1.0.0", "azure-mgmt-reservations": "1.0.0", "azure-mgmt-customproviders": "1.0.0", "azure-mgmt-botservice": "1.0.0", "msal-extensions": "1.0.0", "azure-mgmt-applicationinsights": "1.0.0", "azure-mgmt-azurearcdata": "1.0.0", "azure-mgmt-frontdoor": "1.0.0", "azure-mgmt-hybridnetwork": "1.0.0", "azure-mgmt-extendedlocation": "1.0.0", "azure-mgmt-attestation": "1.0.0", "zipp": "1.0.0", "azure-mgmt-powerbidedicated": "1.0.0", "azure-mgmt-datashare": "1.0.0", "wsproto": "1.0.0", "azure-mgmt-serialconsole": "1.0.0", "azure-schemaregistry": "1.0.0", "azure-mgmt-machinelearningservices": "1.0.0", "azure-mgmt-labservices": "1.0.0", "azure-mgmt-databoxedge": "1.0.0", "azure-mgmt-operationsmanagement": "1.0.0", "azure-mgmt-privatedns": "1.0.0", "azure-mgmt-purview": "1.0.0", "azure-mgmt-changeanalysis": "1.0.0", "azure-mgmt-redisenterprise": "1.0.0", "azure-mgmt-storagepool": "1.0.0", "azure-mgmt-peering": "1.0.0", "azure-mgmt-subscription": "1.0.0", "azure-mgmt-signalr": "1.0.0", "azure-mgmt-iothubprovisioningservices": "1.0.0", "azure-mgmt-managementpartner": "1.0.0", "azure-mgmt-mixedreality": "1.0.0", "azure-mgmt-communication": "1.0.0", "azure-mgmt-streamanalytics": "1.0.0rc1", "azure-containerregistry": "1.0.0b7", "azure-ai-translation-document": "1.0.0b6", "azure-monitor-opentelemetry-exporter": "1.0.0b6", "azure-schemaregistry-avroserializer": "1.0.0b4", "azure-purview-scanning": "1.0.0b3", "azure-purview-catalog": "1.0.0b3", "azure-messaging-webpubsubservice": "1.0.0b3", "azure-mgmt-deviceupdate": "1.0.0b3", "azure-media-videoanalyzer-edge": "1.0.0b3", "azure-mgmt-videoanalyzer": "1.0.0b3", "azure-mgmt-quota": "1.0.0b2", "azure-purview-administration": "1.0.0b2", "azure-mgmt-automanage": "1.0.0b2", "azure-iot-modelsrepository": "1.0.0b2", "azure-mgmt-quantum": "1.0.0b2", "azure-mgmt-chaos": "1.0.0b2", "azure-mgmt-workloadmonitor": "1.0.0b2", "azure-agrifood-farming": "1.0.0b1", "azure-mgmt-storageimportexport": "1.0.0b1", "azure-mgmt-machinelearningcompute": "1.0.0b1", "azure-mgmt-confidentialledger": "1.0.0b1", "azure-mgmt-sqlvirtualmachine": "1.0.0b1", "azure-mgmt-vmwarecloudsimple": "1.0.0b1", "azure-communication-networktraversal": "1.0.0b1", "azure-mgmt-regionmove": "1.0.0b1", "azure-mgmt-testbase": "1.0.0b1", "azure-mgmt-servicelinker": "1.0.0b1", "azure-mgmt-resourcehealth": "1.0.0b1", "azure-mgmt-imagebuilder": "1.0.0b1", "azure-mgmt-healthbot": "1.0.0b1", "azure-iot-deviceupdate": "1.0.0b1", "azure-mgmt-recoveryservicessiterecovery": "1.0.0b1", "azure-mgmt-devspaces": "1.0.0b1", "azure-mgmt-fluidrelay": "1.0.0b1", "azure-mgmt-azureadb2c": "1.0.0b1", "azure-mgmt-agfood": "1.0.0b1", "azure-mgmt-kubernetesconfiguration": "1.0.0b1", "azure-mgmt-agrifood": "1.0.0b1", "azure-confidentialledger": "1.0.0b1", "azure-eventhub-checkpointstoretable": "1.0.0b1", "azure-mgmt-trafficmanager": "1.0.0b1", "azure-mgmt-dataprotection": "1.0.0b1", "azure-mgmt-guestconfig": "1.0.0b1", "azure-media-analytics-edge": "1.0.0b1", "azure-mgmt-securityinsight": "1.0.0b1", "azure-mgmt-edgeorder": "1.0.0b1", "azure-mixedreality-authentication": "1.0.0b1", "azure-ai-language-conversations": "1.0.0b1", "meson": "0.61.2", "azure-graphrbac": "0.61.1", "olefile": "0.46", "ttystatus": "0.38", "wheel": "0.37.1", "ufw": "0.36.1", "pyxdg": "0.27", "azure-servicemanagement-legacy": "0.20.7", "httplib2": "0.20.2", "python-dotenv": "0.19.2", "pyrsistent": "0.18.1", "ruamel.yaml": "0.17.16", "docutils": "0.17.1", "uvicorn": "0.15.0", "lazr.restfulclient": "0.14.4", "nodeenv": "0.13.4", "pymacaroons": "0.13.0", "h11": "0.13.0", "cachecontrol": "0.12.10", "lockfile": "0.12.2", "netifaces": "0.11.0", "toml": "0.10.2", "jmespath": "0.10.0", "azure-synapse-artifacts": "0.10.0", "pyinotify": "0.9.6", "requests-toolbelt": "0.9.1", "azure-cognitiveservices-vision-computervision": "0.9.0", "pathspec": "0.9.0", "tabulate": "0.8.9", "mistune": "0.8.4", "brlapi": "0.8.3", "datrie": "0.8.2", "python-irodsclient": "0.8.1", "azure-synapse-accesscontrol": "0.8.0", "azure-synapse-spark": "0.8.0", "drmaa": "0.7.9", "jeepney": "0.7.1", "azure-cognitiveservices-language-luis": "0.7.0", "ptyprocess": "0.7.0", "msrest": "0.6.21", "msrestazure": "0.6.4", "docopt": "0.6.2", "rdflib-jsonld": "0.6.1", "isodate": "0.6.1", "azure-mgmt-datalake-analytics": "0.6.0", "apturl": "0.5.2", "webencodings": "0.5.1", "azure-synapse-managedprivateendpoints": "0.5.0", "azure-cognitiveservices-vision-face": "0.5.0", "s3transfer": "0.5.0", "pyasn1": "0.4.8", "colorama": "0.4.4", "mypy-extensions": "0.4.3", "dockerpty": "0.4.1", "distlib": "0.3.4", "azure-cognitiveservices-search-customsearch": "0.3.0", "azure-synapse-monitoring": "0.3.0", "azure-cognitiveservices-anomalydetector": "0.3.0", "command-not-found": "0.3", "azure-cognitiveservices-knowledge-qnamaker": "0.3.0", "ruamel.yaml.clib": "0.2.6", "wcwidth": "0.2.5", "arcp": "0.2.1", "pyasn1-modules": "0.2.1", "azure-cognitiveservices-search-customimagesearch": "0.2.0", "azure-cognitiveservices-search-autosuggest": "0.2.0", "azure-cognitiveservices-language-textanalytics": "0.2.0", "ipython-genutils": "0.2.0", "azure-cognitiveservices-search-visualsearch": "0.2.0", "python-debian": "0.1.43+ubuntu1.1", "azure-mgmt-documentdb": "0.1.3", "azure-synapse": "0.1.1", "azure-cognitiveservices-formrecognizer": "0.1.1", "language-selector": "0.1", "azure-cognitiveservices-personalizer": "0.1.0", "azure-applicationinsights": "0.1.0", "azure-mgmt-edgegateway": "0.1.0", "azure-loganalytics": "0.1.0", "unattended-upgrades": "0.1", "azure-template": "0.0.18b3", "connection-pool": "0.0.3", "screen-resolution-extra": "0.0.0", "cmdtest": "0.32+git", "xkit": "0.0.0", "ubuntu-drivers-common": "0.0.0"}}