{"anli_r1_full": {"after": {"dataset": "anli_r1_full", "variant": "after", "performance": {"accuracy": 0.314, "balanced_accuracy": 0.31413449377521235, "mcc": -0.03504851692873528}, "best_model": "NeuralNetTorch", "best_val_score": 0.327, "test_score": 0.335, "accuracy": 0.314, "num_models_trained": 14, "total_models_attempted": 14, "leaderboard_top3": [{"model": "NeuralNetTorch", "score_val": 0.327}, {"model": "LightGBMLarge", "score_val": 0.335}, {"model": "LightGBMXT", "score_val": 0.333}], "model_training_times": {"NeuralNetTorch": 26.97084379196167, "LightGBMLarge": 61.108384132385254, "LightGBMXT": 21.405623197555542, "CatBoost": 18.34986972808838, "LightGBM": 17.429491758346558, "ExtraTreesEntr": 7.289936542510986, "RandomForestGini": 53.733728885650635, "RandomForestEntr": 75.77980041503906, "XGBoost": 33.90637946128845, "ExtraTreesGini": 6.776408672332764, "WeightedEnsemble_L2": 52.61792039871216, "KNeighborsDist": 0.6010358333587646, "KNeighborsUnif": 0.5621156692504883, "NeuralNetFastAI": 40.99461388587952}, "problem_type": "multiclass", "eval_metric": "accuracy", "predictions_saved": true, "confusion_matrix_saved": true, "model_size_mb": 851.9, "num_parameters": "N/A", "model_type": "TabularPredictor", "total_training_time": 374.24536418914795, "train_size": 16946, "val_size": 1000, "test_size": 1000, "feature_count": 384}}}