[{"dataset": "anli_r1_noisy", "preprocessing_method": "tpot", "num_features_after_preprocessing": 1, "timing": {"tpot_fit_time": 0.1, "transform_time": 0.00845193862915039, "data_save_time": 0.01729559898376465, "preprocessing_total_time": 0.15578866004943848, "autogluon_training_time": 5.011998891830444, "total_pipeline_time": 5.168153285980225, "formatted_total_time": "5.17 seconds"}, "tpot_pipeline": "Pipeline(steps=[('randomforestclassifier',\n                 RandomForestClassifier(bootstrap=False, criterion='entropy',\n                                        max_depth=5, min_samples_split=5,\n                                        n_estimators=200, random_state=42))])", "accuracy": "N/A", "performance": "N/A", "best_model": "N/A", "leaderboard_top3": [], "experiment_timing": {"dataset_processing_time": 5.265495777130127, "overall_experiment_time": 32.96695637702942}}, {"dataset": "casehold_imbalanced", "preprocessing_method": "tpot", "num_features_after_preprocessing": 1, "timing": {"tpot_fit_time": 0.1, "transform_time": 21.511850357055664, "data_save_time": 0.04102730751037598, "preprocessing_total_time": 21.566846132278442, "autogluon_training_time": 5.060076951980591, "total_pipeline_time": 26.627211570739746, "formatted_total_time": "26.63 seconds"}, "tpot_pipeline": "Pipeline(steps=[('logisticregression',\n                 LogisticRegression(C=0.01, max_iter=1000, random_state=42,\n                                    solver='liblinear'))])", "accuracy": "N/A", "performance": "N/A", "best_model": "N/A", "leaderboard_top3": [], "experiment_timing": {"dataset_processing_time": 27.70106863975525, "overall_experiment_time": 32.96695637702942}}]